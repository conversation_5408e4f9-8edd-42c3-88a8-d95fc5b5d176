import { <PERSON><PERSON><PERSON>, FiUser, FiBookO<PERSON> } from "react-icons/fi";
import { useThemeProvider } from "../../../providers/ThemeContext";

function ExamHeader({ exam, remainingTime, strikes, connectionStatus }) {
  const { themeBg, themeText, cardBg } = useThemeProvider();

  const formatTime = (totalSeconds) => {
    if (totalSeconds <= 0) return "00:00";
    
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    if (remainingTime <= 300) return "text-red-500"; // Last 5 minutes
    if (remainingTime <= 900) return "text-yellow-500"; // Last 15 minutes
    return "text-green-500";
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'reconnecting': return 'bg-yellow-500';
      case 'disconnected': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={`${cardBg} shadow-lg border-b sticky top-0 z-10`}>
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Exam Info */}
          <div className="flex items-center space-x-4">
            <FiBookOpen className="w-6 h-6 text-violet-600" />
            <div>
              <h1 className={`text-xl font-bold ${themeText}`}>
                {exam?.title || 'Loading...'}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {exam?.total_questions} Questions • {exam?.total_marks} Marks
              </p>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex items-center space-x-6">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${getConnectionStatusColor()}`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                {connectionStatus}
              </span>
            </div>

            {/* Strikes */}
            {strikes > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-red-500">
                  Violations: {strikes}/3
                </span>
              </div>
            )}

            {/* Timer */}
            <div className="flex items-center space-x-2">
              <FiClock className={`w-5 h-5 ${getTimeColor()}`} />
              <span className={`text-lg font-mono font-bold ${getTimeColor()}`}>
                {formatTime(remainingTime)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ExamHeader;
