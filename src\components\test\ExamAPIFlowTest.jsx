/**
 * Exam API Flow Test Component
 * Tests the correct API flow for exam taking
 */

import React, { useState } from 'react';
import { <PERSON>P<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, FiArrowRight } from 'react-icons/fi';

const ExamAPIFlowTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Test exam ID (you can change this to a real exam ID)
  const TEST_EXAM_ID = "dc5aede9-e966-4d28-96ea-568718994656";

  const addTestResult = (step, testName, passed, message, data = null) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      step,
      testName,
      passed,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runAPIFlowTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentStep(0);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addTestResult(0, "Authentication Check", false, "No authentication token found in localStorage");
        setIsRunning(false);
        return;
      }

      addTestResult(0, "Authentication Check", true, "Authentication token found");

      // Step 1: Request Session ID
      setCurrentStep(1);
      addTestResult(1, "Step 1: Request Session ID", null, "Calling POST /exam-session/request/{exam_id}...");
      
      try {
        const sessionResponse = await fetch(`http://127.0.0.1:8000/exam-session/request/${TEST_EXAM_ID}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!sessionResponse.ok) {
          const error = await sessionResponse.json();
          addTestResult(1, "Step 1: Request Session ID", false, `Failed: ${error.detail || error.message || 'Unknown error'}`, error);
        } else {
          const sessionData = await sessionResponse.json();
          addTestResult(1, "Step 1: Request Session ID", true, `Success: Got session_id ${sessionData.session_id}`, sessionData);

          // Step 2: WebSocket Connection (simulated)
          setCurrentStep(2);
          addTestResult(2, "Step 2: WebSocket Connection", true, "WebSocket connection would be established here", {
            note: "WebSocket connection is handled by ExamWebSocketService"
          });

          // Step 3: Get Exam Data using Session ID
          setCurrentStep(3);
          addTestResult(3, "Step 3: Get Exam Data", null, `Calling GET /attempt/${sessionData.session_id}...`);

          try {
            const examResponse = await fetch(`http://127.0.0.1:8000/attempt/${sessionData.session_id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (!examResponse.ok) {
              const error = await examResponse.json();
              addTestResult(3, "Step 3: Get Exam Data", false, `Failed: ${error.detail || error.message || 'Unknown error'}`, error);
            } else {
              const examData = await examResponse.json();
              addTestResult(3, "Step 3: Get Exam Data", true, `Success: Got exam data with ${examData.questions?.length || 0} questions`, {
                examId: examData.id,
                title: examData.title,
                questionCount: examData.questions?.length || 0,
                duration: examData.total_duration,
                remainingTime: examData.remaining_time_seconds
              });

              // Step 4: Validation
              setCurrentStep(4);
              const hasRequiredFields = examData.id && examData.questions && examData.session_id;
              addTestResult(4, "Step 4: Data Validation", hasRequiredFields, 
                hasRequiredFields ? "Exam data has all required fields" : "Exam data missing required fields",
                {
                  hasId: !!examData.id,
                  hasQuestions: !!examData.questions,
                  hasSessionId: !!examData.session_id,
                  hasRemainingTime: typeof examData.remaining_time_seconds === 'number'
                }
              );
            }
          } catch (error) {
            addTestResult(3, "Step 3: Get Exam Data", false, `Network error: ${error.message}`, error);
          }
        }
      } catch (error) {
        addTestResult(1, "Step 1: Request Session ID", false, `Network error: ${error.message}`, error);
      }

    } catch (error) {
      addTestResult(0, "Test Suite Error", false, `Unexpected error: ${error.message}`, error);
    } finally {
      setIsRunning(false);
      setCurrentStep(0);
    }
  };

  const steps = [
    { id: 1, name: "Request Session ID", endpoint: "POST /exam-session/request/{exam_id}" },
    { id: 2, name: "WebSocket Connection", endpoint: "WebSocket connection" },
    { id: 3, name: "Get Exam Data", endpoint: "GET /attempt/{session_id}" },
    { id: 4, name: "Data Validation", endpoint: "Validate response structure" }
  ];

  const passedTests = testResults.filter(result => result.passed === true).length;
  const failedTests = testResults.filter(result => result.passed === false).length;
  const totalTests = testResults.filter(result => result.passed !== null).length;

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Exam API Flow Test</h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4">Correct API Flow:</h2>
          <div className="flex flex-wrap items-center space-x-2 mb-4">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                  currentStep === step.id ? 'bg-blue-100 border-blue-300' :
                  currentStep > step.id ? 'bg-green-100 border-green-300' :
                  'bg-gray-100 border-gray-300'
                }`}>
                  <span className="font-medium">{step.id}</span>
                  <div>
                    <div className="font-medium text-sm">{step.name}</div>
                    <div className="text-xs text-gray-600">{step.endpoint}</div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <FiArrowRight className="w-4 h-4 text-gray-400" />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <button
            onClick={runAPIFlowTest}
            disabled={isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <FiPlay className="w-4 h-4" />
            <span>{isRunning ? 'Testing API Flow...' : 'Test API Flow'}</span>
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="mb-6">
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                <div className="text-sm text-green-800">Passed</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                <div className="text-sm text-red-800">Failed</div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-600">{totalTests}</div>
                <div className="text-sm text-blue-800">Total</div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-3">
          {testResults.map((result) => (
            <div
              key={result.id}
              className={`p-4 rounded-lg border ${
                result.passed === true ? 'bg-green-50 border-green-200' :
                result.passed === false ? 'bg-red-50 border-red-200' :
                'bg-blue-50 border-blue-200'
              }`}
            >
              <div className="flex items-start space-x-3">
                {result.passed === true ? (
                  <FiCheck className="w-5 h-5 text-green-600 mt-0.5" />
                ) : result.passed === false ? (
                  <FiX className="w-5 h-5 text-red-600 mt-0.5" />
                ) : (
                  <div className="w-5 h-5 mt-0.5 rounded-full bg-blue-400 animate-pulse" />
                )}
                <div className="flex-1">
                  <h3 className={`font-medium ${
                    result.passed === true ? 'text-green-800' :
                    result.passed === false ? 'text-red-800' :
                    'text-blue-800'
                  }`}>
                    Step {result.step}: {result.testName}
                  </h3>
                  <p className={`text-sm ${
                    result.passed === true ? 'text-green-600' :
                    result.passed === false ? 'text-red-600' :
                    'text-blue-600'
                  }`}>
                    {result.message}
                  </p>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-500 cursor-pointer">View Details</summary>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    {result.timestamp}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {testResults.length === 0 && !isRunning && (
          <div className="text-center py-8 text-gray-500">
            Click "Test API Flow" to validate the correct exam API sequence
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamAPIFlowTest;
