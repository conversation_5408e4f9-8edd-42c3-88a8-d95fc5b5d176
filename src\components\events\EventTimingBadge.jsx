import React from 'react';
import { Fi<PERSON>lock, FiPlayCircle, FiCheckCircle } from 'react-icons/fi';
import { getEventTimingStatus, getEventTimeRemaining } from '../../utils/eventUtils';

const EventTimingBadge = ({ 
  event, 
  showTimeRemaining = true, 
  size = 'default', // 'small', 'default', 'large'
  variant = 'badge' // 'badge', 'inline', 'detailed'
}) => {
  const timingStatus = getEventTimingStatus(event);
  const timeRemaining = showTimeRemaining ? getEventTimeRemaining(event) : null;

  const getIcon = (iconName) => {
    const iconMap = {
      'clock': FiClock,
      'play-circle': FiPlayCircle,
      'check-circle': FiCheckCircle
    };
    return iconMap[iconName] || FiClock;
  };

  const getSizeClasses = () => {
    const sizeMap = {
      small: {
        badge: 'px-2 py-0.5 text-xs',
        icon: 'h-3 w-3',
        text: 'text-xs'
      },
      default: {
        badge: 'px-2.5 py-1 text-sm',
        icon: 'h-4 w-4',
        text: 'text-sm'
      },
      large: {
        badge: 'px-3 py-1.5 text-base',
        icon: 'h-5 w-5',
        text: 'text-base'
      }
    };
    return sizeMap[size] || sizeMap.default;
  };

  const Icon = getIcon(timingStatus.icon);
  const sizeClasses = getSizeClasses();

  if (variant === 'inline') {
    return (
      <div className="flex items-center space-x-1">
        <Icon className={`${sizeClasses.icon} ${
          timingStatus.status === 'ongoing' ? 'text-green-600' :
          timingStatus.status === 'ended' ? 'text-gray-500' :
          'text-blue-600'
        }`} />
        <span className={`font-medium ${
          timingStatus.status === 'ongoing' ? 'text-green-600' :
          timingStatus.status === 'ended' ? 'text-gray-500' :
          'text-blue-600'
        } ${sizeClasses.text}`}>
          {timingStatus.label}
        </span>
        {timeRemaining && (
          <span className={`text-gray-500 ${sizeClasses.text}`}>
            • {timeRemaining}
          </span>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className="space-y-1">
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center ${sizeClasses.badge} rounded-full font-medium ${timingStatus.color}`}>
            <Icon className={`${sizeClasses.icon} mr-1`} />
            {timingStatus.label}
          </span>
        </div>
        {timeRemaining && (
          <div className={`text-gray-600 ${sizeClasses.text}`}>
            {timeRemaining}
          </div>
        )}
      </div>
    );
  }

  // Default badge variant
  return (
    <span className={`inline-flex items-center ${sizeClasses.badge} rounded-full font-medium ${timingStatus.color}`}>
      <Icon className={`${sizeClasses.icon} mr-1`} />
      {timingStatus.label}
      {timeRemaining && size !== 'small' && (
        <span className="ml-1 opacity-75">
          • {timeRemaining}
        </span>
      )}
    </span>
  );
};

export default EventTimingBadge;
