/**
 * Exam Data Test Page
 * Test page to simulate having exam data and test the DirectExamInterface
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import DirectExamInterface from '../../components/exam/student/DirectExamInterface';

const ExamDataTest = () => {
  const navigate = useNavigate();

  // Mock exam data based on what you provided
  const mockExamData = {
    "id": "dc5aede9-e966-4d28-96ea-568718994656",
    "title": "Exam",
    "description": "Exam",
    "total_marks": 1,
    "total_duration": 999,
    "questions": [
        {
            "id": "fbd7bcad-bda5-43ce-80d9-145d8fcd317c",
            "text": "1+1",
            "Type": "MCQS",
            "Level": "EASY",
            "imageUrl": null,
            "class_id": "7fcc64ec-7539-4e3c-a703-0d2e9ee36f76",
            "subject_id": "eca9295b-091f-407f-8802-56df0722c369",
            "chapter_id": "db174ed9-226c-4921-a8f3-68654942533a",
            "topic_id": null,
            "subtopic_id": null,
            "marks": 1,
            "class_": {
                "id": "7fcc64ec-7539-4e3c-a703-0d2e9ee36f76",
                "ClassNo": "9"
            },
            "subject": {
                "id": "eca9295b-091f-407f-8802-56df0722c369",
                "name": "Mathematics"
            },
            "chapter": {
                "id": "db174ed9-226c-4921-a8f3-68654942533a",
                "name": "Algebra"
            },
            "topic": null,
            "subtopic": null,
            "options": [
                {
                    "id": "4ef66cc4-d574-40df-af09-994364c944d9",
                    "option_text": "2"
                },
                {
                    "id": "f0dd71e1-b807-4fbc-8208-3323fed98f03",
                    "option_text": "3"
                },
                {
                    "id": "d02b1fc5-494c-4b31-84b5-63897b78f8f0",
                    "option_text": "11"
                },
                {
                    "id": "a00535a0-7e21-40ca-84bd-2b22cea186df",
                    "option_text": "12"
                }
            ],
            "teacher_profile": null,
            "teacher_id": "7898d10d-f017-45c7-a2a2-bf5a35aedf5a"
        }
    ],
    "start_time": "2025-09-17T09:00:00",
    "end_time": "2025-09-18T01:39:00",
    "session_id": "2eb4ddf2-245a-43e2-8818-c51707ef2198",
    "remaining_time_seconds": 22960
  };

  return (
    <div>
      <DirectExamInterface
        examData={mockExamData}
        onBackToExams={() => navigate('/student/exams')}
      />
    </div>
  );
};

export default ExamDataTest;
