import { Fi<PERSON>rrow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiUser } from "react-icons/fi";
import { useThemeProvider } from "../../../providers/ThemeContext";

function ExamStartScreen({
  exam,
  onStartExam,
  onBackToExams,
  isStarting
}) {
  const { themeBg, themeText, cardBg } = useThemeProvider();

  return (
    <div className={`min-h-screen ${themeBg} ${themeText}`}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className={`${cardBg} rounded-xl shadow-lg overflow-hidden`}>
          {/* Header */}
          <div className="bg-gradient-to-r from-violet-600 to-purple-600 text-white p-6">
            <div className="flex items-center space-x-3 mb-4">
              <FiBookOpen className="w-8 h-8" />
              <h1 className="text-2xl font-bold">{exam?.title || 'Exam'}</h1>
            </div>
            {exam?.description && (
              <p className="text-violet-100">{exam.description}</p>
            )}
          </div>

          {/* Exam Details */}
          <div className="p-6">
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="space-y-4">
                <h3 className={`text-lg font-semibold ${themeText}`}>Exam Information</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <FiBookOpen className="w-5 h-5 text-violet-600" />
                    <span>{exam?.total_questions || '...'} Questions</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiUser className="w-5 h-5 text-violet-600" />
                    <span>{exam?.total_marks || '...'} Total Marks</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiClock className="w-5 h-5 text-violet-600" />
                    <span>{exam?.total_duration || '...'} Minutes</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className={`text-lg font-semibold ${themeText}`}>Instructions</h3>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Read each question carefully before answering</li>
                  <li>• You can navigate between questions freely</li>
                  <li>• Your answers are automatically saved</li>
                  <li>• Submit your exam before time runs out</li>
                  <li>• Do not refresh or close the browser</li>
                  <li>• Stay in fullscreen mode during the exam</li>
                </ul>
              </div>
            </div>

            {/* Warning */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 text-yellow-600 mt-0.5">⚠️</div>
                <div>
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-200">Important</h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    Once you start the exam, the timer will begin and cannot be paused. 
                    Make sure you have a stable internet connection and enough time to complete the exam.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <button
                onClick={onBackToExams}
                disabled={isStarting}
                className="flex items-center space-x-2 px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span>Back to Exams</span>
              </button>

              <button
                onClick={onStartExam}
                disabled={isStarting}
                className="flex items-center space-x-2 px-8 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors font-semibold disabled:opacity-50"
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Starting Exam...</span>
                  </>
                ) : (
                  <span>Start Exam</span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ExamStartScreen;
