import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiTrash2, <PERSON><PERSON><PERSON>, <PERSON>Loader } from 'react-icons/fi';
import { formatDistanceToNow } from 'date-fns';
import Transition from './Transition';
import notificationService from '../../services/notificationService';
import { useNotification } from '../../contexts/NotificationContext';
import logger from '../../utils/helpers/logger';

function DropdownNotifications({
  align
}) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const trigger = useRef(null);
  const dropdown = useRef(null);
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  // Fetch notifications when dropdown opens
  useEffect(() => {
    if (dropdownOpen && notifications.length === 0) {
      fetchNotifications();
    }
  }, [dropdownOpen]);

  // Fetch unread count on component mount
  useEffect(() => {
    fetchUnreadCount();
  }, []);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (!dropdownOpen || dropdown.current.contains(target) || trigger.current.contains(target)) return;
      setDropdownOpen(false);
    };
    document.addEventListener('click', clickHandler);
    return () => document.removeEventListener('click', clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener('keydown', keyHandler);
    return () => document.removeEventListener('keydown', keyHandler);
  });

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await notificationService.getNotifications({
        page: 1,
        page_size: 5, // Show only recent 5 notifications in dropdown
        unread_only: false
      });

      setNotifications(response.notifications || []);
      setUnreadCount(response.unread_count || 0);
    } catch (err) {
      logger.error('Failed to fetch notifications', { error: err.message }, 'DropdownNotifications');
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const stats = await notificationService.getNotificationStats();
      setUnreadCount(stats.unread_notifications || 0);
    } catch (err) {
      logger.error('Failed to fetch unread count', { error: err.message }, 'DropdownNotifications');
    }
  };

  const handleMarkAsRead = async (notificationId, event) => {
    event.preventDefault();
    event.stopPropagation();

    try {
      await notificationService.markAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, is_read: true, read_at: new Date().toISOString() }
            : notif
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
      showSuccess('Notification marked as read');
    } catch (err) {
      logger.error('Failed to mark notification as read', { error: err.message, notificationId }, 'DropdownNotifications');
      showError('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();

      // Update local state
      setNotifications(prev =>
        prev.map(notif => ({
          ...notif,
          is_read: true,
          read_at: new Date().toISOString()
        }))
      );

      setUnreadCount(0);
      showSuccess('All notifications marked as read');
    } catch (err) {
      logger.error('Failed to mark all notifications as read', { error: err.message }, 'DropdownNotifications');
      showError('Failed to mark all notifications as read');
    }
  };

  const handleViewAll = () => {
    setDropdownOpen(false);
    navigate('/notifications');
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'follow': return '👥';
      case 'exam': return '📝';
      case 'event': return '📅';
      case 'task': return '✅';
      case 'message': return '💬';
      default: return '📣';
    }
  };

  const formatNotificationTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Recently';
    }
  };

  return (
    <div className="relative inline-flex">
      <button
        ref={trigger}
        className={`w-8 h-8 flex items-center justify-center hover:bg-gray-100 lg:hover:bg-gray-200 dark:hover:bg-gray-700/50 dark:lg:hover:bg-gray-800 rounded-full ${dropdownOpen && 'bg-gray-200 dark:bg-gray-800'}`}
        aria-haspopup="true"
        onClick={() => setDropdownOpen(!dropdownOpen)}
        aria-expanded={dropdownOpen}
      >
        <span className="sr-only">Notifications</span>
        <FiBell className="w-4 h-4 text-gray-500/80 dark:text-gray-400/80" />
        {unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center border-2 border-white dark:border-gray-900">
            {unreadCount > 99 ? '99+' : unreadCount}
          </div>
        )}
      </button>

      <Transition
        className={`origin-top-right z-10 absolute top-full -mr-48 sm:mr-0 min-w-80 max-w-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700/60 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1 ${align === 'right' ? 'right-0' : 'left-0'}`}
        show={dropdownOpen}
        enter="transition ease-out duration-200 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-200"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      >
        <div
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={() => setDropdownOpen(false)}
        >
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 dark:border-gray-700/60">
            <div className="text-xs font-semibold text-gray-400 dark:text-gray-500 uppercase">
              Notifications {unreadCount > 0 && `(${unreadCount} unread)`}
            </div>
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="text-xs text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 font-medium"
              >
                Mark all read
              </button>
            )}
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <FiLoader className="w-5 h-5 animate-spin text-gray-400" />
                <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">Loading...</span>
              </div>
            ) : error ? (
              <div className="px-4 py-6 text-center">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                <button
                  onClick={fetchNotifications}
                  className="mt-2 text-xs text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300"
                >
                  Try again
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="px-4 py-6 text-center">
                <FiBell className="w-8 h-8 text-gray-300 dark:text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">No notifications yet</p>
              </div>
            ) : (
              <ul>
                {notifications.map((notification) => (
                  <li key={notification.id} className="border-b border-gray-200 dark:border-gray-700/60 last:border-0">
                    <div className={`block py-3 px-4 hover:bg-gray-50 dark:hover:bg-gray-700/20 ${!notification.is_read ? 'bg-violet-50 dark:bg-violet-900/10' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-1">
                            <span className="text-sm mr-2">{getNotificationIcon(notification.notification_type)}</span>
                            <span className={`text-sm font-medium ${!notification.is_read ? 'text-gray-900 dark:text-gray-100' : 'text-gray-700 dark:text-gray-300'}`}>
                              {notification.title}
                            </span>
                            {!notification.is_read && (
                              <div className="w-2 h-2 bg-violet-500 rounded-full ml-2 flex-shrink-0"></div>
                            )}
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-1">
                            {notification.message}
                          </p>
                          <span className="text-xs text-gray-400 dark:text-gray-500">
                            {formatNotificationTime(notification.created_at)}
                          </span>
                        </div>
                        <div className="flex items-center ml-2 space-x-1">
                          {!notification.is_read && (
                            <button
                              onClick={(e) => handleMarkAsRead(notification.id, e)}
                              className="p-1 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 rounded"
                              title="Mark as read"
                            >
                              <FiCheck className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="border-t border-gray-200 dark:border-gray-700/60 px-4 py-2">
              <button
                onClick={handleViewAll}
                className="w-full text-center text-sm text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 font-medium py-1"
              >
                View all notifications
              </button>
            </div>
          )}
        </div>
      </Transition>
    </div>
  )
}

export default DropdownNotifications;