/**
 * Reconnection Quick Actions
 * Quick action buttons for teachers to manage reconnection requests
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../../../providers/ThemeContext';
import { useNotification } from '../../../../contexts/NotificationContext';
import {
  getPendingReconnectionRequests,
  approveReconnectionRequest,
  selectPendingRequests,
  selectReconnectionState
} from '../../../../store/slices/exam/examReconnectionSlice';
import {
  FiWifi,
  FiClock,
  FiCheck,
  FiX,
  FiEye,
  FiBell,
  FiUser
} from 'react-icons/fi';

const ReconnectionQuickActions = ({ 
  examId = null, 
  onViewAll,
  className = '' 
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { showSuccess, showError } = useNotification();
  
  const pendingRequests = useSelector(selectPendingRequests);
  const reconnectionState = useSelector(selectReconnectionState);
  
  const [showDropdown, setShowDropdown] = useState(false);
  const [filteredRequests, setFilteredRequests] = useState([]);

  // Theme-based styling
  const isDark = currentTheme === 'dark';

  // Load pending requests on mount
  useEffect(() => {
    dispatch(getPendingReconnectionRequests());
  }, [dispatch]);

  // Filter requests by exam if examId provided
  useEffect(() => {
    if (examId) {
      setFilteredRequests(pendingRequests.filter(req => req.exam_id === examId));
    } else {
      setFilteredRequests(pendingRequests);
    }
  }, [pendingRequests, examId]);

  // Auto-refresh every 15 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      dispatch(getPendingReconnectionRequests());
    }, 15000);

    return () => clearInterval(interval);
  }, [dispatch]);

  // Handle quick approve
  const handleQuickApprove = async (requestId, e) => {
    e.stopPropagation();
    
    try {
      await dispatch(approveReconnectionRequest({
        requestId,
        approved: true,
        reason: 'Quick approval'
      })).unwrap();

      showSuccess('Reconnection request approved');
      dispatch(getPendingReconnectionRequests());
    } catch (error) {
      showError(error.message || 'Failed to approve request');
    }
  };

  // Handle quick deny
  const handleQuickDeny = async (requestId, e) => {
    e.stopPropagation();
    
    try {
      await dispatch(approveReconnectionRequest({
        requestId,
        approved: false,
        reason: 'Quick denial'
      })).unwrap();

      showSuccess('Reconnection request denied');
      dispatch(getPendingReconnectionRequests());
    } catch (error) {
      showError(error.message || 'Failed to deny request');
    }
  };

  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const requestTime = new Date(timestamp);
    const diffMs = now - requestTime;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    return `${diffHours}h ago`;
  };

  const requestCount = filteredRequests.length;

  return (
    <div className={`relative ${className}`}>
      {/* Quick Action Button */}
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className={`relative px-3 py-2 rounded-lg border transition-colors ${
          isDark 
            ? 'bg-gray-800 border-gray-700 text-white hover:bg-gray-700' 
            : 'bg-white border-gray-300 text-gray-900 hover:bg-gray-50'
        } flex items-center gap-2`}
      >
        <FiWifi className="w-4 h-4" />
        <span className="text-sm font-medium">Reconnections</span>
        
        {/* Badge */}
        {requestCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {requestCount > 9 ? '9+' : requestCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowDropdown(false)}
          />
          
          {/* Dropdown Content */}
          <div className={`absolute right-0 top-full mt-2 w-80 rounded-lg shadow-xl border z-20 ${
            isDark 
              ? 'bg-gray-800 border-gray-700' 
              : 'bg-white border-gray-200'
          }`}>
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className={`font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Pending Requests
                </h3>
                {onViewAll && (
                  <button
                    onClick={() => {
                      onViewAll();
                      setShowDropdown(false);
                    }}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    View All
                  </button>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="max-h-96 overflow-y-auto">
              {reconnectionState.loading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading...</p>
                </div>
              ) : filteredRequests.length === 0 ? (
                <div className="p-4 text-center">
                  <FiCheck className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No pending requests</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredRequests.slice(0, 5).map((request) => (
                    <div key={request.request_id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <FiUser className="w-3 h-3 text-gray-500" />
                          <span className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                            {request.student_id}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <FiClock className="w-3 h-3" />
                          {formatTimeAgo(request.requested_at)}
                        </div>
                      </div>
                      
                      <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                        {request.reason}
                      </p>
                      
                      <div className="flex gap-2">
                        <button
                          onClick={(e) => handleQuickApprove(request.request_id, e)}
                          disabled={reconnectionState.loading}
                          className="flex-1 px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-1"
                        >
                          <FiCheck className="w-3 h-3" />
                          Approve
                        </button>
                        <button
                          onClick={(e) => handleQuickDeny(request.request_id, e)}
                          disabled={reconnectionState.loading}
                          className="flex-1 px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 disabled:opacity-50 flex items-center justify-center gap-1"
                        >
                          <FiX className="w-3 h-3" />
                          Deny
                        </button>
                      </div>
                    </div>
                  ))}
                  
                  {filteredRequests.length > 5 && (
                    <div className="p-3 text-center border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => {
                          if (onViewAll) onViewAll();
                          setShowDropdown(false);
                        }}
                        className="text-blue-600 hover:text-blue-700 text-sm"
                      >
                        View {filteredRequests.length - 5} more requests
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ReconnectionQuickActions;
