/**
 * Simple Mentor Multi-Select Component
 */

import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchMentorsList } from '../../store/slices/MentorsSlice';
import { LoadingSpinner } from '../ui';
import { FiSearch, FiX, FiChevronDown, FiUser, FiClock, FiDollarSign } from 'react-icons/fi';

const MentorSelector = ({
  selectedMentors = [],
  onMentorsChange,
  className = "",
}) => {
  console.log('🔍 MentorSelector props:', {
    selectedMentors,
    onMentorsChange: typeof onMentorsChange,
    selectedMentorsLength: selectedMentors?.length
  });

  const dispatch = useDispatch();
  const {
    publicMentors: mentors,
    publicMentorsLoading: loading,
    publicMentorsError: error,
  } = useSelector((state) => state.mentors);

  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const dropdownRef = useRef(null);

  useEffect(() => {
    dispatch(fetchMentorsList({ page: currentPage, size: pageSize }));
  }, [dispatch, currentPage, pageSize]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMentorToggle = (mentorId) => {
    console.log('🔄 handleMentorToggle called:', {
      mentorId,
      currentSelected: selectedMentors,
      onMentorsChange: typeof onMentorsChange
    });

    const isSelected = selectedMentors.includes(mentorId);
    const updated = isSelected
      ? selectedMentors.filter((id) => id !== mentorId)
      : [...selectedMentors, mentorId];

    console.log('🔄 Updated selection:', updated);

    if (onMentorsChange) {
      onMentorsChange(updated);
    } else {
      console.error('❌ onMentorsChange is not a function!');
    }
  };

  const handleRemoveMentor = (mentorId) => {
    const updated = selectedMentors.filter((id) => id !== mentorId);
    if (onMentorsChange) {
      onMentorsChange(updated);
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const mentorsToUse = mentors || [];
  console.log('🔍 Mentors data:', { mentors, mentorsToUse, loading, error });

  const filteredMentors = (mentorsToUse || []).filter((mentor) => {
    const matchesSearch =
      !searchTerm ||
      mentor.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.expertise_areas?.some((area) =>
        typeof area === "string" &&
        area.toLowerCase().includes(searchTerm.toLowerCase())
      );

    return matchesSearch;
  });

  // Get selected mentor objects for display
  const selectedMentorObjects = selectedMentors
    .map(id => mentorsToUse.find(mentor => mentor.id === id))
    .filter(Boolean);

  // Pagination
  const totalMentors = filteredMentors.length;
  const totalPages = Math.ceil(totalMentors / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedMentors = filteredMentors.slice(startIndex, startIndex + pageSize);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Competition Mentors</h3>
          <p className="text-sm text-gray-500">
            {selectedMentors.length} mentor{selectedMentors.length !== 1 ? 's' : ''} selected
            {loading && ' (Loading...)'}
            {error && ' (Error loading)'}
          </p>
        </div>
        <button
          onClick={() => dispatch(fetchMentorsList({ page: currentPage, size: pageSize }))}
          className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <FiRefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Multi-Select Dropdown */}
      <div className="relative" ref={dropdownRef}>
        {/* Selected Mentors Display */}
        <div
          onClick={toggleDropdown}
          className="min-h-[42px] w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer hover:border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-1 flex-1">
              {selectedMentorObjects.length === 0 ? (
                <span className="text-gray-500">Select mentors...</span>
              ) : (
                selectedMentorObjects.map((mentor) => (
                  <span
                    key={mentor.id}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {mentor.full_name}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveMentor(mentor.id);
                      }}
                      className="ml-1 hover:text-blue-600"
                    >
                      <FiX className="w-3 h-3" />
                    </button>
                  </span>
                ))
              )}
            </div>
            <FiChevronDown
              className={`w-4 h-4 text-gray-400 transition-transform ${
                isDropdownOpen ? 'transform rotate-180' : ''
              }`}
            />
          </div>
        </div>

        {/* Dropdown Content */}
        {isDropdownOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* Search */}
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search mentors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Loading */}
            {loading && (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="md" />
              </div>
            )}

            {/* Error */}
            {error && (
              <div className="p-3">
                <ErrorMessage message={error} />
              </div>
            )}

            {/* Test data notice */}
            {!loading && (!mentors || mentors.length === 0) && (
              <div className="bg-yellow-50 border-b border-yellow-200 p-3">
                <p className="text-xs text-yellow-800">
                  ⚠️ <strong>Debug Mode:</strong> Showing test mentors
                </p>
              </div>
            )}

            {/* Mentors List */}
            {!loading && (
              <div className="max-h-60 overflow-y-auto">
                {paginatedMentors.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <FiUser className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-sm">No mentors found</p>
                  </div>
                ) : (
                  paginatedMentors.map((mentor) => {
                    const isSelected = selectedMentors.includes(mentor.id);

                    return (
                      <div
                        key={mentor.id}
                        onClick={() => handleMentorToggle(mentor.id)}
                        className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                          isSelected ? 'bg-blue-50' : ''
                        }`}
                      >
                        <div className="flex items-center flex-1">
                          {mentor.profile_image_url ? (
                            <img
                              src={mentor.profile_image_url}
                              alt={mentor.full_name}
                              className="w-8 h-8 rounded-full object-cover mr-3"
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                              <span className="text-white text-xs font-bold">
                                {mentor.full_name?.charAt(0) || "M"}
                              </span>
                            </div>
                          )}
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {mentor.full_name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {mentor.experience_years} yrs exp · R{mentor.hourly_rate}/h
                            </p>
                            {mentor.expertise_areas?.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {mentor.expertise_areas.slice(0, 2).map((area) => (
                                  <span
                                    key={area}
                                    className="px-1 py-0.5 rounded text-xs bg-green-100 text-green-800"
                                  >
                                    {typeof area === "object" ? area.name : area}
                                  </span>
                                ))}
                                {mentor.expertise_areas.length > 2 && (
                                  <span className="px-1 py-0.5 rounded text-xs bg-gray-100 text-gray-800">
                                    +{mentor.expertise_areas.length - 2}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        {isSelected && (
                          <FiCheck className="w-4 h-4 text-blue-600 ml-2" />
                        )}
                      </div>
                    );
                  })
                )}
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && (
              <div className="p-3 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {startIndex + 1}-{Math.min(startIndex + pageSize, totalMentors)} of {totalMentors}
                  </span>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-2 py-1 text-xs border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                    >
                      Previous
                    </button>
                    <span className="text-xs text-gray-600">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-2 py-1 text-xs border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selection Summary */}
      {selectedMentors.length > 0 && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-800">
            <strong>{selectedMentors.length}</strong> mentor{selectedMentors.length !== 1 ? 's' : ''} selected for this competition
          </p>
        </div>
      )}
    </div>
  );
};

export default MentorSelector;