/**
 * UserEventDashboard Component
 *
 * Dashboard for users to view their registered events and tickets
 * Enhanced with better styling and logical organization for exam/competition events
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiMapPin,
  FiClock,
  FiDownload,
  FiEye,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiFilter,
  FiSearch,
  FiDollarSign,
  FiAward,
  FiBookOpen,
  FiUsers,
  FiTrendingUp
} from 'react-icons/fi';
import { format } from 'date-fns';
import { EventBookingService } from '../../services/eventBookingService';
import newEventService from '../../services/newEventService';
import competitionExamService from '../../services/competitionExamService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import { useNotification } from '../../contexts/NotificationContext';
import { BASE_API } from '../../utils/api/API_URL';
import { downloadTicketPDF } from '../../utils/ticketPdfGenerator';
import EventTimingBadge from './EventTimingBadge';
import { getEventTimingStatus } from '../../utils/eventUtils';

const UserEventDashboard = ({ embedded = false }) => {
  const navigate = useNavigate();
  const { showSuccess, showError, showInfo } = useNotification();
  
  // Local state
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState(null);
  const [registrations, setRegistrations] = useState([]);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    search: '',
    upcoming: false
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load dashboard summary
      const dashboardData = await EventBookingService.getUserEventDashboard();

      // Ensure dashboard data is a valid object
      if (dashboardData && typeof dashboardData === 'object' && !Array.isArray(dashboardData)) {
        setDashboard(dashboardData);
      } else {
        console.warn('Invalid dashboard data received:', dashboardData);
        setDashboard({
          total_registrations: 0,
          confirmed_registrations: 0,
          upcoming_events: 0,
          total_tickets: 0
        });
      }

      // Load user registrations
      const registrationsData = await EventBookingService.getUserRegistrations({
        skip: 0,
        limit: 50
      });
      // API returns array directly, not wrapped in object
      const registrationsArray = Array.isArray(registrationsData) ? registrationsData : registrationsData.registrations || [];

      // Ensure all registrations are valid objects with required nested structure
      const validRegistrations = registrationsArray.filter(reg =>
        reg &&
        typeof reg === 'object' &&
        reg.registration_id &&
        reg.event &&
        typeof reg.event === 'object'
      );

      console.log('Loaded registrations:', validRegistrations.length, 'valid out of', registrationsArray.length);
      setRegistrations(validRegistrations);

    } catch (err) {
      setError(err.message || 'Failed to load event dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Clear cache before refreshing
      newEventService.clearCache();
      await loadDashboardData();
      showSuccess('Dashboard refreshed successfully');
    } catch (err) {
      showError('Failed to refresh dashboard');
    } finally {
      setRefreshing(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handlePayment = async (registration) => {
    try {
      showInfo('Creating payment link...');

      // Use the same payment service as MyRegistrations page
      const paymentResult = await newEventService.payForRegistration(registration.registration_id);

      if (paymentResult.success) {
        showInfo('Redirecting to payment...');
        // The service will automatically handle the PayFast redirect
      } else {
        showError('Failed to create payment link');
      }

    } catch (error) {
      console.error('Payment error:', error);
      showError(error.message || 'Failed to process payment');
    }
  };

  // Handle downloading ticket
  const handleDownloadTicket = async (registration) => {
    try {
      console.log('Download ticket clicked for registration:', registration);
      showInfo('Generating ticket PDF...');

      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      console.log('Using token:', token ? 'Token found' : 'No token');

      // Fetch ticket data from backend
      const response = await fetch(`${BASE_API}/api/tickets/registrations/${registration.registration_id}/ticket`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Ticket data response status:', response.status);

      if (!response.ok) {
        throw new Error(`Failed to fetch ticket data: ${response.status} ${response.statusText}`);
      }

      const ticketData = await response.json();
      console.log('Ticket data received:', ticketData);

      // Generate and download PDF
      showInfo('Creating PDF...');
      const result = downloadTicketPDF(ticketData);

      if (result.success) {
        showSuccess('Ticket PDF downloaded successfully!');
      } else {
        throw new Error(result.error || 'Failed to generate PDF');
      }
    } catch (error) {
      console.error('Download error:', error);
      showError(`Failed to download ticket: ${error.message}`);
    }
  };

  // Handle viewing event details
  const handleViewDetails = (registration) => {
    if (registration.event?.id) {
      // Navigate to event details page
      window.open(`/events/${registration.event.id}`, '_blank');
    } else {
      showError('Event details not available');
    }
  };

  // Helper function to determine if an event is exam/competition based
  const isExamEvent = (registration) => {
    return registration.event?.exam_id ||
           registration.event?.category?.toLowerCase().includes('exam') ||
           registration.event?.category?.toLowerCase().includes('competition') ||
           registration.event?.title?.toLowerCase().includes('exam') ||
           registration.event?.title?.toLowerCase().includes('competition');
  };

  // Filter registrations based on current filters
  const filteredRegistrations = registrations.filter(registration => {
    // Status filter
    if (filters.status !== 'all' && registration.status?.toLowerCase() !== filters.status.toLowerCase()) {
      return false;
    }

    // Search filter
    if (filters.search && !registration.event?.title?.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    // Upcoming filter
    if (filters.upcoming && new Date(registration.event?.start_datetime) <= new Date()) {
      return false;
    }

    return true;
  });

  // Separate exam/competition events from regular events
  const examEvents = filteredRegistrations.filter(isExamEvent);
  const regularEvents = filteredRegistrations.filter(reg => !isExamEvent(reg));

  const formatDate = (dateString) => {
    if (!dateString) return 'Date TBD';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return format(date, 'MMM dd, yyyy');
    } catch (error) {
      console.warn('Date formatting error:', error, 'for date:', dateString);
      return 'Date TBD';
    }
  };

  const formatTime = (dateString) => {
    if (!dateString) return 'Time TBD';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Time TBD';
      return format(date, 'h:mm a');
    } catch (error) {
      console.warn('Time formatting error:', error, 'for date:', dateString);
      return 'Time TBD';
    }
  };

  const getStatusBadge = (status, paymentStatus) => {
    // Handle API status values (uppercase)
    const normalizedStatus = status?.toLowerCase();
    const normalizedPaymentStatus = paymentStatus?.toLowerCase();

    if (normalizedStatus === 'confirmed') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FiCheck className="w-3 h-3 mr-1" />
          Confirmed
        </span>
      );
    } else if (normalizedStatus === 'pending') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <FiClock className="w-3 h-3 mr-1" />
          {normalizedPaymentStatus === 'pending' ? 'Payment Pending' : 'Pending'}
        </span>
      );
    } else if (normalizedStatus === 'cancelled') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <FiX className="w-3 h-3 mr-1" />
          Cancelled
        </span>
      );
    }

    // Fallback for unknown status
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {status || 'Unknown'}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadDashboardData} />;
  }

  return (
    <div className="space-y-6">
      {/* Header - only show if not embedded */}
      {!embedded && (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Events</h1>
            <p className="text-gray-600">Manage your event registrations and tickets</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      )}

      {/* Refresh button for embedded mode */}
      {embedded && (
        <div className="flex justify-end">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      )}

      {/* Enhanced Dashboard Stats */}
      {dashboard && (
        <div className="mobile-stats-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl card-shadow-medium border border-blue-200 p-6 event-card-hover">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-blue-600 rounded-lg">
                  <FiCalendar className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-700">Total Events</p>
                <p className="text-2xl font-bold text-blue-900">{Number(dashboard.total_registrations) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl card-shadow-medium border border-green-200 p-6 event-card-hover">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-green-600 rounded-lg">
                  <FiCheck className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-green-700">Confirmed</p>
                <p className="text-2xl font-bold text-green-900">{Number(dashboard.confirmed_registrations) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl card-shadow-medium border border-yellow-200 p-6 event-card-hover">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-yellow-600 rounded-lg">
                  <FiClock className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-yellow-700">Upcoming</p>
                <p className="text-2xl font-bold text-yellow-900">{Number(dashboard.upcoming_events) || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl card-shadow-medium border border-purple-200 p-6 event-card-hover">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 bg-purple-600 rounded-lg">
                  <FiAward className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-purple-700">Exams</p>
                <p className="text-2xl font-bold text-purple-900">{examEvents.length}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Filters */}
      <div className="filter-section bg-white rounded-xl card-shadow-medium border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center visual-hierarchy-1">
            <FiFilter className="h-5 w-5 mr-2 text-gray-600" />
            Filter Events
          </h3>
          <span className="text-sm text-gray-500 status-badge px-3 py-1 bg-gray-100 rounded-full">
            {filteredRegistrations.length} event{filteredRegistrations.length !== 1 ? 's' : ''} found
          </span>
        </div>

        <div className="mobile-event-grid grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 visual-hierarchy-2">Search Events</label>
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by event name..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="search-input-enhanced w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus-enhanced transition-colors"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 visual-hierarchy-2">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus-enhanced transition-colors"
            >
              <option value="all">All Status</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 visual-hierarchy-2">Options</label>
            <div className="flex items-center h-12">
              <label className="flex items-center cursor-pointer interactive-hover p-2 rounded-lg">
                <input
                  type="checkbox"
                  checked={filters.upcoming}
                  onChange={(e) => handleFilterChange('upcoming', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                />
                <span className="ml-3 text-sm text-gray-700 font-medium">Upcoming events only</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Exam/Competition Events Section */}
      {examEvents.length > 0 && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl card-shadow-strong border border-purple-200 mobile-event-card">
          <div className="px-6 py-4 border-b border-purple-200 exam-event-gradient rounded-t-xl">
            <h2 className="text-lg font-bold text-white flex items-center visual-hierarchy-1">
              <FiAward className="h-6 w-6 mr-3" />
              Competition & Exam Events ({examEvents.length})
            </h2>
            <p className="text-purple-100 text-sm mt-1">Take exams and participate in competitions</p>
          </div>

          <div className="divide-y divide-purple-100">
            {examEvents.map((registration) => (
              <ExamEventCard
                key={registration.registration_id}
                registration={registration}
                onPayment={handlePayment}
                onDownloadTicket={handleDownloadTicket}
                onViewDetails={handleViewDetails}
                navigate={navigate}
                showSuccess={showSuccess}
                showError={showError}
                formatDate={formatDate}
                formatTime={formatTime}
                getStatusBadge={getStatusBadge}
              />
            ))}
          </div>
        </div>
      )}

      {/* Regular Events Section */}
      <div className="bg-white rounded-xl card-shadow-medium border border-gray-200 mobile-event-card">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center visual-hierarchy-1">
            <FiCalendar className="h-5 w-5 mr-2 text-gray-600" />
            Regular Events ({regularEvents.length})
          </h2>
          <p className="text-gray-600 text-sm mt-1 visual-hierarchy-3">Workshops, seminars, and other events</p>
        </div>

        {regularEvents.length === 0 ? (
          <div className="empty-state text-center py-12">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2 visual-hierarchy-2">No regular events found</h3>
            <p className="text-gray-500 visual-hierarchy-3">
              {filters.search || filters.status !== 'all' || filters.upcoming
                ? 'Try adjusting your filters'
                : 'You haven\'t registered for any regular events yet'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {regularEvents.map((registration) => (
              <RegularEventCard
                key={registration.registration_id}
                registration={registration}
                onPayment={handlePayment}
                onDownloadTicket={handleDownloadTicket}
                onViewDetails={handleViewDetails}
                formatDate={formatDate}
                formatTime={formatTime}
                getStatusBadge={getStatusBadge}
              />
            ))}
          </div>
        )}
      </div>

      {/* Show all events if no separation needed */}
      {examEvents.length === 0 && regularEvents.length === 0 && filteredRegistrations.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="text-center py-12">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
            <p className="text-gray-500">
              {filters.search || filters.status !== 'all' || filters.upcoming
                ? 'Try adjusting your filters'
                : 'You haven\'t registered for any events yet'
              }
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

// Exam Event Card Component
const ExamEventCard = ({
  registration,
  onPayment,
  onDownloadTicket,
  onViewDetails,
  navigate,
  showSuccess,
  showError,
  formatDate,
  formatTime,
  getStatusBadge
}) => {
  return (
    <div className="p-6 exam-event-card-hover interactive-hover transition-colors">
      <div className="flex items-start justify-between mobile-event-grid">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiAward className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="event-title-responsive text-lg font-bold text-gray-900 visual-hierarchy-1">
                  {String(registration.event?.title || 'Untitled Event')}
                </h3>
                <p className="event-subtitle-responsive text-sm text-purple-600 font-medium">Competition/Exam Event</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(registration.status, registration.payment_status)}
              <div className="timing-badge-glow">
                <EventTimingBadge event={registration.event} variant="inline" size="small" />
              </div>
              {/* Exam availability indicator */}
              {(() => {
                const timingStatus = getEventTimingStatus(registration.event);
                if (timingStatus.status === 'ongoing') {
                  return (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 animate-pulse">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                      EXAM LIVE
                    </span>
                  );
                }
                if (timingStatus.status === 'ended') {
                  return (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <span className="w-2 h-2 bg-gray-500 rounded-full mr-1"></span>
                      EXAM ENDED
                    </span>
                  );
                }
                return null;
              })()}
            </div>
          </div>

          {/* Event Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
            <div className="flex items-center">
              <FiCalendar className="w-4 h-4 mr-2 text-purple-500" />
              <span>
                {formatDate(registration.event?.start_datetime)} at {formatTime(registration.event?.start_datetime)}
              </span>
            </div>

            {registration.event?.location && (
              <div className="flex items-center">
                <FiMapPin className="w-4 h-4 mr-2 text-purple-500" />
                <span>{String(registration.event.location)}</span>
              </div>
            )}
          </div>

          {/* Exam-specific info */}
          <div className="bg-purple-50 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
              <div>
                <div className="font-medium text-purple-900">Exam ID</div>
                <div className="text-purple-700">{registration.event?.exam_id || 'N/A'}</div>
              </div>
              <div>
                <div className="font-medium text-purple-900">Registration #</div>
                <div className="text-purple-700">{String(registration.registration_number || 'N/A')}</div>
              </div>
              <div>
                <div className="font-medium text-purple-900">Fee</div>
                <div className="text-purple-700">
                  {String(registration.total_amount || 0)} {String(registration.currency || 'ZAR')}
                </div>
              </div>
            </div>

            {/* Exam availability info */}
            {(() => {
              const timingStatus = getEventTimingStatus(registration.event);

              if (timingStatus.status === 'ended') {
                return (
                  <div className="flex items-center text-xs text-gray-600 bg-gray-100 rounded p-2">
                    <FiX className="w-3 h-3 mr-1 text-red-500" />
                    This exam is no longer available as the event has ended.
                  </div>
                );
              }

              if (timingStatus.status === 'upcoming') {
                return (
                  <div className="flex items-center text-xs text-yellow-700 bg-yellow-100 rounded p-2">
                    <FiClock className="w-3 h-3 mr-1 text-yellow-600" />
                    Exam will become available when the event starts.
                  </div>
                );
              }

              if (timingStatus.status === 'ongoing') {
                return (
                  <div className="flex items-center text-xs text-green-700 bg-green-100 rounded p-2">
                    <FiAward className="w-3 h-3 mr-1 text-green-600" />
                    Exam is now available! Click the button to start.
                  </div>
                );
              }

              return null;
            })()}
          </div>
        </div>

        <div className="button-group mobile-event-actions flex flex-col space-y-2 ml-4">
          {/* Payment button for pending registrations */}
          {registration.status?.toLowerCase() === 'pending' && registration.payment_status?.toLowerCase() === 'pending' && (
            <button
              onClick={() => onPayment(registration)}
              className="mobile-event-button px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium focus-enhanced"
              title="Complete Payment"
            >
              Pay Now
            </button>
          )}

          {/* Download ticket for confirmed registrations */}
          {registration.status?.toLowerCase() === 'confirmed' && (
            <button
              onClick={() => onDownloadTicket(registration)}
              className="mobile-event-button px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium flex items-center focus-enhanced"
              title="Download Ticket"
            >
              <FiDownload className="w-4 h-4 mr-1" />
              Ticket
            </button>
          )}

          {/* COMPETITION EXAM BUTTON - Smart logic based on event timing */}
          {(() => {
            const timingStatus = getEventTimingStatus(registration.event);
            const eventId = registration.event?.id || registration.event_id;
            const examId = registration.event?.exam_id;

            // If event has ended, show disabled state
            if (timingStatus.status === 'ended') {
              return (
                <div className="mobile-event-button exam-button-disabled px-4 py-2 text-white rounded-lg text-sm font-bold flex items-center justify-center">
                  <FiX className="w-4 h-4 mr-2" />
                  EXAM ENDED
                </div>
              );
            }

            // If event is upcoming, show pending state
            if (timingStatus.status === 'upcoming') {
              return (
                <div className="mobile-event-button exam-button-pending px-4 py-2 text-white rounded-lg text-sm font-bold flex items-center justify-center">
                  <FiClock className="w-4 h-4 mr-2" />
                  EXAM PENDING
                </div>
              );
            }

            // If event is ongoing, show active exam button
            if (timingStatus.status === 'ongoing') {
              return (
                <button
                  onClick={async () => {
                    if (!eventId) {
                      showError('No event ID found');
                      return;
                    }

                    if (!examId) {
                      showError('No exam linked to this event');
                      return;
                    }

                    navigate(`/student/take-competition-exam/${eventId}`, {
                      state: {
                        event: registration.event,
                        registration: registration,
                        examId: examId
                      }
                    });
                  }}
                  className="mobile-event-button exam-button animate-pulse px-4 py-2 text-white rounded-lg text-sm font-bold flex items-center justify-center focus-enhanced"
                  title="Take Competition Exam - Event is Live!"
                >
                  <FiAward className="w-4 h-4 mr-2" />
                  TAKE EXAM NOW
                </button>
              );
            }

            // Fallback - shouldn't reach here
            return null;
          })()}

          {/* View details button */}
          <button
            onClick={() => onViewDetails(registration)}
            className="p-2 text-gray-400 hover:text-purple-600 transition-colors rounded-lg hover:bg-purple-50 interactive-hover focus-enhanced"
            title="View Event Details"
          >
            <FiEye className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Regular Event Card Component
const RegularEventCard = ({
  registration,
  onPayment,
  onDownloadTicket,
  onViewDetails,
  formatDate,
  formatTime,
  getStatusBadge
}) => {
  return (
    <div className="p-6 hover:bg-gray-50 transition-colors interactive-hover">
      <div className="flex items-start justify-between mobile-event-grid">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <h3 className="event-title-responsive text-lg font-medium text-gray-900 visual-hierarchy-1">
                {String(registration.event?.title || 'Untitled Event')}
              </h3>
              {getStatusBadge(registration.status, registration.payment_status)}
            </div>
            <div className="timing-badge-glow">
              <EventTimingBadge event={registration.event} variant="inline" size="small" />
            </div>
          </div>

          {/* Event Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
            <div className="flex items-center">
              <FiCalendar className="w-4 h-4 mr-2" />
              <span>
                {formatDate(registration.event?.start_datetime)} at {formatTime(registration.event?.start_datetime)}
              </span>
            </div>

            {registration.event?.location && (
              <div className="flex items-center">
                <FiMapPin className="w-4 h-4 mr-2" />
                <span>{String(registration.event.location)}</span>
              </div>
            )}
          </div>

          {/* Ticket & Payment Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="font-medium text-blue-900">{String(registration.ticket?.name || 'Standard Ticket')}</div>
              <div className="text-blue-700 text-xs">{String(registration.ticket?.description || 'Ticket')}</div>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <div className="font-medium text-green-900">
                {String(registration.total_amount || 0)} {String(registration.currency || 'ZAR')}
              </div>
              <div className="text-green-700 text-xs">
                Quantity: {String(registration.quantity || 1)}
              </div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="font-medium text-gray-900 text-xs">
                {String(registration.registration_number || 'N/A')}
              </div>
              <div className="text-gray-600 text-xs">
                Registered: {formatDate(registration.registered_at)}
              </div>
            </div>
          </div>

          {/* Attendee Info */}
          <div className="bg-gray-50 p-3 rounded-lg text-sm">
            <div className="font-medium text-gray-900 mb-1">Attendee Information</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-gray-600">
              <div><strong>Name:</strong> {String(registration.attendee_info?.name || 'N/A')}</div>
              {registration.attendee_info?.email && (
                <div><strong>Email:</strong> {String(registration.attendee_info.email)}</div>
              )}
              {registration.attendee_info?.phone && (
                <div><strong>Phone:</strong> {String(registration.attendee_info.phone)}</div>
              )}
              {registration.attendee_info?.company && (
                <div><strong>Company:</strong> {String(registration.attendee_info.company)}</div>
              )}
              {registration.attendee_info?.position && (
                <div><strong>Position:</strong> {String(registration.attendee_info.position)}</div>
              )}
              <div><strong>Registration #:</strong> {String(registration.registration_number || 'N/A')}</div>
            </div>
            {registration.special_requirements && (
              <div className="mt-2 text-xs text-orange-600">
                <strong>Special Requirements:</strong> {String(registration.special_requirements)}
              </div>
            )}
          </div>
        </div>

        <div className="button-group mobile-event-actions flex flex-col space-y-2 ml-4">
          {/* Payment button for pending registrations */}
          {registration.status?.toLowerCase() === 'pending' && registration.payment_status?.toLowerCase() === 'pending' && (
            <button
              onClick={() => onPayment(registration)}
              className="mobile-event-button px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium focus-enhanced"
              title="Complete Payment"
            >
              Pay Now
            </button>
          )}

          {/* Download ticket for confirmed registrations */}
          {registration.status?.toLowerCase() === 'confirmed' && (
            <button
              onClick={() => onDownloadTicket(registration)}
              className="mobile-event-button px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium flex items-center focus-enhanced"
              title="Download Ticket"
            >
              <FiDownload className="w-4 h-4 mr-1" />
              Ticket
            </button>
          )}

          {/* View details button */}
          <button
            onClick={() => onViewDetails(registration)}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-50 interactive-hover focus-enhanced"
            title="View Event Details"
          >
            <FiEye className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserEventDashboard;
