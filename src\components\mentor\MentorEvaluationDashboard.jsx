import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  <PERSON>A<PERSON>,
  FiUsers,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiBarChart2,
  FiEye,
  FiBookOpen,
  FiTrendingUp,
  FiTarget,
  FiRefreshCw
} from 'react-icons/fi';
import {
  fetchMentorDashboard,
  fetchAssignedCompetitions,
  selectDashboard,
  selectCompetitions,
  clearErrors
} from '../../store/slices/MentorEvaluationSlice';
import { LoadingSpinner, ErrorMessage } from '../ui';

const MentorEvaluationDashboard = ({ onCompetitionSelect }) => {
  const dispatch = useDispatch();
  const dashboard = useSelector(selectDashboard);
  const competitions = useSelector(selectCompetitions);

  useEffect(() => {
    loadDashboardData();
  }, [dispatch]);

  const loadDashboardData = () => {
    dispatch(clearErrors());
    dispatch(fetchMentorDashboard());
    dispatch(fetchAssignedCompetitions());
  };

  const handleRetry = () => {
    loadDashboardData();
  };

  const getProgressColor = (percentage) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { color: 'bg-green-100 text-green-800', label: 'Active' },
      'pending': { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      'completed': { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
      'overdue': { color: 'bg-red-100 text-red-800', label: 'Overdue' }
    };
    
    const config = statusConfig[status] || statusConfig['pending'];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const isLoading = dashboard.loading || competitions.loading;
  const hasError = dashboard.error || competitions.error;

  if (isLoading && !dashboard.data && !competitions.data.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (hasError && !dashboard.data && !competitions.data.length) {
    return (
      <ErrorMessage
        message={dashboard.error || competitions.error || 'Failed to load dashboard data'}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Messages */}
      {hasError && (dashboard.data || competitions.data.length > 0) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-yellow-800">
              {dashboard.error || competitions.error}
            </p>
            <button
              onClick={handleRetry}
              className="inline-flex items-center text-sm text-yellow-800 hover:text-yellow-900"
            >
              <FiRefreshCw className="h-4 w-4 mr-1" />
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Dashboard Statistics */}
      {dashboard.data && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiAward className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Competitions</p>
                <p className="text-2xl font-semibold text-gray-900">{dashboard.data.total_competitions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiClock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Competitions</p>
                <p className="text-2xl font-semibold text-gray-900">{dashboard.data.active_competitions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiUsers className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Submissions</p>
                <p className="text-2xl font-semibold text-gray-900">{dashboard.data.total_submissions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCheckCircle className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completed Reviews</p>
                <p className="text-2xl font-semibold text-gray-900">{dashboard.data.completed_submissions}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Progress Overview */}
      {dashboard.data && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Evaluation Progress</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Pending Submissions</span>
                  <span className="font-medium">{dashboard.data.pending_submissions}</span>
                </div>
                <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full"
                    style={{ width: `${(dashboard.data.pending_submissions / dashboard.data.total_submissions) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Completed Submissions</span>
                  <span className="font-medium">{dashboard.data.completed_submissions}</span>
                </div>
                <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${(dashboard.data.completed_submissions / dashboard.data.total_submissions) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Workload Overview</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Current Workload</span>
                <span className="text-lg font-semibold text-gray-900">{dashboard.data.current_workload}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Capacity</span>
                <span className="text-lg font-semibold text-gray-900">{dashboard.data.total_workload}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Average Score</span>
                <span className="text-lg font-semibold text-gray-900">{dashboard.data.average_score}%</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Assigned Competitions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Assigned Competitions</h3>
          <p className="text-sm text-gray-600 mt-1">Competitions you are assigned to evaluate</p>
        </div>
        
        <div className="p-6">
          {competitions.loading && competitions.data.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : competitions.data.length === 0 ? (
            <div className="text-center py-8">
              <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No competitions assigned yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {competitions.data.map((competition) => (
                <div 
                  key={competition.assignment_id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => onCompetitionSelect && onCompetitionSelect(competition)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-medium text-gray-900">{competition.competition_title}</h4>
                      <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Total Submissions:</span>
                          <span className="ml-2 font-medium">{competition.total_submissions}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Checked:</span>
                          <span className="ml-2 font-medium">{competition.checked_submissions}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Progress:</span>
                          <span className="ml-2 font-medium">{competition.progress_percentage}%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(competition.assignment_status)}
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${getProgressColor(competition.progress_percentage)}`}
                          style={{ width: `${competition.progress_percentage}%` }}
                        ></div>
                      </div>
                      <FiEye className="h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MentorEvaluationDashboard;
