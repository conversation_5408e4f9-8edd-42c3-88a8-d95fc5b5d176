import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudentAnalytics } from '../../../hooks/useStudentAnalytics';
import { FluidPageContainer } from '../../../components/ui/layout';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiBookOpen,
  FiTarget,
  FiClock,
  FiCheckCircle,
  FiArrowLeft,
  FiBarChart2
} from 'react-icons/fi';

// Chart components
import { D3<PERSON><PERSON><PERSON><PERSON>, D3Bar<PERSON>hart, D3Heatmap } from '../../../components/charts';

/**
 * Subject Analytics Page
 * Detailed analytics for individual subjects
 */
const SubjectAnalytics = () => {
  const navigate = useNavigate();

  // Use the analytics hook
  const {
    data: { subject: subjectAnalytics },
    loading: { subject: subjectLoading },
    errors: { subject: subjectError },
    fetch: { subject: fetchSubjectData },
    clearErrors
  } = useStudentAnalytics({
    autoFetch: false // We'll fetch manually with specific date range
  });

  const [selectedSubject, setSelectedSubject] = useState(null);
  const [dateRange, setDateRange] = useState({
    start_date: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1).toISOString(),
    end_date: new Date().toISOString(),
    period_type: 'monthly'
  });

  // Fetch subject analytics
  useEffect(() => {
    fetchSubjectData(dateRange);
  }, [fetchSubjectData, dateRange]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearErrors();
    };
  }, [clearErrors]);

  // Set default selected subject
  useEffect(() => {
    if (subjectAnalytics?.subjects?.length > 0 && !selectedSubject) {
      setSelectedSubject(subjectAnalytics.subjects[0]);
    }
  }, [subjectAnalytics, selectedSubject]);

  // Loading state
  if (subjectLoading) {
    return (
      <FluidPageContainer>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </FluidPageContainer>
    );
  }

  // Error state
  if (subjectError) {
    return (
      <FluidPageContainer>
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">
            <FiBookOpen />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
            Unable to Load Subject Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {subjectError}
          </p>
          <button
            onClick={() => fetchSubjectData(dateRange)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </FluidPageContainer>
    );
  }

  if (!subjectAnalytics || !selectedSubject) return null;

  // Prepare chart data
  const performanceTrendData = selectedSubject.performance_trend?.map(item => ({
    x: item.period,
    y: item.value,
    change: item.change_percentage,
    trend: item.trend_direction
  })) || [];

  const chapterPerformanceData = Object.entries(selectedSubject.chapter_performance || {}).map(([chapter, score]) => ({
    x: chapter,
    y: score
  }));

  // Heatmap data for study patterns (mock data for demonstration)
  const studyPatternData = [
    { x: 'Mon', y: 'Morning', value: 2 },
    { x: 'Mon', y: 'Afternoon', value: 3 },
    { x: 'Mon', y: 'Evening', value: 1 },
    { x: 'Tue', y: 'Morning', value: 1 },
    { x: 'Tue', y: 'Afternoon', value: 4 },
    { x: 'Tue', y: 'Evening', value: 2 },
    { x: 'Wed', y: 'Morning', value: 3 },
    { x: 'Wed', y: 'Afternoon', value: 2 },
    { x: 'Wed', y: 'Evening', value: 3 },
    { x: 'Thu', y: 'Morning', value: 2 },
    { x: 'Thu', y: 'Afternoon', value: 3 },
    { x: 'Thu', y: 'Evening', value: 1 },
    { x: 'Fri', y: 'Morning', value: 4 },
    { x: 'Fri', y: 'Afternoon', value: 1 },
    { x: 'Fri', y: 'Evening', value: 2 },
  ];

  // Get trend icon
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
      case 'improving':
        return <FiTrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
      case 'declining':
        return <FiTrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <FiMinus className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <FluidPageContainer>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <button
            onClick={() => navigate('/student/analytics')}
            className="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            Subject Analytics
          </h1>
        </div>
        
        {/* Subject Selector */}
        <div className="flex flex-wrap gap-2">
          {subjectAnalytics.subjects.map((subject) => (
            <button
              key={subject.subject_id}
              onClick={() => setSelectedSubject(subject)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedSubject.subject_id === subject.subject_id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {subject.subject_name}
            </button>
          ))}
        </div>
      </div>

      {/* Subject Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Average Score
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.average_score}%
              </p>
              <div className="flex items-center mt-2">
                {getTrendIcon(selectedSubject.ranking?.rank_trend)}
                <span className="text-xs text-gray-500 ml-1">
                  vs Class: {selectedSubject.class_average}%
                </span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiTarget className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Class Rank
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                #{selectedSubject.ranking?.current_rank}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {selectedSubject.ranking?.percentile}th percentile
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiBarChart2 className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Study Hours
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.study_time_hours}h
              </p>
              <p className="text-xs text-gray-500 mt-2">
                This period
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiClock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Completion Rate
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.assignment_completion_rate}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {selectedSubject.total_assignments} assignments
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiCheckCircle className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Performance Trend */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Performance Trend
          </h3>
          {performanceTrendData.length > 0 ? (
            <D3LineChart
              data={performanceTrendData}
              width={500}
              height={300}
              xKey="x"
              yKey="y"
              showDots={true}
              showArea={true}
              animate={true}
              xAxisLabel="Period"
              yAxisLabel="Score"
            />
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No trend data available
            </div>
          )}
        </div>

        {/* Chapter Performance */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Chapter Performance
          </h3>
          {chapterPerformanceData.length > 0 ? (
            <D3BarChart
              data={chapterPerformanceData}
              width={500}
              height={300}
              xKey="x"
              yKey="y"
              showValues={true}
              animate={true}
              xAxisLabel="Chapters"
              yAxisLabel="Score"
            />
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No chapter data available
            </div>
          )}
        </div>
      </div>

      {/* Study Pattern Heatmap */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
          Study Pattern Analysis
        </h3>
        <D3Heatmap
          data={studyPatternData}
          width={700}
          height={200}
          xKey="x"
          yKey="y"
          valueKey="value"
          xAxisLabel="Day of Week"
          yAxisLabel="Time of Day"
          showValues={true}
          animate={true}
        />
      </div>

      {/* Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Strengths */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
          <h4 className="font-semibold text-green-800 dark:text-green-200 mb-4">
            Strongest Areas
          </h4>
          <ul className="space-y-2">
            {selectedSubject.strongest_chapters?.map((chapter, index) => (
              <li key={index} className="flex items-center text-green-700 dark:text-green-300">
                <FiCheckCircle className="w-4 h-4 mr-2" />
                {chapter}
              </li>
            ))}
          </ul>
        </div>

        {/* Improvement Areas */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
          <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-4">
            Areas for Improvement
          </h4>
          <ul className="space-y-2">
            {selectedSubject.improvement_areas?.map((area, index) => (
              <li key={index} className="flex items-center text-yellow-700 dark:text-yellow-300">
                <FiTarget className="w-4 h-4 mr-2" />
                {area}
              </li>
            ))}
          </ul>
          <div className="mt-4">
            <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Recommended Actions:
            </h5>
            <ul className="space-y-1">
              {selectedSubject.recommended_actions?.map((action, index) => (
                <li key={index} className="text-sm text-yellow-700 dark:text-yellow-300">
                  • {action}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </FluidPageContainer>
  );
};

export default SubjectAnalytics;
