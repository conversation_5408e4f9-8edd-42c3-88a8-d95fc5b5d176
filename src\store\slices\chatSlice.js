import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import chatService from '../../services/chatService';

// Async thunks for chat operations

// Send a message
export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ receiverId, message }, { rejectWithValue }) => {
    try {
      const response = await chatService.sendMessage(receiverId, message);
      return { receiverId, message: response };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Get conversations
export const getConversations = createAsyncThunk(
  'chat/getConversations',
  async ({ page = 1, pageSize = 20 } = {}, { rejectWithValue }) => {
    try {
      const response = await chatService.getConversations(page, pageSize);
      return response;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Get conversation messages
export const getConversationMessages = createAsyncThunk(
  'chat/getConversationMessages',
  async ({ userId, page = 1, pageSize = 50 }, { rejectWithValue }) => {
    try {
      const response = await chatService.getConversationMessages(userId, page, pageSize);
      return { userId, ...response };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Mark conversation as read
export const markConversationRead = createAsyncThunk(
  'chat/markConversationRead',
  async (userId, { rejectWithValue }) => {
    try {
      await chatService.markConversationRead(userId);
      return userId;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Delete message
export const deleteMessage = createAsyncThunk(
  'chat/deleteMessage',
  async (messageId, { rejectWithValue }) => {
    try {
      await chatService.deleteMessage(messageId);
      return messageId;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Get chat stats
export const getChatStats = createAsyncThunk(
  'chat/getChatStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await chatService.getChatStats();
      return response;
    } catch (error) {
      console.warn('Chat stats failed, using defaults:', error.message);
      // Return default stats instead of rejecting to prevent UI breaking
      return {
        user_id: localStorage.getItem('userId'),
        total_conversations: 0,
        total_messages_sent: 0,
        total_messages_received: 0,
        unread_messages_count: 0,
        active_conversations_count: 0
      };
    }
  }
);

const initialState = {
  // Conversations
  conversations: [],
  conversationsLoading: false,
  conversationsError: null,
  conversationsPagination: {
    page: 1,
    totalCount: 0,
    hasNext: false,
    hasPrevious: false
  },

  // Current conversation messages
  currentConversation: null,
  messages: {},
  messagesLoading: false,
  messagesError: null,
  messagesPagination: {},

  // Sending messages
  sendingMessage: false,
  sendMessageError: null,

  // Chat stats
  stats: {
    totalConversations: 0,
    totalMessagesSent: 0,
    totalMessagesReceived: 0,
    unreadMessagesCount: 0,
    activeConversationsCount: 0
  },
  statsLoading: false,
  statsError: null,

  // UI state
  selectedConversationId: null
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    // Set selected conversation
    setSelectedConversation: (state, action) => {
      state.selectedConversationId = action.payload;
      state.currentConversation = state.conversations.find(
        conv => conv.other_user.id === action.payload
      );
    },

    // Clear messages for a conversation
    clearMessages: (state, action) => {
      const userId = action.payload;
      if (state.messages[userId]) {
        delete state.messages[userId];
      }
    },

    // Add new message to conversation (for real-time updates)
    addMessage: (state, action) => {
      const { message, conversationUserId } = action.payload;

      if (!state.messages[conversationUserId]) {
        state.messages[conversationUserId] = [];
      }

      // Push to end instead of unshift to maintain chronological order
      state.messages[conversationUserId].push(message);

      // Update conversation last message
      const conversation = state.conversations.find(
        conv => conv.other_user.id === conversationUserId
      );
      if (conversation) {
        conversation.last_message = message;
        conversation.last_activity = message.sent_at;

        // Update unread count if message is from other user
        const currentUserId = localStorage.getItem('userId');
        if (message.sender_id !== currentUserId) {
          conversation.unread_count = (conversation.unread_count || 0) + 1;
        }
      }
    },

    // Update message status (read, deleted, etc.)
    updateMessage: (state, action) => {
      const { messageId, updates } = action.payload;
      
      Object.keys(state.messages).forEach(userId => {
        const messageIndex = state.messages[userId].findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          state.messages[userId][messageIndex] = {
            ...state.messages[userId][messageIndex],
            ...updates
          };
        }
      });
    },

    // Set typing status
    setTyping: (state, action) => {
      const { userId, isTyping } = action.payload;
      if (isTyping) {
        state.typingUsers[userId] = true;
      } else {
        delete state.typingUsers[userId];
      }
    },

    // Clear all chat data (for logout)
    clearChatData: (state) => {
      return { ...initialState };
    },

    // Clear errors
    clearErrors: (state) => {
      state.conversationsError = null;
      state.messagesError = null;
      state.sendMessageError = null;
      state.statsError = null;
    },

    // Add virtual conversation for new chats
    addVirtualConversation: (state, action) => {
      const virtualConversation = action.payload;

      // Check if conversation already exists
      const existingIndex = state.conversations.findIndex(
        conv => conv.other_user.id === virtualConversation.other_user.id
      );

      if (existingIndex === -1) {
        // Add virtual conversation to the beginning of the list
        state.conversations.unshift(virtualConversation);

        // Initialize empty messages array for this conversation
        state.messages[virtualConversation.other_user.id] = [];
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Get conversations
      .addCase(getConversations.pending, (state) => {
        state.conversationsLoading = true;
        state.conversationsError = null;
      })
      .addCase(getConversations.fulfilled, (state, action) => {
        state.conversationsLoading = false;
        state.conversations = action.payload.conversations || [];
        state.conversationsPagination = {
          page: action.payload.page || 1,
          totalCount: action.payload.total_count || 0,
          hasNext: action.payload.has_next || false,
          hasPrevious: action.payload.has_previous || false
        };
      })
      .addCase(getConversations.rejected, (state, action) => {
        state.conversationsLoading = false;
        state.conversationsError = action.payload;
      })

      // Get conversation messages
      .addCase(getConversationMessages.pending, (state) => {
        state.messagesLoading = true;
        state.messagesError = null;
      })
      .addCase(getConversationMessages.fulfilled, (state, action) => {
        state.messagesLoading = false;
        const { userId, messages, page, total_count, has_next, has_previous } = action.payload;
        
        if (page === 1) {
          state.messages[userId] = messages || [];
        } else {
          state.messages[userId] = [...(state.messages[userId] || []), ...(messages || [])];
        }
        
        state.messagesPagination[userId] = {
          page,
          totalCount: total_count || 0,
          hasNext: has_next || false,
          hasPrevious: has_previous || false
        };
      })
      .addCase(getConversationMessages.rejected, (state, action) => {
        state.messagesLoading = false;
        state.messagesError = action.payload;
      })

      // Send message
      .addCase(sendMessage.pending, (state) => {
        state.sendingMessage = true;
        state.sendMessageError = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.sendingMessage = false;
        const { receiverId, message } = action.payload;

        // Add message to conversation
        if (!state.messages[receiverId]) {
          state.messages[receiverId] = [];
        }
        // Push to end instead of unshift to maintain chronological order
        state.messages[receiverId].push(message);

        // Update conversation
        const conversation = state.conversations.find(
          conv => conv.other_user.id === receiverId
        );
        if (conversation) {
          conversation.last_message = message;
          conversation.last_activity = message.sent_at;

          // If this was a virtual conversation, mark it as real
          if (conversation.is_virtual) {
            delete conversation.is_virtual;
          }
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.sendingMessage = false;
        state.sendMessageError = action.payload;
      })

      // Mark conversation as read
      .addCase(markConversationRead.fulfilled, (state, action) => {
        const userId = action.payload;
        const conversation = state.conversations.find(
          conv => conv.other_user.id === userId
        );
        if (conversation) {
          conversation.unread_count = 0;
        }
        
        // Mark all messages as read
        if (state.messages[userId]) {
          state.messages[userId].forEach(message => {
            if (!message.read_at) {
              message.read_at = new Date().toISOString();
            }
          });
        }
      })

      // Delete message
      .addCase(deleteMessage.fulfilled, (state, action) => {
        const messageId = action.payload;
        
        Object.keys(state.messages).forEach(userId => {
          const messageIndex = state.messages[userId].findIndex(msg => msg.id === messageId);
          if (messageIndex !== -1) {
            state.messages[userId][messageIndex].is_deleted = true;
          }
        });
      })

      // Get chat stats
      .addCase(getChatStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(getChatStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload;
      })
      .addCase(getChatStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  }
});

export const {
  setSelectedConversation,
  clearMessages,
  addMessage,
  updateMessage,
  setTyping,
  clearChatData,
  clearErrors,
  addVirtualConversation
} = chatSlice.actions;

// Selectors
export const selectConversations = (state) => state.chat.conversations;
export const selectConversationsLoading = (state) => state.chat.conversationsLoading;
export const selectCurrentConversation = (state) => state.chat.currentConversation;
export const selectMessages = (state, userId) => state.chat.messages[userId] || [];
export const selectMessagesLoading = (state) => state.chat.messagesLoading;
export const selectSendingMessage = (state) => state.chat.sendingMessage;
export const selectChatStats = (state) => state.chat.stats;
export const selectUnreadCount = (state) => state.chat.stats.unreadMessagesCount;
export const selectSelectedConversationId = (state) => state.chat.selectedConversationId;
// export const selectTypingUsers = (state) => state.chat.typingUsers; // Temporarily disabled

export default chatSlice.reducer;
