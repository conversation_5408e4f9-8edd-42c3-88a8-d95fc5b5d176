import { useState, useEffect } from "react";
import { useThemeProvider } from "../../../providers/ThemeContext";

function ExamQuestion({ question, answer, onAnswerChange, questionNumber, totalQuestions }) {
  const { themeText, cardBg } = useThemeProvider();
  const [selectedAnswer, setSelectedAnswer] = useState(answer || '');

  useEffect(() => {
    setSelectedAnswer(answer || '');
  }, [answer]);

  const handleAnswerChange = (value) => {
    setSelectedAnswer(value);
    onAnswerChange(question.id, value);
  };

  const renderQuestionContent = () => {
    switch (question.question_type) {
      case 'multiple_choice':
        return (
          <div className="space-y-3">
            {question.options && Object.entries(question.options).map(([key, option]) => (
              <label
                key={key}
                className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedAnswer === key
                    ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-violet-300'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={key}
                  checked={selectedAnswer === key}
                  onChange={(e) => handleAnswerChange(e.target.value)}
                  className="mr-3 text-violet-600"
                />
                <span className={themeText}>{option}</span>
              </label>
            ))}
          </div>
        );

      case 'true_false':
        return (
          <div className="space-y-3">
            {['true', 'false'].map((option) => (
              <label
                key={option}
                className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedAnswer === option
                    ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-violet-300'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={selectedAnswer === option}
                  onChange={(e) => handleAnswerChange(e.target.value)}
                  className="mr-3 text-violet-600"
                />
                <span className={themeText}>{option === 'true' ? 'True' : 'False'}</span>
              </label>
            ))}
          </div>
        );

      case 'short_answer':
      case 'essay':
        return (
          <textarea
            value={selectedAnswer}
            onChange={(e) => handleAnswerChange(e.target.value)}
            placeholder="Enter your answer here..."
            className={`w-full p-3 border rounded-lg ${cardBg} ${themeText} border-gray-200 dark:border-gray-700 focus:border-violet-500 focus:ring-1 focus:ring-violet-500`}
            rows={question.question_type === 'essay' ? 6 : 3}
          />
        );

      default:
        return (
          <div className="text-red-500">
            Unsupported question type: {question.question_type}
          </div>
        );
    }
  };

  return (
    <div className={`${cardBg} rounded-xl p-6 shadow-lg`}>
      {/* Question Header */}
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium text-violet-600">
          Question {questionNumber} of {totalQuestions}
        </span>
        <span className="text-sm text-gray-500">
          {question.marks} {question.marks === 1 ? 'mark' : 'marks'}
        </span>
      </div>

      {/* Question Text */}
      <div className={`text-lg font-medium mb-6 ${themeText}`}>
        {question.question_text}
      </div>

      {/* Answer Options */}
      {renderQuestionContent()}
    </div>
  );
}

export default ExamQuestion;
