import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiCreditCard, 
  FiCalendar, 
  FiDollarSign, 
  FiCheck, 
  FiX, 
  FiClock,
  FiRefreshCw,
  FiDownload,
  <PERSON><PERSON>ye,
  FiFilter
} from 'react-icons/fi';
import {
  getPaymentHistory,
  getPaymentStatus,
  selectPaymentHistory,
  selectHistoryLoading,
  selectHistoryError,
  selectStatusLoading,
  selectStatusError
} from '../../store/slices/PaymentSlice';
import { usePaymentAccess } from '../../hooks/usePaymentAccess';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { useNotification } from '../../contexts/NotificationContext';

const UserPaymentHistory = () => {
  const dispatch = useDispatch();
  const { showSuccess, showError } = useNotification();
  
  // Redux state
  const paymentHistory = useSelector(selectPaymentHistory);
  const historyLoading = useSelector(selectHistoryLoading);
  const historyError = useSelector(selectHistoryError);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);
  
  // Payment access control
  const { currentUser, userRole, canPurchase } = usePaymentAccess();
  
  // Local state
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all',
    eventType: 'all'
  });
  const [selectedPayment, setSelectedPayment] = useState(null);

  // Load payment history on component mount
  useEffect(() => {
    if (currentUser && canPurchase) {
      loadPaymentHistory();
    }
  }, [currentUser, canPurchase]);

  const loadPaymentHistory = async () => {
    setError(null);

    try {
      await dispatch(getPaymentHistory()).unwrap();
    } catch (err) {
      setError(err.message || 'Failed to load payment history');
    }
  };

  const handleRefreshStatus = async (paymentId) => {
    try {
      await dispatch(getPaymentStatus(paymentId)).unwrap();
      showSuccess('Payment status updated');
    } catch (err) {
      showError(`Failed to refresh status: ${err.message}`);
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'complete':
      case 'completed':
      case 'success':
        return <FiCheck className="h-5 w-5 text-green-500" />;
      case 'failed':
      case 'cancelled':
        return <FiX className="h-5 w-5 text-red-500" />;
      case 'pending':
      case 'processing':
        return <FiClock className="h-5 w-5 text-yellow-500" />;
      default:
        return <FiCreditCard className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'complete':
      case 'completed':
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount, currency = 'PKR') => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Filter payments based on selected filters
  const filteredPayments = paymentHistory.filter(payment => {
    if (filters.status !== 'all' && payment.status?.toLowerCase() !== filters.status) {
      return false;
    }
    
    if (filters.dateRange !== 'all') {
      const paymentDate = new Date(payment.created_at);
      const now = new Date();
      const daysDiff = (now - paymentDate) / (1000 * 60 * 60 * 24);
      
      switch (filters.dateRange) {
        case 'week':
          if (daysDiff > 7) return false;
          break;
        case 'month':
          if (daysDiff > 30) return false;
          break;
        case 'quarter':
          if (daysDiff > 90) return false;
          break;
      }
    }
    
    return true;
  });

  // Show access denied for users who cannot make payments
  if (!canPurchase) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <FiCreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Payment History Not Available</h2>
          <p className="text-gray-600 mb-4">
            {userRole === 'admin' 
              ? 'Administrators do not have payment history as they have access to all events.'
              : `${userRole} users do not have access to payment features.`
            }
          </p>
        </div>
      </div>
    );
  }

  if (historyLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading payment history...</p>
        </div>
      </div>
    );
  }

  if (error || historyError) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <ErrorMessage
          message={error || historyError}
          onRetry={loadPaymentHistory}
        />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Payment History</h1>
        <p className="text-gray-600 mt-2">
          View and manage your event ticket purchases
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <FiFilter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="complete">Completed</option>
            <option value="pending">Pending</option>
            <option value="failed">Failed</option>
          </select>

          <select
            value={filters.dateRange}
            onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Time</option>
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last 3 Months</option>
          </select>

          <button
            onClick={loadPaymentHistory}
            disabled={historyLoading}
            className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            <FiRefreshCw className={`h-4 w-4 mr-1 ${historyLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Payment List */}
      {filteredPayments.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <FiCreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Payments Found</h3>
          <p className="text-gray-600">
            {paymentHistory.length === 0 
              ? "You haven't made any payments yet."
              : "No payments match your current filters."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredPayments.map((payment) => (
            <div key={payment.payment_id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(payment.status)}
                  <div>
                    <h3 className="font-semibold text-gray-900">{payment.event_title || 'Event Payment'}</h3>
                    <p className="text-sm text-gray-600">Payment ID: {payment.payment_id}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900">
                    {formatCurrency(payment.amount, payment.currency)}
                  </div>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                    {payment.status}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <FiCalendar className="h-4 w-4 mr-2" />
                  {formatDate(payment.created_at)}
                </div>
                
                {payment.ticket_type && (
                  <div className="flex items-center">
                    <FiCreditCard className="h-4 w-4 mr-2" />
                    {payment.ticket_type}
                  </div>
                )}
                
                {payment.quantity && (
                  <div className="flex items-center">
                    <span className="text-sm">Qty: {payment.quantity}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleRefreshStatus(payment.payment_id)}
                    disabled={statusLoading}
                    className="inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    <FiRefreshCw className={`h-4 w-4 mr-1 ${statusLoading ? 'animate-spin' : ''}`} />
                    Refresh Status
                  </button>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setSelectedPayment(payment)}
                    className="inline-flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 font-medium"
                  >
                    <FiEye className="h-4 w-4 mr-1" />
                    View Details
                  </button>
                  
                  {payment.status?.toLowerCase() === 'complete' && (
                    <button className="inline-flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 font-medium">
                      <FiDownload className="h-4 w-4 mr-1" />
                      Receipt
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserPaymentHistory;
