/**
 * Exam Session Data Redux Slice
 * Handles fetching exam data using session_id for security
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import examSessionDataService from '../../../services/exam/session/ExamSessionDataService';

// Async thunks for session-based exam data operations

// Get exam data using session_id
export const getExamDataBySession = createAsyncThunk(
  'examSessionData/getExamData',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('🔐 Redux: Fetching exam data by session:', sessionId);
      const examData = await examSessionDataService.getExamDataBySession(sessionId);
      return { sessionId, examData };
    } catch (error) {
      console.error('❌ Redux: Failed to fetch exam data by session:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Get session status
export const getSessionStatus = createAsyncThunk(
  'examSessionData/getSessionStatus',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('📊 Redux: Fetching session status:', sessionId);
      const statusData = await examSessionDataService.getSessionStatus(sessionId);
      return { sessionId, statusData };
    } catch (error) {
      console.error('❌ Redux: Failed to fetch session status:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Get session with full exam data
export const getSessionWithExamData = createAsyncThunk(
  'examSessionData/getSessionWithExamData',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('🔄 Redux: Fetching session with exam data:', sessionId);
      const fullData = await examSessionDataService.getSessionWithExamData(sessionId);
      return { sessionId, fullData };
    } catch (error) {
      console.error('❌ Redux: Failed to fetch session with exam data:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Validate session access
export const validateSessionAccess = createAsyncThunk(
  'examSessionData/validateAccess',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('🔐 Redux: Validating session access:', sessionId);
      const validationResult = await examSessionDataService.validateSessionAccess(sessionId);
      return { sessionId, validationResult };
    } catch (error) {
      console.error('❌ Redux: Session validation failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Get session answers
export const getSessionAnswers = createAsyncThunk(
  'examSessionData/getSessionAnswers',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('📝 Redux: Fetching session answers:', sessionId);
      const answersData = await examSessionDataService.getSessionAnswers(sessionId);
      return { sessionId, answersData };
    } catch (error) {
      console.error('❌ Redux: Failed to fetch session answers:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Save session answer
export const saveSessionAnswer = createAsyncThunk(
  'examSessionData/saveAnswer',
  async ({ sessionId, questionId, answer }, { rejectWithValue }) => {
    try {
      console.log('💾 Redux: Saving session answer:', { sessionId, questionId });
      const saveResult = await examSessionDataService.saveSessionAnswer(sessionId, questionId, answer);
      return { sessionId, questionId, answer, saveResult };
    } catch (error) {
      console.error('❌ Redux: Failed to save session answer:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Initial state
const initialState = {
  // Session data
  currentSessionId: null,
  examData: null,
  sessionStatus: null,
  sessionAnswers: {},
  validationResult: null,
  
  // Loading states
  loading: false,
  loadingStatus: false,
  loadingAnswers: false,
  savingAnswer: false,
  validating: false,
  
  // Error handling
  error: null,
  statusError: null,
  answersError: null,
  saveError: null,
  validationError: null,
  
  // Cache info
  lastFetched: null,
  dataSource: null, // 'session' or 'direct'
};

// Create slice
const examSessionDataSlice = createSlice({
  name: 'examSessionData',
  initialState,
  reducers: {
    // Clear all data
    clearSessionData: (state) => {
      state.currentSessionId = null;
      state.examData = null;
      state.sessionStatus = null;
      state.sessionAnswers = {};
      state.validationResult = null;
      state.error = null;
      state.statusError = null;
      state.answersError = null;
      state.saveError = null;
      state.validationError = null;
      state.lastFetched = null;
      state.dataSource = null;
    },
    
    // Clear errors
    clearErrors: (state) => {
      state.error = null;
      state.statusError = null;
      state.answersError = null;
      state.saveError = null;
      state.validationError = null;
    },
    
    // Set current session ID
    setCurrentSessionId: (state, action) => {
      state.currentSessionId = action.payload;
    },
    
    // Update local answer (optimistic update)
    updateLocalAnswer: (state, action) => {
      const { questionId, answer } = action.payload;
      state.sessionAnswers[questionId] = answer;
    },
    
    // Set data source
    setDataSource: (state, action) => {
      state.dataSource = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get exam data by session
      .addCase(getExamDataBySession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getExamDataBySession.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSessionId = action.payload.sessionId;
        state.examData = action.payload.examData;
        state.lastFetched = Date.now();
        state.dataSource = 'session';
        state.error = null;
      })
      .addCase(getExamDataBySession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get session status
      .addCase(getSessionStatus.pending, (state) => {
        state.loadingStatus = true;
        state.statusError = null;
      })
      .addCase(getSessionStatus.fulfilled, (state, action) => {
        state.loadingStatus = false;
        state.sessionStatus = action.payload.statusData;
        state.statusError = null;
      })
      .addCase(getSessionStatus.rejected, (state, action) => {
        state.loadingStatus = false;
        state.statusError = action.payload;
      })
      
      // Get session with exam data
      .addCase(getSessionWithExamData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSessionWithExamData.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSessionId = action.payload.sessionId;
        const { exam_data, session_status, answers } = action.payload.fullData;
        state.examData = exam_data;
        state.sessionStatus = session_status;
        state.sessionAnswers = answers || {};
        state.lastFetched = Date.now();
        state.dataSource = 'session';
        state.error = null;
      })
      .addCase(getSessionWithExamData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Validate session access
      .addCase(validateSessionAccess.pending, (state) => {
        state.validating = true;
        state.validationError = null;
      })
      .addCase(validateSessionAccess.fulfilled, (state, action) => {
        state.validating = false;
        state.validationResult = action.payload.validationResult;
        state.validationError = null;
      })
      .addCase(validateSessionAccess.rejected, (state, action) => {
        state.validating = false;
        state.validationError = action.payload;
      })
      
      // Get session answers
      .addCase(getSessionAnswers.pending, (state) => {
        state.loadingAnswers = true;
        state.answersError = null;
      })
      .addCase(getSessionAnswers.fulfilled, (state, action) => {
        state.loadingAnswers = false;
        state.sessionAnswers = action.payload.answersData;
        state.answersError = null;
      })
      .addCase(getSessionAnswers.rejected, (state, action) => {
        state.loadingAnswers = false;
        state.answersError = action.payload;
      })
      
      // Save session answer
      .addCase(saveSessionAnswer.pending, (state) => {
        state.savingAnswer = true;
        state.saveError = null;
      })
      .addCase(saveSessionAnswer.fulfilled, (state, action) => {
        state.savingAnswer = false;
        const { questionId, answer } = action.payload;
        state.sessionAnswers[questionId] = answer;
        state.saveError = null;
      })
      .addCase(saveSessionAnswer.rejected, (state, action) => {
        state.savingAnswer = false;
        state.saveError = action.payload;
      });
  }
});

// Export actions
export const {
  clearSessionData,
  clearErrors,
  setCurrentSessionId,
  updateLocalAnswer,
  setDataSource
} = examSessionDataSlice.actions;

// Selectors
export const selectExamSessionData = (state) => state.examSessionData;
export const selectCurrentSessionId = (state) => state.examSessionData.currentSessionId;
export const selectExamData = (state) => state.examSessionData.examData;
export const selectSessionStatus = (state) => state.examSessionData.sessionStatus;
export const selectSessionAnswers = (state) => state.examSessionData.sessionAnswers;
export const selectValidationResult = (state) => state.examSessionData.validationResult;
export const selectDataSource = (state) => state.examSessionData.dataSource;
export const selectIsLoading = (state) => state.examSessionData.loading;
export const selectErrors = (state) => ({
  error: state.examSessionData.error,
  statusError: state.examSessionData.statusError,
  answersError: state.examSessionData.answersError,
  saveError: state.examSessionData.saveError,
  validationError: state.examSessionData.validationError
});

export default examSessionDataSlice.reducer;
