/**
 * New Event Registration Component
 * 
 * Redesigned from scratch to use the new event API structure.
 * Handles both free event registration and paid ticket purchasing.
 * 
 * Features:
 * - Clean separation between free and paid events
 * - Proper API integration with new event service
 * - PayFast payment integration
 * - Real-time status updates
 * - Comprehensive error handling
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiX,
  FiCalendar,
  FiMapPin,
  FiDollarSign,
  FiUser,
  FiMail,
  FiPhone,
  FiInfo,
  FiCreditCard,
  FiCheck,
  FiClock,
  FiUsers,
  FiShield,
  FiAlertCircle,
  FiMinus,
  FiPlus
} from 'react-icons/fi';
import { format } from 'date-fns';
import newEventService from '../../services/newEventService';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../ui';

const NewEventRegistration = ({
  event,
  isOpen,
  onClose,
  onSuccess,
  selectedTicket = null
}) => {
  const navigate = useNavigate();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTicketId, setSelectedTicketId] = useState(selectedTicket?.id || null);
  const [quantity, setQuantity] = useState(1);
  const [attendeeInfo, setAttendeeInfo] = useState({
    name: '',
    email: '',
    phone: '',
    dietary_requirements: '',
    special_needs: '',
    emergency_contact: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [registrationResult, setRegistrationResult] = useState(null);

  // Hooks
  const { showSuccess, showError, showInfo } = useNotification();

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setSelectedTicketId(selectedTicket?.id || null);
      setQuantity(1);
      setError(null);
      setRegistrationResult(null);
      
      // Pre-fill user info if available
      const user = newEventService.getUserInfo();
      if (user) {
        setAttendeeInfo(prev => ({
          ...prev,
          name: user.username || user.name || '',
          email: user.email || ''
        }));
      }
    }
  }, [isOpen, selectedTicket]);

  // Get selected ticket details
  const getSelectedTicket = () => {
    return event?.tickets?.find(ticket => ticket.id === selectedTicketId);
  };

  // Calculate total amount
  const calculateTotal = () => {
    const ticket = getSelectedTicket();
    return ticket ? (ticket.price * quantity) : 0;
  };

  // Check if event has free tickets
  const isFreeTicket = () => {
    const ticket = getSelectedTicket();
    return ticket && ticket.price === 0;
  };

  // Validate form data
  const validateForm = () => {
    if (!selectedTicketId) {
      setError('Please select a ticket');
      return false;
    }

    if (!attendeeInfo.name.trim()) {
      setError('Name is required');
      return false;
    }

    if (!attendeeInfo.email.trim()) {
      setError('Email is required');
      return false;
    }

    if (!/\S+@\S+\.\S+/.test(attendeeInfo.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  // Handle ticket selection
  const handleTicketSelect = (ticketId) => {
    setSelectedTicketId(ticketId);
    setQuantity(1);
    setError(null);
  };

  // Handle quantity change
  const handleQuantityChange = (newQuantity) => {
    const ticket = getSelectedTicket();
    if (ticket && newQuantity <= ticket.max_per_user && newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  // Handle attendee info change
  const handleAttendeeInfoChange = (field, value) => {
    setAttendeeInfo(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  // Handle next step
  const handleNextStep = () => {
    if (currentStep === 1 && !selectedTicketId) {
      setError('Please select a ticket');
      return;
    }

    if (currentStep === 2 && !validateForm()) {
      return;
    }

    setCurrentStep(prev => prev + 1);
    setError(null);
  };

  // Handle previous step
  const handlePreviousStep = () => {
    setCurrentStep(prev => prev - 1);
    setError(null);
  };

  // Handle registration (both free and paid tickets create registration record)
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsProcessing(true);
    setError(null);

    try {
      newEventService.logEventAction('registration_attempt', {
        event_id: event.id,
        ticket_id: selectedTicketId,
        quantity: quantity,
        is_free: isFreeTicket()
      });

      const registrationData = {
        ticket_id: selectedTicketId,
        quantity: quantity,
        attendee_info: attendeeInfo
      };

      showInfo('Creating registration...');
      const result = await newEventService.registerForEvent(event.id, registrationData);

      if (result.success) {
        setRegistrationResult(result);

        if (result.status === 'CONFIRMED') {
          // Free ticket - registration is complete
          showSuccess('Registration confirmed! Redirecting to My Events...');

          newEventService.logEventAction('registration_success', {
            event_id: event.id,
            registration_id: result.registration_id,
            type: 'free'
          });

          // Redirect to My Events after a short delay
          setTimeout(() => {
            navigate('/student/events/my');
          }, 2000);
        } else if (result.status === 'PENDING_PAYMENT') {
          // Paid ticket - registration created, payment needed later
          showSuccess('Registration created! Redirecting to My Events...');

          newEventService.logEventAction('registration_created', {
            event_id: event.id,
            registration_id: result.registration_id,
            type: 'paid',
            amount: result.ticket.total_amount
          });

          // Redirect to My Events after a short delay
          setTimeout(() => {
            navigate('/student/events/my');
          }, 2000);
        }

        if (onSuccess) onSuccess(result);
      }

    } catch (error) {
      console.error('Registration failed:', error);
      setError(error.message || 'Registration failed. Please try again.');
      showError(error.message || 'Registration failed');

      newEventService.logEventAction('registration_error', {
        event_id: event.id,
        error: error.message
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'EEEE, MMMM do, yyyy \'at\' h:mm a');
    } catch (error) {
      return dateString;
    }
  };

  // Don't render if not open
  if (!isOpen || !event) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Register for Event
            </h2>
            <p className="text-sm text-gray-600 mt-1">{event.title}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isProcessing}
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep >= step 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                  }
                `}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={`
                    w-16 h-1 mx-2
                    ${currentStep > step ? 'bg-blue-600' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-xs text-gray-600">
            <span>Select Ticket</span>
            <span>Your Details</span>
            <span>Confirm</span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <FiAlertCircle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-800 font-medium">Registration Error</p>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          )}

          {/* Step 1: Ticket Selection */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Your Ticket</h3>
                
                {/* Event Info */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="flex items-start space-x-4">
                    {event.banner_image_url && (
                      <img
                        src={event.banner_image_url}
                        alt={event.title}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{event.title}</h4>
                      <div className="flex items-center text-sm text-gray-600 mt-2 space-x-4">
                        <div className="flex items-center">
                          <FiCalendar className="w-4 h-4 mr-1" />
                          {formatDate(event.start_datetime)}
                        </div>
                        <div className="flex items-center">
                          <FiMapPin className="w-4 h-4 mr-1" />
                          {event.location}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Ticket Options */}
                <div className="space-y-3">
                  {event.tickets?.map((ticket) => (
                    <div
                      key={ticket.id}
                      className={`
                        border rounded-lg p-4 cursor-pointer transition-all
                        ${selectedTicketId === ticket.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        ${ticket.status !== 'ACTIVE' ? 'opacity-50 cursor-not-allowed' : ''}
                      `}
                      onClick={() => ticket.status === 'ACTIVE' && handleTicketSelect(ticket.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <div className={`
                              w-4 h-4 rounded-full border-2 mr-3
                              ${selectedTicketId === ticket.id
                                ? 'border-blue-500 bg-blue-500'
                                : 'border-gray-300'
                              }
                            `}>
                              {selectedTicketId === ticket.id && (
                                <FiCheck className="w-2 h-2 text-white m-0.5" />
                              )}
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">{ticket.name}</h5>
                              <p className="text-sm text-gray-600">{ticket.description}</p>
                            </div>
                          </div>
                          
                          {/* Ticket Features */}
                          {ticket.features && ticket.features.length > 0 && (
                            <div className="mt-2 ml-7">
                              <ul className="text-xs text-gray-600 space-y-1">
                                {ticket.features.map((feature, index) => (
                                  <li key={index} className="flex items-center">
                                    <FiCheck className="w-3 h-3 text-green-500 mr-1" />
                                    {feature}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                        
                        <div className="text-right ml-4">
                          <div className="flex flex-col items-end">
                            <div className="flex items-center text-lg font-bold text-gray-900">
                              {ticket.price === 0 ? (
                                <span className="text-green-600">FREE</span>
                              ) : (
                                <>
                                  <FiDollarSign className="w-4 h-4" />
                                  {ticket.price.toFixed(2)} {ticket.currency}
                                </>
                              )}
                            </div>
                            {ticket.price > 0 && (
                              <div className="text-xs text-orange-600 mt-1">
                                Pay later from registrations
                              </div>
                            )}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {ticket.available_quantity} available
                          </div>
                          {ticket.status !== 'ACTIVE' && (
                            <div className="text-xs text-red-500 mt-1">
                              {ticket.status === 'SOLD_OUT' ? 'Sold Out' : 'Unavailable'}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Quantity Selection */}
                {selectedTicketId && (
                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity
                    </label>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={quantity <= 1}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FiMinus className="w-4 h-4" />
                      </button>
                      <span className="text-lg font-medium w-8 text-center">{quantity}</span>
                      <button
                        onClick={() => handleQuantityChange(quantity + 1)}
                        disabled={quantity >= getSelectedTicket()?.max_per_user}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <FiPlus className="w-4 h-4" />
                      </button>
                      <span className="text-sm text-gray-600 ml-2">
                        (Max {getSelectedTicket()?.max_per_user} per person)
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Attendee Information */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <div className="relative">
                      <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        value={attendeeInfo.name}
                        onChange={(e) => handleAttendeeInfoChange('name', e.target.value)}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <div className="relative">
                      <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="email"
                        value={attendeeInfo.email}
                        onChange={(e) => handleAttendeeInfoChange('email', e.target.value)}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your email"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <div className="relative">
                      <FiPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="tel"
                        value={attendeeInfo.phone}
                        onChange={(e) => handleAttendeeInfoChange('phone', e.target.value)}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="+27 123 456 789"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Emergency Contact
                    </label>
                    <div className="relative">
                      <FiPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="tel"
                        value={attendeeInfo.emergency_contact}
                        onChange={(e) => handleAttendeeInfoChange('emergency_contact', e.target.value)}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="+27 987 654 321"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dietary Requirements
                  </label>
                  <textarea
                    value={attendeeInfo.dietary_requirements}
                    onChange={(e) => handleAttendeeInfoChange('dietary_requirements', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows="2"
                    placeholder="Any dietary restrictions or preferences..."
                  />
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Needs / Accessibility Requirements
                  </label>
                  <textarea
                    value={attendeeInfo.special_needs}
                    onChange={(e) => handleAttendeeInfoChange('special_needs', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows="2"
                    placeholder="Any accessibility needs or special requirements..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Your Registration</h3>
                
                {/* Registration Summary */}
                <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                  {/* Event Details */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Event Details</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2" />
                        {formatDate(event.start_datetime)}
                      </div>
                      <div className="flex items-center">
                        <FiMapPin className="w-4 h-4 mr-2" />
                        {event.location}
                      </div>
                      <div className="flex items-center">
                        <FiUsers className="w-4 h-4 mr-2" />
                        {event.current_registrations || 0} / {event.max_attendees} registered
                      </div>
                    </div>
                  </div>

                  {/* Ticket Details */}
                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Ticket Details</h4>
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{getSelectedTicket()?.name}</p>
                        <p className="text-sm text-gray-600">Quantity: {quantity}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">
                          {isFreeTicket() ? (
                            <span className="text-green-600">FREE</span>
                          ) : (
                            <>
                              <span>R {calculateTotal().toFixed(2)}</span>
                              <div className="text-xs text-orange-600 mt-1">
                                Payment required later
                              </div>
                            </>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Attendee Details */}
                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Attendee Information</h4>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p><strong>Name:</strong> {attendeeInfo.name}</p>
                      <p><strong>Email:</strong> {attendeeInfo.email}</p>
                      {attendeeInfo.phone && <p><strong>Phone:</strong> {attendeeInfo.phone}</p>}
                    </div>
                  </div>

                  {/* Payment Info */}
                  {!isFreeTicket() && (
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex items-center text-sm text-orange-600">
                        <FiCreditCard className="w-4 h-4 mr-2" />
                        Registration will be created. Complete payment later from your registrations page.
                      </div>
                    </div>
                  )}
                </div>

                {/* Terms and Conditions */}
                <div className="mt-6">
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="terms"
                      className="mt-1 mr-3"
                      required
                    />
                    <label htmlFor="terms" className="text-sm text-gray-600">
                      I agree to the event terms and conditions and understand the cancellation policy.
                      {!isFreeTicket() && ' I understand that payment is required to confirm my attendance.'}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Success Result */}
          {registrationResult && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-start">
                <FiCheck className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-green-800 font-medium">
                    {isFreeEvent() ? 'Registration Confirmed!' : 'Payment Link Created!'}
                  </p>
                  <p className="text-green-700 text-sm mt-1">
                    {registrationResult.message}
                  </p>
                  {registrationResult.registration_number && (
                    <p className="text-green-700 text-sm mt-1">
                      Registration Number: <strong>{registrationResult.registration_number}</strong>
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={handlePreviousStep}
                disabled={isProcessing}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isProcessing}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            
            {currentStep < 3 ? (
              <button
                onClick={handleNextStep}
                disabled={isProcessing || (currentStep === 1 && !selectedTicketId)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isProcessing || !attendeeInfo.name || !attendeeInfo.email}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isProcessing ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Creating Registration...
                  </>
                ) : (
                  <>
                    <FiCheck className="w-4 h-4 mr-2" />
                    Create Registration
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewEventRegistration;
