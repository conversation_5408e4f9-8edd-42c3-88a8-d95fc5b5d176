/**
 * Payment Validation Utilities
 * 
 * Validation functions for payment forms, user data,
 * and PayFast-specific requirements.
 */

import logger from '../helpers/logger';

/**
 * Validate user payment information
 * @param {Object} userData - User data for payment
 * @returns {Object} Validation result
 */
export const validateUserPaymentData = (userData) => {
  const errors = {};
  const warnings = [];

  // Email validation
  if (!userData.email || typeof userData.email !== 'string') {
    errors.email = 'Email is required';
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email.trim())) {
      errors.email = 'Please enter a valid email address';
    }
  }

  // Name validation
  if (!userData.name || typeof userData.name !== 'string') {
    errors.name = 'Full name is required';
  } else {
    const trimmedName = userData.name.trim();
    if (trimmedName.length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    }
    if (trimmedName.length > 100) {
      errors.name = 'Name must be less than 100 characters';
    }
    if (!/^[a-zA-Z\s\-'\.]+$/.test(trimmedName)) {
      errors.name = 'Name can only contain letters, spaces, hyphens, apostrophes, and periods';
    }
  }

  // Phone validation (optional)
  if (userData.phone && typeof userData.phone === 'string') {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = userData.phone.replace(/[\s\-\(\)]/g, '');
    if (!phoneRegex.test(cleanPhone)) {
      errors.phone = 'Please enter a valid phone number';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};

/**
 * Validate event registration data
 * @param {Object} registrationData - Registration data
 * @param {Object} event - Event information
 * @returns {Object} Validation result
 */
export const validateEventRegistrationData = (registrationData, event) => {
  const errors = {};
  const warnings = [];

  // Check if registration is open
  if (event.registration_deadline) {
    const deadline = new Date(event.registration_deadline);
    const now = new Date();
    if (now > deadline) {
      errors.registration = 'Registration deadline has passed';
    }
  }

  // Check if event is full
  if (event.max_participants && event.current_participants) {
    if (event.current_participants >= event.max_participants) {
      errors.capacity = 'Event is at full capacity';
    } else if (event.current_participants / event.max_participants > 0.9) {
      warnings.push('Event is nearly full - register soon to secure your spot');
    }
  }

  // Validate additional info length
  if (registrationData.additional_info && registrationData.additional_info.length > 500) {
    errors.additional_info = 'Additional information must be less than 500 characters';
  }

  // Validate emergency contact if provided
  if (registrationData.emergency_contact) {
    const contact = registrationData.emergency_contact;
    if (contact.name && !contact.phone) {
      errors.emergency_phone = 'Phone number is required when emergency contact name is provided';
    }
    if (contact.phone && !contact.name) {
      errors.emergency_name = 'Name is required when emergency contact phone is provided';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};

/**
 * Validate payment form data before submission
 * @param {Object} paymentData - Payment form data
 * @returns {Object} Validation result
 */
export const validatePaymentFormData = (paymentData) => {
  const errors = {};
  const warnings = [];

  // Amount validation
  if (!paymentData.amount || typeof paymentData.amount !== 'number') {
    errors.amount = 'Payment amount is required';
  } else {
    if (paymentData.amount <= 0) {
      errors.amount = 'Amount must be greater than zero';
    }
    if (paymentData.amount < 5) {
      errors.amount = 'Minimum payment amount is PKR 5.00';
    }
    if (paymentData.amount > 1000000) {
      errors.amount = 'Maximum payment amount is PKR 1,000,000.00';
    }
  }

  // Currency validation
  if (!paymentData.currency) {
    errors.currency = 'Currency is required';
  } else if (!['PKR', 'USD', 'EUR', 'GBP'].includes(paymentData.currency)) {
    errors.currency = 'Unsupported currency';
  }

  // User data validation
  const userValidation = validateUserPaymentData({
    email: paymentData.user_email,
    name: paymentData.user_name,
    phone: paymentData.user_phone
  });

  if (!userValidation.isValid) {
    Object.assign(errors, userValidation.errors);
  }

  // URL validation
  const urlFields = ['return_url', 'cancel_url'];
  urlFields.forEach(field => {
    if (paymentData[field]) {
      try {
        const url = new URL(paymentData[field]);
        if (!['http:', 'https:'].includes(url.protocol)) {
          errors[field] = `${field} must use HTTP or HTTPS protocol`;
        }
      } catch {
        errors[field] = `Invalid ${field} format`;
      }
    }
  });

  // Registration ID validation (for event payments)
  if (paymentData.registration_id) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(paymentData.registration_id)) {
      errors.registration_id = 'Invalid registration ID format';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};

/**
 * Validate PayFast merchant configuration
 * @param {Object} config - PayFast configuration
 * @returns {Object} Validation result
 */
export const validatePayFastConfig = (config) => {
  const errors = {};
  const warnings = [];

  // Required fields
  const requiredFields = ['merchant_id', 'merchant_key', 'passphrase'];
  requiredFields.forEach(field => {
    if (!config[field] || typeof config[field] !== 'string' || config[field].trim() === '') {
      errors[field] = `${field} is required`;
    }
  });

  // Merchant ID format
  if (config.merchant_id) {
    if (!/^\d+$/.test(config.merchant_id)) {
      errors.merchant_id = 'Merchant ID must contain only numbers';
    }
  }

  // Merchant key format
  if (config.merchant_key) {
    if (config.merchant_key.length < 10) {
      errors.merchant_key = 'Merchant key appears to be too short';
    }
  }

  // Environment validation
  if (config.environment && !['sandbox', 'production'].includes(config.environment)) {
    errors.environment = 'Environment must be either "sandbox" or "production"';
  }

  // Sandbox warning
  if (config.environment === 'sandbox') {
    warnings.push('Using PayFast sandbox environment - payments will not be processed');
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};

/**
 * Validate payment status response
 * @param {Object} statusResponse - Payment status response
 * @returns {Object} Validation result
 */
export const validatePaymentStatusResponse = (statusResponse) => {
  const errors = {};
  const warnings = [];

  // Required fields
  const requiredFields = ['payment_id', 'status', 'amount', 'currency'];
  requiredFields.forEach(field => {
    if (!statusResponse[field]) {
      errors[field] = `${field} is missing from status response`;
    }
  });

  // Status validation
  const validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'refunded'];
  if (statusResponse.status && !validStatuses.includes(statusResponse.status)) {
    errors.status = 'Invalid payment status';
  }

  // Amount validation
  if (statusResponse.amount) {
    const amount = parseFloat(statusResponse.amount);
    if (isNaN(amount) || amount <= 0) {
      errors.amount = 'Invalid amount in status response';
    }
  }

  // Timestamp validation
  const timestampFields = ['created_at', 'updated_at', 'processed_at'];
  timestampFields.forEach(field => {
    if (statusResponse[field]) {
      const date = new Date(statusResponse[field]);
      if (isNaN(date.getTime())) {
        errors[field] = `Invalid ${field} timestamp`;
      }
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};

/**
 * Sanitize user input for payment processing
 * @param {Object} input - User input data
 * @returns {Object} Sanitized data
 */
export const sanitizePaymentInput = (input) => {
  const sanitized = {};

  // Sanitize string fields
  const stringFields = ['user_name', 'user_email', 'additional_info', 'item_description'];
  stringFields.forEach(field => {
    if (input[field] && typeof input[field] === 'string') {
      sanitized[field] = input[field]
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .substring(0, 255); // Limit length
    }
  });

  // Sanitize numeric fields
  const numericFields = ['amount'];
  numericFields.forEach(field => {
    if (input[field] !== undefined) {
      const num = parseFloat(input[field]);
      if (!isNaN(num) && num >= 0) {
        sanitized[field] = Math.round(num * 100) / 100; // Round to 2 decimal places
      }
    }
  });

  // Sanitize email
  if (input.user_email && typeof input.user_email === 'string') {
    sanitized.user_email = input.user_email.toLowerCase().trim();
  }

  // Copy other safe fields
  const safeFields = ['currency', 'registration_id', 'subscription_id', 'return_url', 'cancel_url'];
  safeFields.forEach(field => {
    if (input[field]) {
      sanitized[field] = input[field];
    }
  });

  return sanitized;
};

/**
 * Check if payment can be processed
 * @param {Object} paymentData - Payment data
 * @param {Object} userContext - User context
 * @returns {Object} Check result
 */
export const canProcessPayment = (paymentData, userContext = {}) => {
  const errors = [];
  const warnings = [];

  // Check user authentication
  if (!userContext.isAuthenticated) {
    errors.push('User must be authenticated to process payments');
  }

  // Check user verification status
  if (userContext.isAuthenticated && !userContext.isEmailVerified) {
    errors.push('Email verification required before processing payments');
  }

  // Check payment data validity
  const paymentValidation = validatePaymentFormData(paymentData);
  if (!paymentValidation.isValid) {
    errors.push(...Object.values(paymentValidation.errors));
  }

  // Check for duplicate payment attempts
  if (userContext.recentPaymentAttempts && userContext.recentPaymentAttempts > 3) {
    warnings.push('Multiple recent payment attempts detected');
  }

  // Check account status
  if (userContext.accountStatus === 'suspended') {
    errors.push('Account is suspended - cannot process payments');
  }

  return {
    canProcess: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate refund request
 * @param {Object} refundData - Refund request data
 * @param {Object} originalPayment - Original payment data
 * @returns {Object} Validation result
 */
export const validateRefundRequest = (refundData, originalPayment) => {
  const errors = {};
  const warnings = [];

  // Check if refund is allowed
  if (originalPayment.status !== 'completed') {
    errors.payment_status = 'Can only refund completed payments';
  }

  // Check refund amount
  if (!refundData.refund_amount || typeof refundData.refund_amount !== 'number') {
    errors.refund_amount = 'Refund amount is required';
  } else {
    if (refundData.refund_amount <= 0) {
      errors.refund_amount = 'Refund amount must be greater than zero';
    }
    if (refundData.refund_amount > originalPayment.amount) {
      errors.refund_amount = 'Refund amount cannot exceed original payment amount';
    }
  }

  // Check refund reason
  if (!refundData.reason || typeof refundData.reason !== 'string') {
    errors.reason = 'Refund reason is required';
  } else if (refundData.reason.trim().length < 10) {
    errors.reason = 'Refund reason must be at least 10 characters';
  }

  // Check time limits (example: 30 days)
  if (originalPayment.processed_at) {
    const paymentDate = new Date(originalPayment.processed_at);
    const daysSincePayment = (Date.now() - paymentDate.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSincePayment > 30) {
      warnings.push('Refund requested more than 30 days after payment');
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
};
