import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { 
  FiCheck, 
  FiCalendar, 
  FiMapPin, 
  FiDownload,
  FiArrowRight,
  FiHome,
  FiGift,
  FiInfo
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import { getCurrentUser } from '../../utils/helpers/authHelpers';

const DemoPaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showSuccess } = useNotification();
  
  // Local state
  const [currentUser, setCurrentUser] = useState(null);
  const [ticketData, setTicketData] = useState(null);

  // Get demo purchase params from URL
  const ticketId = searchParams.get('ticket_id');
  const purchaseId = searchParams.get('purchase_id');
  const confirmationCode = searchParams.get('confirmation_code');

  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);

    // Create demo ticket data
    const demoTicket = {
      id: ticketId || 'demo-ticket',
      name: 'Demo Event Ticket',
      event: {
        title: 'Demo Event Experience',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        location: 'Demo Venue, Demo City'
      },
      purchase_id: purchaseId || `demo-purchase-${Date.now()}`,
      confirmation_code: confirmationCode || `DEMO-${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
      amount: 0,
      currency: 'ZAR',
      demo_mode: true
    };

    setTicketData(demoTicket);
    showSuccess('Demo ticket purchased successfully!');
  }, [ticketId, purchaseId, confirmationCode, showSuccess]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!ticketData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading demo ticket...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
            <FiCheck className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Demo Ticket Purchased!
          </h1>
          <p className="text-lg text-gray-600">
            Your demo event ticket has been confirmed
          </p>
        </div>

        {/* Demo Mode Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <FiInfo className="h-6 w-6 text-yellow-600 mt-0.5 mr-4" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Demo Mode Active</h3>
              <p className="text-yellow-700">
                This is a demonstration purchase. No real payment was processed and this is not a real event ticket.
              </p>
            </div>
          </div>
        </div>

        {/* Ticket Details */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8">
          <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold mb-2">Demo Event Ticket</h2>
                <p className="text-green-100">Confirmation: {ticketData.confirmation_code}</p>
              </div>
              <FiGift className="h-8 w-8" />
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Event Details */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Details</h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <FiCalendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900">{ticketData.event.title}</p>
                      <p className="text-sm text-gray-600">{formatDate(ticketData.event.date)}</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <FiMapPin className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-gray-900">{ticketData.event.location}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Purchase Details */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Purchase Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Ticket Type:</span>
                    <span className="font-medium">{ticketData.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Purchase ID:</span>
                    <span className="font-mono text-sm">{ticketData.purchase_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span className="font-medium text-green-600">FREE (Demo)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Confirmed
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => navigate('/student/events/my')}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
          >
            <FiArrowRight className="h-5 w-5 mr-2" />
            View My Events
          </button>

          <button
            onClick={() => navigate('/student/events')}
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"
          >
            <FiHome className="h-5 w-5 mr-2" />
            Browse More Events
          </button>
        </div>

        {/* Help Section */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            Need help with your demo purchase?
          </p>
          <Link
            to="/support"
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            Contact Demo Support
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DemoPaymentSuccess;
