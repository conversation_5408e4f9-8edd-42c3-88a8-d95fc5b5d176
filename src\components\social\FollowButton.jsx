import React, { useState, useEffect } from 'react';
import { FiUser<PERSON><PERSON>, FiUserMinus, FiLoader } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';

/**
 * FollowButton Component
 * Reusable button for following/unfollowing users
 */
const FollowButton = ({ 
  userId, 
  initialFollowStatus = null,
  onFollowChange = null,
  size = 'medium',
  variant = 'primary',
  showIcon = true,
  className = '',
  disabled = false
}) => {
  const [isFollowing, setIsFollowing] = useState(initialFollowStatus);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Size variants
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-sm',
    large: 'px-6 py-3 text-base'
  };

  // Style variants
  const getVariantClasses = (following) => {
    if (variant === 'outline') {
      return following
        ? 'border-2 border-red-500 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20'
        : 'border-2 border-blue-500 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20';
    }
    
    return following
      ? 'bg-red-500 hover:bg-red-600 text-white'
      : 'bg-blue-500 hover:bg-blue-600 text-white';
  };

  // Load follow status if not provided
  useEffect(() => {
    if (initialFollowStatus === null && userId) {
      loadFollowStatus();
    }
  }, [userId, initialFollowStatus]);

  const loadFollowStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const status = await socialFollowService.getFollowStatus(userId);
      setIsFollowing(status === 'following' || status === true);
    } catch (err) {
      console.error('Failed to load follow status:', err);
      setError('Failed to load follow status');
      setIsFollowing(false);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowToggle = async () => {
    if (loading || disabled) return;

    try {
      setLoading(true);
      setError(null);

      if (isFollowing) {
        await socialFollowService.unfollowUser(userId);
        setIsFollowing(false);
        onFollowChange?.(false, userId);
      } else {
        await socialFollowService.followUser(userId);
        setIsFollowing(true);
        onFollowChange?.(true, userId);
      }
    } catch (err) {
      console.error('Failed to toggle follow:', err);
      setError(err.message || 'Failed to update follow status');
      
      // Show error briefly then clear
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  // Don't render if no userId
  if (!userId) {
    return null;
  }

  // Don't render for current user (assuming we have user context)
  const currentUserId = localStorage.getItem('userId');
  if (userId === currentUserId) {
    return null;
  }

  const buttonText = isFollowing ? 'Unfollow' : 'Follow';
  const IconComponent = loading ? FiLoader : (isFollowing ? FiUserMinus : FiUserPlus);

  return (
    <div className="relative">
      <button
        onClick={handleFollowToggle}
        disabled={loading || disabled}
        className={`
          inline-flex items-center gap-2 rounded-lg font-medium transition-all duration-200
          ${sizeClasses[size]}
          ${getVariantClasses(isFollowing)}
          ${loading || disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}
          ${className}
        `}
        title={error || buttonText}
      >
        {showIcon && (
          <IconComponent 
            className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} 
          />
        )}
        <span>{buttonText}</span>
      </button>

      {/* Error tooltip */}
      {error && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-red-600 text-white text-xs rounded-lg shadow-lg z-50 whitespace-nowrap">
          {error}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-red-600"></div>
        </div>
      )}
    </div>
  );
};

export default FollowButton;
