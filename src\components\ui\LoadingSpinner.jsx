import React from 'react';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'blue', 
  text = 'Loading...', 
  fullScreen = false,
  currentTheme = 'light'
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-24 w-24'
  };

  // Color classes
  const colorClasses = {
    blue: 'border-blue-600',
    violet: 'border-violet-600',
    green: 'border-green-600',
    red: 'border-red-600',
    gray: 'border-gray-600'
  };

  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  const spinnerContent = (
    <div className="text-center">
      <div className={`animate-spin rounded-full ${sizeClasses[size]} border-b-2 ${colorClasses[color]} mx-auto mb-4`}></div>
      {text && (
        <p className={`${textSecondary} text-sm`}>{text}</p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        {spinnerContent}
      </div>
    );
  }

  return spinnerContent;
};

/**
 * Card Loading Skeleton Component
 */
export const CardSkeleton = ({ className = '', rows = 3 }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-gray-200 dark:bg-gray-700 rounded"
          style={{ width: `${100 - (i * 10)}%` }}
        />
      ))}
    </div>
  </div>
);

/**
 * Chart Loading Skeleton Component
 */
export const ChartSkeleton = ({ width = '100%', height = '300px', className = '' }) => (
  <div
    className={`animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg ${className}`}
    style={{ width, height }}
  >
    <div className="flex items-center justify-center h-full">
      <div className="text-gray-400 dark:text-gray-500">
        <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
      </div>
    </div>
  </div>
);

export default LoadingSpinner;
