import React from 'react';
import { FiPlus, FiZap, FiClock, FiCheck, FiX, FiAlertCircle, FiInfo, FiEdit, FiList, FiMessageSquare, FiSettings, FiAward, FiTrendingUp } from 'react-icons/fi';

const QuestionForm = ({
  questionType,
  setQuestionType,
  descType,
  setDescType,
  questionForm,
  onQuestionChange,
  onOptionChange,
  onAddQuestion,
  onQuestionAdd,
  onAIGenerate,
  subjects = [],
  subjectId,
  setSubjectId,
  chaptersBySubject,
  topicsByChapter,
  subtopicsByTopic,
  chapterId,
  topicId,
  subtopicId,
  setChapterId,
  setTopicId,
  setSubtopicId,
  chaptersLoading,
  topicsLoading,
  subtopicsLoading,
  themeClasses,
  isSubmitting,
  userType = 'teacher',
  // AI Generation options
  aiNoOfQuestions,
  setAiNoOfQuestions,
  aiDifficultyMode,
  setAiDifficultyMode,
  aiNoOfEasy,
  setAiNoOfEasy,
  aiNoOfMedium,
  setAiNoOfMedium,
  aiNoOfHard,
  setAiNoOfHard,
  // Manual question creation fields
  gradeClasses = []
}) => {


  return (
    <div className="space-y-6">
      {/* Enhanced Institute-specific note */}
      {userType === 'institute' && (
        <div className="relative overflow-hidden bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/20 dark:via-emerald-900/20 dark:to-teal-900/20 border border-green-200 dark:border-green-700 rounded-2xl p-6 shadow-lg">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="question-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <circle cx="10" cy="10" r="1" fill="currentColor" className="text-green-600"/>
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#question-grid)" />
            </svg>
          </div>

          <div className="relative flex items-start gap-4">
            <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-green-600 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <FiPlus className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-bold text-green-900 dark:text-green-100 mb-2">
                Build Your Question Bank
              </h4>
              <p className="text-green-700 dark:text-green-200 text-sm leading-relaxed mb-3">
                Create a comprehensive collection of categorized questions by subject, chapter, and topic.
                These questions can be reused across multiple competitions and assessments.
              </p>
              <div className="flex flex-wrap gap-2">
                <div className="flex items-center gap-1 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full text-xs text-green-700 dark:text-green-300">
                  <FiZap className="w-3 h-3" />
                  AI-Powered Generation
                </div>
                <div className="flex items-center gap-1 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full text-xs text-green-700 dark:text-green-300">
                  <FiEdit className="w-3 h-3" />
                  Manual Creation
                </div>
                <div className="flex items-center gap-1 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full text-xs text-green-700 dark:text-green-300">
                  <FiList className="w-3 h-3" />
                  Smart Categorization
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Question Type Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-4">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-1 flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <FiPlus className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            Question Type Selection
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">Choose the type of question you want to create</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => setQuestionType("MCQS")}
            className={`group relative p-4 rounded-xl font-medium transition-all duration-300 flex flex-col items-center gap-3 border-2 ${
              questionType === "MCQS"
                ? "bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-500 text-blue-700 dark:text-blue-300 shadow-xl ring-2 ring-blue-100 dark:ring-blue-900 transform scale-105"
                : "bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 hover:border-gray-400 dark:hover:border-gray-500 shadow-lg hover:shadow-xl transform hover:scale-105"
            }`}
          >
            <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
              questionType === "MCQS"
                ? "bg-blue-600 text-white shadow-lg"
                : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 group-hover:bg-gray-300 dark:group-hover:bg-gray-500"
            }`}>
              <FiList className="w-6 h-6" />
            </div>
            <div className="text-center">
              <h4 className="text-base font-bold mb-1">Multiple Choice</h4>
              <p className="text-xs opacity-75">Questions with predefined options</p>
            </div>
            {questionType === "MCQS" && (
              <div className="absolute top-2 right-2 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                <FiCheck className="w-3 h-3 text-white" />
              </div>
            )}
          </button>

          <button
            type="button"
            onClick={() => setQuestionType("DESCRIPTIVE")}
            className={`group relative p-4 rounded-xl font-medium transition-all duration-300 flex flex-col items-center gap-3 border-2 ${
              questionType === "DESCRIPTIVE"
                ? "bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-500 text-purple-700 dark:text-purple-300 shadow-xl ring-2 ring-purple-100 dark:ring-purple-900 transform scale-105"
                : "bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 hover:border-gray-400 dark:hover:border-gray-500 shadow-lg hover:shadow-xl transform hover:scale-105"
            }`}
          >
            <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
              questionType === "DESCRIPTIVE"
                ? "bg-purple-600 text-white shadow-lg"
                : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 group-hover:bg-gray-300 dark:group-hover:bg-gray-500"
            }`}>
              <FiMessageSquare className="w-6 h-6" />
            </div>
            <div className="text-center">
              <h4 className="text-base font-bold mb-1">Descriptive</h4>
              <p className="text-xs opacity-75">Open-ended text responses</p>
            </div>
            {questionType === "DESCRIPTIVE" && (
              <div className="absolute top-2 right-2 w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center">
                <FiCheck className="w-3 h-3 text-white" />
              </div>
            )}
          </button>
        </div>
      </div>

      {/* Enhanced AI Generation Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-700 shadow-lg">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="ai-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="currentColor" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#ai-grid)" className="text-blue-600" />
          </svg>
        </div>

        <div className="relative">
          <div className="flex items-start gap-4 mb-6">
            <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <FiZap className="w-7 h-7 text-white animate-pulse" />
            </div>
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-blue-900 dark:text-blue-100 mb-2">
                AI-Powered Question Generation
              </h3>
              <p className="text-blue-700 dark:text-blue-300 text-lg leading-relaxed">
                Generate high-quality questions instantly using advanced AI. Customize difficulty levels and topics to match your requirements.
              </p>
            </div>
          </div>

          {/* AI Generation Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Number of Questions <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={aiNoOfQuestions}
              onChange={e => setAiNoOfQuestions(parseInt(e.target.value) || 1)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            />
          </div>
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Difficulty Mode
            </label>
            <select
              value={aiDifficultyMode}
              onChange={e => setAiDifficultyMode(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
            >
              <option value="mix">Mixed (Default)</option>
              <option value="custom">Custom Distribution</option>
            </select>
          </div>
        </div>

        {/* Custom Difficulty Distribution */}
        {aiDifficultyMode === "custom" && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Easy Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfEasy}
                onChange={e => setAiNoOfEasy(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Medium Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfMedium}
                onChange={e => setAiNoOfMedium(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Hard Questions
              </label>
              <input
                type="number"
                min="0"
                value={aiNoOfHard}
                onChange={e => setAiNoOfHard(parseInt(e.target.value) || 0)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              />
            </div>
            <div className="md:col-span-3">
              <p className={`text-sm ${themeClasses.label}`}>
                Total: {aiNoOfEasy + aiNoOfMedium + aiNoOfHard} questions
                {(aiNoOfEasy + aiNoOfMedium + aiNoOfHard) !== aiNoOfQuestions && (
                  <span className="text-red-500 ml-2">
                    (Should equal {aiNoOfQuestions})
                  </span>
                )}
              </p>
            </div>
          </div>
        )}

        {/* Subject/Chapter/Topic/Subtopic Selection - For Both Teachers and Institutes */}
        <div className="space-y-4 mb-6">
          {/* Subject Selection */}
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>
              Subject <span className="text-red-500">*</span>
            </label>
            <select
              value={subjectId}
              onChange={e => setSubjectId(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            >
              <option value="">Select Subject</option>
              {subjects && subjects.map(subject => (
                <option key={subject.id} value={subject.id}>{subject.name}</option>
              ))}
            </select>
          </div>

          {/* Chapter/Topic/Subtopic Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Chapter <span className="text-red-500">*</span>
              </label>
              <select
                value={chapterId}
                onChange={e => setChapterId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                required
                disabled={chaptersLoading || !subjectId}
              >
                <option value="">{chaptersLoading ? "Loading..." : subjectId ? "Select Chapter" : "Select Subject First"}</option>
                {chaptersBySubject && chaptersBySubject.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Topic (optional)</label>
              <select
                value={topicId}
                onChange={e => setTopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={topicsLoading || !chapterId}
              >
                <option value="">{topicsLoading ? "Loading..." : chapterId ? "Select Topic" : "Select Chapter First"}</option>
                {topicsByChapter && topicsByChapter.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>Subtopic (optional)</label>
              <select
                value={subtopicId}
                onChange={e => setSubtopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={subtopicsLoading || !topicId}
              >
                <option value="">{subtopicsLoading ? "Loading..." : topicId ? "Select Subtopic" : "Select Topic First"}</option>
                {subtopicsByTopic && subtopicsByTopic.map(opt => (
                  <option key={opt.id} value={opt.id}>{opt.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

          <button
            type="button"
            onClick={onAIGenerate}
            disabled={!subjectId || !chapterId || isSubmitting || (aiDifficultyMode === "custom" && (aiNoOfEasy + aiNoOfMedium + aiNoOfHard) !== aiNoOfQuestions)}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2"
          >
            <FiZap className="w-4 h-4" />
            {isSubmitting ? "Generating..." : `Generate ${aiNoOfQuestions} AI Questions`}
          </button>
        </div>
      </div>

      {/* Manual Question Creation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mt-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <FiPlus className="w-5 h-5 text-green-600" />
          Add Question Manually
        </h3>

        {/* Descriptive Type Selection */}
        {questionType === "DESCRIPTIVE" && (
          <div className="mb-4">
            <label className={`block mb-2 font-medium ${themeClasses.label}`}>Descriptive Type</label>
            <select
              value={descType}
              onChange={e => setDescType(e.target.value)}
              className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
              required
            >
              <option value="SHORT">Short Answer</option>
              <option value="LONG">Long Answer</option>
            </select>
          </div>
        )}

        <form onSubmit={onQuestionAdd || onAddQuestion} className="space-y-6">
          {/* Subject/Chapter/Topic/Subtopic and Class Number Selection for Manual Questions */}
          <div className="space-y-4 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-blue-800 dark:text-blue-300">Question Categorization</h4>
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded-full">
                Class number inherited from exam
              </div>
            </div>
            {/* Subject Selection */}
            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                value={subjectId}
                onChange={e => setSubjectId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                required
              >
                <option value="">Select Subject</option>
                {subjects && subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>

            {/* Chapter/Topic/Subtopic Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                  Chapter <span className="text-red-500">*</span>
                </label>
                <select
                  value={chapterId}
                  onChange={e => setChapterId(e.target.value)}
                  className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                  required
                  disabled={chaptersLoading || !subjectId}
                >
                  <option value="">{chaptersLoading ? "Loading..." : subjectId ? "Select Chapter" : "Select Subject First"}</option>
                  {chaptersBySubject && chaptersBySubject.map(chapter => (
                    <option key={chapter.id} value={chapter.id}>{chapter.name}</option>
                  ))}
                </select>
                {chaptersLoading && <p className="text-xs text-gray-500 mt-1">Loading chapters...</p>}
              </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Topic <span className="text-gray-400">(optional)</span>
              </label>
              <select
                value={topicId}
                onChange={e => setTopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={!chapterId || topicsLoading}
              >
                <option value="">{topicsLoading ? "Loading..." : chapterId ? "Select Topic (Optional)" : "Select Chapter First"}</option>
                {topicsByChapter && topicsByChapter.map(topic => (
                  <option key={topic.id} value={topic.id}>{topic.name}</option>
                ))}
              </select>
              {topicsLoading && <p className="text-xs text-gray-500 mt-1">Loading topics...</p>}
            </div>

            <div>
              <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                Subtopic <span className="text-gray-400">(optional)</span>
              </label>
              <select
                value={subtopicId}
                onChange={e => setSubtopicId(e.target.value)}
                className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                disabled={subtopicsLoading || !chapterId}
              >
                <option value="">{subtopicsLoading ? "Loading..." : chapterId ? "Select Subtopic (Optional)" : "Select Chapter First"}</option>
                {subtopicsByTopic && subtopicsByTopic.map(subtopic => (
                  <option key={subtopic.id} value={subtopic.id}>{subtopic.name}</option>
                ))}
              </select>
              {subtopicsLoading && <p className="text-xs text-gray-500 mt-1">Loading subtopics...</p>}
            </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-5 rounded-xl border border-gray-200 dark:border-gray-700">
            <label className={`block mb-3 font-medium ${themeClasses.label} flex items-center gap-2`}>
              <FiEdit className="w-4 h-4 text-blue-600" />
              Question Text <span className="text-red-500">*</span>
            </label>
            <textarea
              name="text"
              value={questionForm.text}
              onChange={onQuestionChange}
              rows={4}
              className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none ${themeClasses.input}`}
              placeholder="Enter your question here..."
              required
            />
          </div>

          {questionType === "MCQS" && (
            <div className="bg-white dark:bg-gray-800 p-5 rounded-xl border border-gray-200 dark:border-gray-700">
              <label className={`block mb-4 font-medium ${themeClasses.label} flex items-center gap-2`}>
                <FiList className="w-4 h-4 text-green-600" />
                Options
              </label>
              <div className="space-y-3">
                {questionForm.options.map((opt, idx) => (
                  <div key={idx} className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                    <span className="w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full flex items-center justify-center text-sm font-semibold">
                      {String.fromCharCode(65 + idx)}
                    </span>
                    <input
                      type="text"
                      value={opt.option_text}
                      onChange={(e) => onOptionChange(idx, "option_text", e.target.value)}
                      className={`flex-1 rounded-lg px-3 py-2 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                      placeholder={`Option ${idx + 1}`}
                      required
                    />
                    <label className="flex items-center gap-2 cursor-pointer px-3 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors duration-200">
                      <input
                        type="radio"
                        name="correct_option"
                        checked={opt.is_correct}
                        onChange={() => onOptionChange(idx, "is_correct", true)}
                        className="w-4 h-4 text-green-600 focus:ring-green-500"
                      />
                      <span className={`text-sm font-medium ${opt.is_correct ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}`}>
                        Correct
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 p-5 rounded-xl border border-gray-200 dark:border-gray-700">
            <label className={`block mb-3 font-medium ${themeClasses.label} flex items-center gap-2`}>
              <FiMessageSquare className="w-4 h-4 text-purple-600" />
              {questionType === "MCQS" ? "Answer Explanation (optional)" : "Expected Answer"}
            </label>
            <textarea
              name="answer"
              value={questionForm.answer}
              onChange={onQuestionChange}
              rows={3}
              className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none ${themeClasses.input}`}
              placeholder={questionType === "MCQS" ? "Provide explanation for the correct answer..." : "Enter the expected answer..."}
            />
          </div>

          <div className="bg-white dark:bg-gray-800 p-5 rounded-xl border border-gray-200 dark:border-gray-700">
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
              <FiSettings className="w-4 h-4 text-orange-600" />
              Question Settings
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                  <FiAward className="w-4 h-4 text-yellow-600" />
                  Marks
                </label>
                <input
                  type="number"
                  name="marks"
                  value={questionForm.marks}
                  onChange={onQuestionChange}
                  min={1}
                  max={100}
                  className={`w-full rounded-lg px-3 py-2 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                />
              </div>
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
                  <FiTrendingUp className="w-4 h-4 text-red-600" />
                  Difficulty Level
                </label>
                <select
                  name="Level"
                  value={questionForm.Level}
                  onChange={onQuestionChange}
                  className={`w-full rounded-lg px-3 py-2 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                >
                  <option value="EASY">Easy</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="HARD">Hard</option>
                </select>
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={isSubmitting || !subjectId || !chapterId || !questionForm.text.trim()}
            className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-4 px-6 rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
          >
            <FiPlus className="w-5 h-5" />
            {isSubmitting ? "Adding Question..." : "Add Question Manually"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default QuestionForm;
