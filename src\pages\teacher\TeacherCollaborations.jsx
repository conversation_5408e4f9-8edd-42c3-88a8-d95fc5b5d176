import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiUsers, FiMail, FiUserCheck, FiRefreshCw } from 'react-icons/fi';
import {
  getTeacherCollaborations,
  fetchTeacherSentInvitations,
  fetchTeacherReceivedInvitations,
  sendInvitationToInstituteFromTeacher,
  acceptTeacherInvitation,
  rejectTeacherInvitation,
  updateTeacherCollaboration,
  deleteTeacherCollaboration,
  clearSendInvitationState,
  clearRespondInvitationState,
  clearCollaborationActions,
  selectTeacherCollaborations,
  selectTeacherSentInvitations,
  selectTeacherReceivedInvitations,
  selectTeacherSendInvitationState,
  selectTeacherRespondInvitationState,
  selectTeacherCollaborationActions
} from '../../store/slices/TeacherCollaborationSlice';
import CollaborationList from '../../components/collaboration/CollaborationList';
import InvitationList from '../../components/collaboration/InvitationList';
import SendInvitationModal from '../../components/collaboration/SendInvitationModal';
import { useToast } from '../../components/ui/Toast';

const TeacherCollaborations = () => {
  const dispatch = useDispatch();
  const { toast } = useToast();
  
  // Local state
  const [activeTab, setActiveTab] = useState('collaborations');
  const [showSendModal, setShowSendModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isReloading, setIsReloading] = useState(false);

  // Redux state
  const collaborations = useSelector(selectTeacherCollaborations);
  const sentInvitations = useSelector(selectTeacherSentInvitations);
  const receivedInvitations = useSelector(selectTeacherReceivedInvitations);
  const sendInvitationState = useSelector(selectTeacherSendInvitationState);
  const respondInvitationState = useSelector(selectTeacherRespondInvitationState);
  const collaborationActions = useSelector(selectTeacherCollaborationActions);

  // Load data on component mount
  useEffect(() => {
    loadCollaborations();
    loadInvitations();
  }, [dispatch]);

  // Handle success states
  useEffect(() => {
    if (sendInvitationState.success) {
      toast.success('Success', 'Invitation sent successfully!');
      setShowSendModal(false);
      dispatch(clearSendInvitationState());
      loadInvitations(); // Refresh invitations
    }
  }, [sendInvitationState.success, dispatch, toast]);

  useEffect(() => {
    if (respondInvitationState.success) {
      toast.success('Success', 'Invitation response sent successfully!');
      dispatch(clearRespondInvitationState());
      loadInvitations(); // Refresh invitations
      loadCollaborations(); // Refresh collaborations in case invitation was accepted
    }
  }, [respondInvitationState.success, dispatch, toast]);

  useEffect(() => {
    if (collaborationActions.success) {
      toast.success('Success', 'Collaboration updated successfully!');
      dispatch(clearCollaborationActions());
      loadCollaborations(); // Refresh collaborations
    }
  }, [collaborationActions.success, dispatch, toast]);

  // Handle error states
  useEffect(() => {
    if (sendInvitationState.error) {
      toast.error('Error', typeof sendInvitationState.error === 'string'
        ? sendInvitationState.error
        : 'Failed to send invitation');
    }
  }, [sendInvitationState.error, toast]);

  useEffect(() => {
    if (respondInvitationState.error) {
      toast.error('Error', typeof respondInvitationState.error === 'string'
        ? respondInvitationState.error
        : 'Failed to respond to invitation');
    }
  }, [respondInvitationState.error, toast]);

  useEffect(() => {
    if (collaborationActions.error) {
      toast.error('Error', typeof collaborationActions.error === 'string'
        ? collaborationActions.error
        : 'Failed to update collaboration');
    }
  }, [collaborationActions.error]);

  const loadCollaborations = () => {
    dispatch(getTeacherCollaborations({ page: currentPage, size: 20 }));
  };

  const loadInvitations = () => {
    dispatch(fetchTeacherSentInvitations({ page: 1, size: 50 }));
    dispatch(fetchTeacherReceivedInvitations({ page: 1, size: 50 }));
  };

  const handleReloadCollaborations = async () => {
    setIsReloading(true);
    try {
      await dispatch(getTeacherCollaborations({ page: currentPage, size: 20 })).unwrap();
    } catch (error) {
      console.error('Error reloading collaborations:', error);
    } finally {
      setIsReloading(false);
    }
  };

  const handleReloadInvitations = async () => {
    setIsReloading(true);
    try {
      await Promise.all([
        dispatch(fetchTeacherSentInvitations({ page: 1, size: 50 })).unwrap(),
        dispatch(fetchTeacherReceivedInvitations({ page: 1, size: 50 })).unwrap()
      ]);
    } catch (error) {
      console.error('Error reloading invitations:', error);
    } finally {
      setIsReloading(false);
    }
  };

  const handleSendInvitation = async (invitationData) => {
    await dispatch(sendInvitationToInstituteFromTeacher(invitationData)).unwrap();
  };

  const handleAcceptInvitation = async (invitation) => {
    await dispatch(acceptTeacherInvitation(invitation.id)).unwrap();
  };

  const handleRejectInvitation = async (invitation) => {
    await dispatch(rejectTeacherInvitation(invitation.id)).unwrap();
  };

  const handleEditCollaboration = (collaboration) => {
    // TODO: Implement edit collaboration modal
    console.log('Edit collaboration:', collaboration);
  };

  const handleDeleteCollaboration = async (collaboration) => {
    if (window.confirm('Are you sure you want to delete this collaboration?')) {
      await dispatch(deleteTeacherCollaboration(collaboration.id)).unwrap();
    }
  };

  const handleViewCollaborationDetails = (collaboration) => {
    // TODO: Implement collaboration details modal
    console.log('View collaboration details:', collaboration);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    dispatch(getTeacherCollaborations({ page, size: 20 }));
  };

  const handleFilterChange = (filters) => {
    // TODO: Implement filtering
    console.log('Filter change:', filters);
  };

  const tabs = [
    {
      id: 'collaborations',
      label: 'My Collaborations',
      icon: FiUsers,
      count: collaborations.total || 0
    },
    {
      id: 'invitations',
      label: 'Invitations',
      icon: FiMail,
      count: (sentInvitations.data?.length || 0) + (receivedInvitations.data?.length || 0)
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Institute Collaborations
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Manage your collaborations with educational institutes
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSendModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-violet-600 hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 transition-colors"
            >
              <FiMail className="h-4 w-4 mr-2" />
              Send Invitation
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-violet-500 text-violet-600 dark:text-violet-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                  <span className={`${
                    activeTab === tab.id
                      ? 'bg-violet-100 text-violet-600 dark:bg-violet-900 dark:text-violet-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  } inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}>
                    {tab.count}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        {activeTab === 'collaborations' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Active Collaborations
              </h2>
              <button
                onClick={handleReloadCollaborations}
                disabled={isReloading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 transition-colors"
              >
                <FiRefreshCw className={`w-4 h-4 mr-2 ${isReloading ? 'animate-spin' : ''}`} />
                {isReloading ? 'Reloading...' : 'Reload'}
              </button>
            </div>

            <CollaborationList
              collaborations={collaborations.data}
              loading={collaborations.loading || isReloading}
              error={collaborations.error}
              pagination={collaborations}
              onEdit={handleEditCollaboration}
              onDelete={handleDeleteCollaboration}
              onViewDetails={handleViewCollaborationDetails}
              onPageChange={handlePageChange}
              onFilterChange={handleFilterChange}
              onCreateNew={() => setShowSendModal(true)}
              userRole="teacher"
              showCreateButton={true}
            />
          </div>
        )}

        {activeTab === 'invitations' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Invitation Management
              </h2>
              <button
                onClick={handleReloadInvitations}
                disabled={isReloading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 transition-colors"
              >
                <FiRefreshCw className={`w-4 h-4 mr-2 ${isReloading ? 'animate-spin' : ''}`} />
                {isReloading ? 'Reloading...' : 'Reload'}
              </button>
            </div>

            <InvitationList
              sentInvitations={sentInvitations.data}
              receivedInvitations={receivedInvitations.data}
              loading={sentInvitations.loading || receivedInvitations.loading || isReloading}
              error={sentInvitations.error || receivedInvitations.error}
              onAccept={handleAcceptInvitation}
              onReject={handleRejectInvitation}
              onSendNew={() => setShowSendModal(true)}
              userRole="teacher"
              showSendButton={true}
            />
          </div>
        )}
      </div>

      {/* Send Invitation Modal */}
      <SendInvitationModal
        isOpen={showSendModal}
        onClose={() => setShowSendModal(false)}
        onSend={handleSendInvitation}
        loading={sendInvitationState.loading}
        userRole="teacher"
        recipientType="institute"
      />
    </div>
  );
};

export default TeacherCollaborations;
