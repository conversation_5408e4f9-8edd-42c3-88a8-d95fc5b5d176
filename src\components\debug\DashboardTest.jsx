import React from 'react';
import { D3B<PERSON><PERSON>hart, D3L<PERSON><PERSON><PERSON> } from '../charts';

/**
 * Dashboard Test Component
 * Tests the enhanced chart components with sample data
 */
const DashboardTest = () => {
  // Sample data for testing
  const barChartData = [
    { x: 'Math', y: 85 },
    { x: 'Science', y: 92 },
    { x: 'English', y: 78 },
    { x: 'History', y: 88 },
    { x: 'Art', y: 95 }
  ];

  const lineChartData = [
    { x: 'Jan', y: 75 },
    { x: 'Feb', y: 80 },
    { x: 'Mar', y: 85 },
    { x: 'Apr', y: 82 },
    { x: 'May', y: 90 },
    { x: 'Jun', y: 88 }
  ];

  const isDark = document.documentElement.classList.contains('dark');

  return (
    <div className="p-8 space-y-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          Dashboard Chart Tests
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Testing enhanced chart components with modern styling
        </p>
      </div>

      {/* Bar Chart Test */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Enhanced Bar Chart
        </h2>
        <div className="overflow-x-auto">
          <D3BarChart
            data={barChartData}
            width={600}
            height={350}
            xKey="x"
            yKey="y"
            showValues={true}
            animate={true}
            gradient={true}
            cornerRadius={6}
            theme={isDark ? 'dark' : 'light'}
            xAxisLabel="Subjects"
            yAxisLabel="Score (%)"
          />
        </div>
      </div>

      {/* Line Chart Test */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Enhanced Line Chart
        </h2>
        <div className="overflow-x-auto">
          <D3LineChart
            data={lineChartData}
            width={600}
            height={350}
            xKey="x"
            yKey="y"
            animate={true}
            showPoints={true}
            gradient={true}
            theme={isDark ? 'dark' : 'light'}
            xAxisLabel="Month"
            yAxisLabel="Performance (%)"
          />
        </div>
      </div>

      {/* Multiple Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Compact Bar Chart
          </h3>
          <D3BarChart
            data={barChartData.slice(0, 3)}
            width={400}
            height={250}
            xKey="x"
            yKey="y"
            showValues={true}
            animate={true}
            gradient={true}
            cornerRadius={4}
            theme={isDark ? 'dark' : 'light'}
            xAxisLabel="Top Subjects"
            yAxisLabel="Score"
          />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Compact Line Chart
          </h3>
          <D3LineChart
            data={lineChartData.slice(0, 4)}
            width={400}
            height={250}
            xKey="x"
            yKey="y"
            animate={true}
            showPoints={true}
            gradient={true}
            theme={isDark ? 'dark' : 'light'}
            xAxisLabel="Recent Months"
            yAxisLabel="Trend"
          />
        </div>
      </div>

      {/* Test Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Test Results
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
            <div className="text-green-800 dark:text-green-200 font-semibold">✓ Charts Rendering</div>
            <div className="text-green-600 dark:text-green-400 text-sm">All charts display correctly</div>
          </div>
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <div className="text-blue-800 dark:text-blue-200 font-semibold">✓ Animations Working</div>
            <div className="text-blue-600 dark:text-blue-400 text-sm">Smooth transitions enabled</div>
          </div>
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
            <div className="text-purple-800 dark:text-purple-200 font-semibold">✓ Theme Support</div>
            <div className="text-purple-600 dark:text-purple-400 text-sm">Dark/light mode compatible</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardTest;
