/**
 * Direct Exam Interface
 * Simple exam interface that works directly with exam data using proper Redux architecture
 */

import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lertTriangle } from 'react-icons/fi';

// Redux actions
import {
  updateAnswer,
  setRemainingTime,
  clearSession,
  setSessionStatus,
  submitExamSession
} from '../../../store/slices/exam/examSessionSlice';
import { checkExamWithAI } from '../../../store/slices/exam/aiCheckingSlice';

const DirectExamInterface = ({ examData, examId, onBackToExams }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Redux state
  const examSession = useSelector(state => state.examSession);
  const { submitting } = useSelector(state => state.exams);

  // Local state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(examData?.remaining_time_seconds || 0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [violationCount, setViolationCount] = useState(0);
  const [maxViolations] = useState(3); // Maximum violations allowed
  const [violationNotification, setViolationNotification] = useState(null);

  // Time tracking for each question
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [timeSpentPerQuestion, setTimeSpentPerQuestion] = useState({});

  // Initialize session data in Redux if not already set
  React.useEffect(() => {
    if (examData && !examSession.sessionId) {
      console.log('🔍 Debug examData structure:', examData);
      dispatch(setSessionStatus('active'));
      dispatch(setRemainingTime(examData.remaining_time_seconds || 0));

      // Reset question timer when exam starts
      setQuestionStartTime(Date.now());
    }
  }, [examData, examSession.sessionId, dispatch]);

  // Activate security measures when exam starts (delayed to avoid fullscreen issues)
  React.useEffect(() => {
    if (examData) {
      console.log('🔒 Scheduling security measures activation...');

      // Delay activation to ensure component is fully rendered and user has interacted
      const securityTimer = setTimeout(async () => {
        try {
          const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
          AntiCheatService.default.activate();
          // Mark exam as started for anti-cheat service
          AntiCheatService.default.isExamStarted = true;
          console.log('✅ Anti-cheat service activated');
        } catch (error) {
          console.warn('⚠️ Could not activate anti-cheat service:', error);
        }
      }, 2000); // 2 second delay to ensure user interaction

      // Cleanup: Deactivate security when component unmounts
      return () => {
        clearTimeout(securityTimer);
        console.log('🔒 Deactivating security measures on cleanup...');
        const deactivateSecurityMeasures = async () => {
          try {
            const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
            AntiCheatService.default.deactivate();
            console.log('✅ Anti-cheat service deactivated on cleanup');
          } catch (error) {
            console.warn('⚠️ Could not deactivate anti-cheat service on cleanup:', error);
          }
        };

        deactivateSecurityMeasures();
      };
    }
  }, [examData]);

  // Timer effect - separate local and Redux state updates
  React.useEffect(() => {
    if (timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [timeRemaining]);

  // Separate effect to sync local time with Redux
  React.useEffect(() => {
    dispatch(setRemainingTime(timeRemaining));
  }, [timeRemaining, dispatch]);



  // Separate effect for auto-submit when time expires
  React.useEffect(() => {
    if (timeRemaining <= 0 && timeRemaining !== null && !isSubmitting) {
      // Use setTimeout to avoid setState during render
      const autoSubmitTimer = setTimeout(() => {
        handleSubmit(true); // Auto-submit when time runs out
      }, 100);

      return () => clearTimeout(autoSubmitTimer);
    }
  }, [timeRemaining, isSubmitting]);

  const handleAnswerChange = useCallback((questionId, optionId) => {
    // Debug: Log the questionId being passed
    console.log('🔍 handleAnswerChange called with:', { questionId, optionId });
    console.log('🔍 questionId type:', typeof questionId);
    console.log('🔍 questionId value:', questionId);

    if (!questionId || questionId === 'undefined') {
      console.error('❌ Invalid questionId passed to handleAnswerChange:', questionId);
      return;
    }

    // Track time spent on this question
    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
    setTimeSpentPerQuestion(prev => ({
      ...prev,
      [questionId]: (prev[questionId] || 0) + timeSpent
    }));

    // Reset timer for continued work on this question
    setQuestionStartTime(Date.now());

    // Update Redux state
    dispatch(updateAnswer({ questionId, answer: optionId }));
    console.log('📝 Answer changed via Redux:', { questionId, optionId, timeSpent });
  }, [dispatch, questionStartTime]);

  const handleSubmit = useCallback(async (isAutoSubmit = false) => {
    if (isSubmitting) return; // Prevent double submission

    try {
      setIsSubmitting(true);
      console.log('📤 Submitting exam via Redux...', {
        isAutoSubmit,
        answers: examSession.currentAnswers,
        sessionId: examData?.session_id
      });

      // 1. Set submission status
      dispatch(setSessionStatus('submitting'));

      // Track time spent on current question before submission
      const questionsArray = examData?.questions || [];
      const currentQuestionId = questionsArray[currentQuestionIndex]?.question_id;
      if (currentQuestionId) {
        const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
        setTimeSpentPerQuestion(prev => ({
          ...prev,
          [currentQuestionId]: (prev[currentQuestionId] || 0) + timeSpent
        }));
      }

      // 2. Submit exam using Redux action with proper payload format
      const submissionData = {
        sessionId: examData?.session_id,
        exam: examData,
        questions: examData?.questions || [],
        studentAnswers: examSession.currentAnswers,
        isAutoSubmit,
        timingData: timeSpentPerQuestion, // ✅ Include actual timing data
        submissionTime: new Date().toISOString()
      };

      const result = await dispatch(submitExamSession(submissionData)).unwrap();
      console.log('✅ Exam submitted successfully via Redux:', result);

      // 3. Turn off all security measures
      console.log('🔒 Turning off security measures...');
      try {
        const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
        AntiCheatService.default.deactivate();
        console.log('✅ Anti-cheat service deactivated');
      } catch (error) {
        console.warn('⚠️ Could not deactivate anti-cheat service:', error);
      }

      // 4. Permanently disconnect WebSocket to prevent reconnections
      console.log('🔌 Permanently disconnecting WebSocket...');
      try {
        const ExamWebSocketService = await import('../../../services/exam/websocket/ExamWebSocketService.js');
        ExamWebSocketService.default.permanentDisconnect();
        console.log('✅ WebSocket permanently disconnected');
      } catch (error) {
        console.warn('⚠️ Could not disconnect WebSocket:', error);
      }

      // 5. Trigger AI checking
      const examIdForAI = examData?.exam?.exam_id || examData?.exam_id || examData?.id || examId;
      if (examIdForAI) {
        try {
          console.log('🤖 Triggering AI checking via Redux...');
          await dispatch(checkExamWithAI({
            examId: examIdForAI,
            studentId: null // null for student endpoint
          })).unwrap();
          console.log('✅ AI checking completed');
        } catch (aiError) {
          console.warn('⚠️ AI checking failed:', aiError);
        }
      }

      // 6. Clear session data
      dispatch(clearSession());

      // 7. Navigate to success page
      // Try multiple sources for exam ID with comprehensive fallbacks
      const finalExamId = examData?.exam?.exam_id ||
                         examData?.exam_id ||
                         examData?.id ||
                         examId ||
                         (examData?.exam && examData.exam.id) ||
                         (examData?.session_id && examData.session_id.split('-')[0]); // Last resort fallback

      console.log('🔍 Debug exam ID extraction:', {
        examData,
        'examData?.exam?.exam_id': examData?.exam?.exam_id,
        'examData?.exam_id': examData?.exam_id,
        'examData?.id': examData?.id,
        'examId prop': examId,
        'examData?.exam?.id': examData?.exam?.id,
        'examData structure': examData ? Object.keys(examData) : 'undefined',
        'examData.exam structure': examData?.exam ? Object.keys(examData.exam) : 'undefined',
        finalExamId
      });

      if (!finalExamId) {
        console.error('❌ No exam ID found! Cannot navigate to results page');
        console.error('❌ Full examData structure:', JSON.stringify(examData, null, 2));

        // Show error notification
        setViolationNotification({
          type: 'error',
          message: 'Error: Could not determine exam ID for results page. Redirecting to exams list.',
          timestamp: Date.now()
        });

        // Navigate to exams list after a delay
        setTimeout(() => {
          navigate('/student/exams');
        }, 3000);
        return;
      }

      navigate(`/student/exam-submitted/${finalExamId}`, {
        state: {
          examTitle: examData?.exam?.title || examData?.title || 'Exam',
          submissionTime: new Date().toISOString(),
          submissionResult: result,
          answers: examSession.currentAnswers,
          autoAITriggered: true,
          isAutoSubmit,
          examId: finalExamId // Explicitly pass exam ID in state as backup
        }
      });

    } catch (error) {
      console.error('❌ Error submitting exam via Redux:', error);
      dispatch(setSessionStatus('active')); // Reset status on error
      setIsSubmitting(false); // Reset submission state on error

      // Show error notification instead of alert
      setViolationNotification({
        type: 'error',
        message: 'Error submitting exam. Please try again.',
        timestamp: Date.now()
      });

      // Auto-hide error after 5 seconds
      setTimeout(() => {
        setViolationNotification(null);
      }, 5000);
    }
  }, [dispatch, examSession.currentAnswers, examData, isSubmitting, navigate]);

  // Navigation handlers with time tracking
  const handleQuestionNavigation = useCallback((newIndex) => {
    // Track time spent on current question before navigating
    const questionsArray = examData?.questions || [];
    const currentQuestionId = questionsArray[currentQuestionIndex]?.question_id;
    if (currentQuestionId) {
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      setTimeSpentPerQuestion(prev => ({
        ...prev,
        [currentQuestionId]: (prev[currentQuestionId] || 0) + timeSpent
      }));
    }

    // Navigate to new question and reset timer
    setCurrentQuestionIndex(newIndex);
    setQuestionStartTime(Date.now());
  }, [currentQuestionIndex, examData?.questions, questionStartTime]);

  const handleNextQuestion = useCallback(() => {
    const questionsArray = examData?.questions || [];
    if (currentQuestionIndex < questionsArray.length - 1) {
      handleQuestionNavigation(currentQuestionIndex + 1);
    }
  }, [currentQuestionIndex, examData?.questions, handleQuestionNavigation]);

  const handlePreviousQuestion = useCallback(() => {
    if (currentQuestionIndex > 0) {
      handleQuestionNavigation(currentQuestionIndex - 1);
    }
  }, [currentQuestionIndex, handleQuestionNavigation]);

  // WebSocket violation monitoring
  React.useEffect(() => {
    const setupViolationMonitoring = async () => {
      try {
        const ExamWebSocketService = await import('../../../services/exam/websocket/ExamWebSocketService.js');

        const handleViolationWarning = (violationData) => {
          console.log('⚠️ Violation warning received:', violationData);

          // Increment violation count locally since backend strikes aren't incrementing properly
          setViolationCount(prevCount => {
            const newCount = prevCount + 1;
            console.log(`🚨 Violation count updated: ${newCount}/${maxViolations}`);

            // Show clean UI notification instead of annoying alert
            setViolationNotification({
              type: newCount >= maxViolations ? 'critical' : 'warning',
              message: newCount >= maxViolations
                ? 'Maximum violations reached! Your exam will be submitted automatically.'
                : `Violation detected: ${violationData.violationType}`,
              warning: newCount >= maxViolations
                ? 'Your exam is being submitted due to security violations.'
                : `Warning ${newCount}/${maxViolations}: You will be disqualified if you receive ${maxViolations - newCount} more violation(s).`,
              timestamp: Date.now()
            });

            // Auto-hide notification after 5 seconds (unless critical)
            if (newCount < maxViolations) {
              setTimeout(() => {
                setViolationNotification(null);
              }, 5000);
            }

            // Auto-submit if max violations reached
            if (newCount >= maxViolations) {
              console.log('🚨 Maximum violations reached - auto-submitting exam');
              setTimeout(() => {
                handleSubmit(true); // Auto-submit due to violations
              }, 3000); // Give user 3 seconds to see the message
            }

            return newCount;
          });
        };

        // Handle WebSocket disconnection - auto-submit immediately
        const handleWebSocketDisconnection = (disconnectionData) => {
          console.log('🚨 WebSocket disconnected - auto-submitting exam immediately:', disconnectionData);

          // Show critical notification
          setViolationNotification({
            type: 'critical',
            message: 'Connection lost! Your exam is being submitted automatically to prevent data loss.',
            warning: 'Do not refresh or close this page.',
            timestamp: Date.now()
          });

          // Auto-submit immediately on disconnection
          setTimeout(() => {
            handleSubmit(true); // Auto-submit due to connection loss
          }, 1000); // Give 1 second to show the message
        };

        // Handle WebSocket errors - auto-submit if critical
        const handleWebSocketError = (errorData) => {
          console.log('🚨 WebSocket error received:', errorData);

          // Auto-submit on critical errors that prevent reconnection
          if (errorData.code === 'MAX_RECONNECT_ATTEMPTS' || errorData.code === 'POLICY_VIOLATION') {
            console.log('🚨 Critical WebSocket error - auto-submitting exam');

            setViolationNotification({
              type: 'critical',
              message: 'Connection error! Your exam is being submitted automatically.',
              warning: errorData.message || 'Unable to maintain secure connection.',
              timestamp: Date.now()
            });

            setTimeout(() => {
              handleSubmit(true); // Auto-submit due to critical error
            }, 2000);
          }
        };

        // Handle student disqualification - auto-submit immediately
        const handleDisqualification = (data) => {
          console.log('🚨 Student disqualified - auto-submitting exam:', data);

          setViolationNotification({
            type: 'critical',
            message: 'You have been disqualified! Your exam is being submitted automatically.',
            warning: data.reason || 'Disqualified due to security violations.',
            timestamp: Date.now()
          });

          // Auto-submit exam immediately on disqualification
          console.log('🚨 AUTO-SUBMIT TRIGGERED: Disqualification detected - calling handleSubmit(true)');
          console.log('🚨 This should use the /exam-session/auto-submit endpoint');
          handleSubmit(true).then(() => {
            // Redirect to disqualification page after successful auto-submit
            setTimeout(() => {
              navigate('/student/exam/disqualified', {
                state: { reason: data.reason }
              });
            }, 1000);
          }).catch((error) => {
            console.error('❌ Failed to auto-submit exam on disqualification:', error);
            // Still redirect even if auto-submit fails
            setTimeout(() => {
              navigate('/student/exam/disqualified', {
                state: { reason: data.reason }
              });
            }, 1000);
          });
        };

        // Listen for violation warnings
        console.log('🔌 Setting up violation warning listener...');
        ExamWebSocketService.default.on('violation_warning', handleViolationWarning);

        // Listen for WebSocket disconnections and errors
        ExamWebSocketService.default.on('disconnected', handleWebSocketDisconnection);
        ExamWebSocketService.default.on('error', handleWebSocketError);

        // Listen for student disqualification
        ExamWebSocketService.default.on('disqualified', handleDisqualification);

        // Cleanup listeners on unmount
        return () => {
          ExamWebSocketService.default.off('violation_warning', handleViolationWarning);
          ExamWebSocketService.default.off('disconnected', handleWebSocketDisconnection);
          ExamWebSocketService.default.off('error', handleWebSocketError);
          ExamWebSocketService.default.off('disqualified', handleDisqualification);
        };
      } catch (error) {
        console.warn('⚠️ Could not setup violation monitoring:', error);
      }
    };

    if (examData) {
      setupViolationMonitoring();
    }
  }, [examData, maxViolations, handleSubmit]);

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (!examData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">No exam data available</p>
          <button
            onClick={onBackToExams || (() => navigate('/student/exams'))}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  const questions = examData.questions || [];
  const currentQuestion = questions[currentQuestionIndex];
  const progress = questions.length > 0 ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0;

  // Debug: Log current question data to identify the issue
  React.useEffect(() => {
    console.log('🔍 Exam data structure:', examData);
    console.log('🔍 Questions array:', questions);
    if (currentQuestion) {
      console.log('🔍 Current question data:', currentQuestion);
      console.log('🔍 Question ID:', currentQuestion.question_id);
      console.log('🔍 Question alt ID:', currentQuestion.id);
      console.log('🔍 Question properties:', Object.keys(currentQuestion));
      console.log('🔍 Final question ID used:', currentQuestion.question_id || currentQuestion.id);
    }
  }, [examData, questions, currentQuestion]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Violation Notification */}
      {violationNotification && (
        <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg border-l-4 ${
          violationNotification.type === 'critical'
            ? 'bg-red-50 border-red-500 text-red-800'
            : violationNotification.type === 'error'
            ? 'bg-red-50 border-red-500 text-red-800'
            : 'bg-yellow-50 border-yellow-500 text-yellow-800'
        }`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FiAlertTriangle className={`w-5 h-5 ${
                violationNotification.type === 'critical' || violationNotification.type === 'error'
                  ? 'text-red-500'
                  : 'text-yellow-500'
              }`} />
            </div>
            <div className="ml-3 flex-1">
              <p className="font-medium">{violationNotification.message}</p>
              {violationNotification.warning && (
                <p className="text-sm mt-1 opacity-90">{violationNotification.warning}</p>
              )}
            </div>
            {violationNotification.type !== 'critical' && (
              <button
                onClick={() => setViolationNotification(null)}
                className="ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600"
              >
                <span className="sr-only">Close</span>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900">{examData.title}</h1>
              <p className="text-sm text-gray-600">{examData.description}</p>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FiClock className="w-4 h-4" />
                <span className={timeRemaining < 300 ? 'text-red-600 font-bold' : ''}>
                  {formatTime(timeRemaining)}
                </span>
              </div>
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FiBookOpen className="w-4 h-4" />
                <span>{currentQuestionIndex + 1} of {questions.length}</span>
              </div>
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FiUser className="w-4 h-4" />
                <span>{Object.keys(examSession.currentAnswers || {}).length} answered</span>
              </div>

              <div className={`flex items-center space-x-2 text-sm ${violationCount > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                <FiAlertTriangle className="w-4 h-4" />
                <span>Violations: {violationCount}/{maxViolations}</span>
              </div>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Question navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-medium text-gray-900 mb-3">Questions</h3>
              <div className="grid grid-cols-5 lg:grid-cols-3 gap-2">
                {questions.map((question, index) => {
                  const questionId = question.question_id || question.id;
                  const isAnswered = examSession.currentAnswers?.[questionId];

                  return (
                    <button
                      key={questionId}
                      onClick={() => setCurrentQuestionIndex(index)}
                      className={`w-8 h-8 rounded text-sm font-medium transition-colors ${
                        index === currentQuestionIndex
                          ? 'bg-blue-600 text-white'
                          : isAnswered
                          ? 'bg-green-100 text-green-800 border border-green-300'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {index + 1}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Question content */}
          <div className="lg:col-span-3">
            {currentQuestion ? (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="mb-6">
                  <div className="flex items-start justify-between mb-4">
                    <h2 className="text-lg font-medium text-gray-900">
                      Question {currentQuestionIndex + 1}
                    </h2>
                    <span className="text-sm text-gray-500">
                      {currentQuestion.marks} mark{currentQuestion.marks !== 1 ? 's' : ''}
                    </span>
                  </div>
                  
                  <p className="text-gray-800 text-base leading-relaxed">
                    {currentQuestion.question_text || currentQuestion.text}
                  </p>
                </div>

                {/* Answer Input Based on Question Type */}
                {(() => {
                  const questionType = currentQuestion.Type || currentQuestion.question_type || currentQuestion.type;
                  return questionType === 'MCQS' && currentQuestion.options;
                })() && (
                  <div className="space-y-3 mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Select your answer:</h4>
                    {Array.isArray(currentQuestion.options) ? (
                      // Array format: [{ id: "...", option_text: "..." }, ...]
                      currentQuestion.options.map((option, index) => (
                        <label
                          key={option.id || index}
                          className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                            examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] === option.option_text
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          <input
                            type="radio"
                            name={`question_${currentQuestion.question_id || currentQuestion.id}`}
                            value={option.option_text}
                            checked={examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] === option.option_text}
                            onChange={() => {
                              const questionId = currentQuestion.question_id || currentQuestion.id;
                              handleAnswerChange(questionId, option.option_text);
                            }}
                            className="text-blue-600"
                          />
                          <span className="text-gray-800">{option.option_text}</span>
                        </label>
                      ))
                    ) : (
                      // Object format: { "optionId": { "option_text": "...", ... }, ... }
                      Object.entries(currentQuestion.options).map(([optionId, optionData]) => (
                        <label
                          key={optionId}
                          className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                            examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] === optionId
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          <input
                            type="radio"
                            name={`question_${currentQuestion.question_id || currentQuestion.id}`}
                            value={optionId}
                            checked={examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] === optionId}
                            onChange={() => {
                              const questionId = currentQuestion.question_id || currentQuestion.id;
                              handleAnswerChange(questionId, optionId);
                            }}
                            className="text-blue-600"
                          />
                          <span className="text-gray-800">{optionData.option_text}</span>
                        </label>
                      ))
                    )}
                  </div>
                )}

                {/* Short Answer Input */}
                {(() => {
                  const questionType = currentQuestion.Type || currentQuestion.question_type || currentQuestion.type;
                  return questionType === 'SHORT';
                })() && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your answer:
                    </label>
                    <input
                      type="text"
                      value={examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] || ''}
                      onChange={(e) => {
                        const questionId = currentQuestion.question_id || currentQuestion.id;
                        handleAnswerChange(questionId, e.target.value);
                      }}
                      placeholder="Enter your short answer here..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      maxLength={500}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum 500 characters
                    </p>
                  </div>
                )}

                {/* Long Answer Input */}
                {(() => {
                  const questionType = currentQuestion.Type || currentQuestion.question_type || currentQuestion.type;
                  return questionType === 'LONG';
                })() && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your answer:
                    </label>
                    <textarea
                      value={examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] || ''}
                      onChange={(e) => {
                        const questionId = currentQuestion.question_id || currentQuestion.id;
                        handleAnswerChange(questionId, e.target.value);
                      }}
                      placeholder="Enter your detailed answer here..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={8}
                      maxLength={2000}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum 2000 characters ({(examSession.currentAnswers?.[currentQuestion.question_id || currentQuestion.id] || '').length}/2000)
                    </p>
                  </div>
                )}

                {/* Fallback for unknown question types */}
                {(() => {
                  const questionType = currentQuestion.Type || currentQuestion.question_type || currentQuestion.type;
                  return !['MCQS', 'SHORT', 'LONG'].includes(questionType) && (
                    <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-yellow-800">
                        <strong>Unknown question type:</strong> {questionType}
                      </p>
                      <p className="text-sm text-yellow-600 mt-1">
                        Please contact support if you see this message.
                      </p>
                    </div>
                  );
                })()}

                {/* Navigation */}
                <div className="flex justify-between">
                  <button
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestionIndex === 0}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  
                  <div className="space-x-3">
                    {currentQuestionIndex < questions.length - 1 ? (
                      <button
                        onClick={handleNextQuestion}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        Next
                      </button>
                    ) : (
                      <button
                        onClick={() => handleSubmit(false)}
                        className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                      >
                        <FiCheck className="w-4 h-4" />
                        <span>Submit Exam</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                <p className="text-gray-600">No questions available</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectExamInterface;
