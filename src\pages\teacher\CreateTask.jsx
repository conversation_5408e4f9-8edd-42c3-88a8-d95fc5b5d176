import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createTaskForStudent,
  createTaskForClassroom,
  createTaskForMultipleStudents,
  createTaskForStudentWithFiles,
  createTaskForClassroomWithFiles,
  createTaskForMultipleStudentsWithFiles,
  uploadTeacherTaskAttachment,
} from "../../store/slices/TaskSlice";
import { fetchSubjects } from "../../store/slices/SubjectSlice";
import { fetchAllStudents } from "../../store/slices/userSlice";
// Removed createAnnouncement import - backend now handles announcement creation
import { fetchAllOwnClasses } from "../../store/slices/ClassroomSlice";
import { DateTimeInput } from "../../components/ui/FormComponents";
import TaskAttachments from "../../components/task/TaskAttachments";
import { getErrorMessage } from "../../utils/helpers/errorHandler";

const TASK_MODES = {
  SINGLE_STUDENT: "for-student",
  CLASSROOM: "for-classroom", 
  MULTIPLE_STUDENTS: "for-multiple-students"
};

const TASK_STATUS_OPTIONS = [
  { value: "pending", label: "Pending" },
  { value: "in_progress", label: "In Progress" },
  { value: "completed", label: "Completed" }
];

const INITIAL_FORM_STATE = {
  name: "",
  description: "",
  status: "pending",
  deadline: "",
  accept_after_deadline: false,
  subject_id: "",
  student_id: "",
  student_ids: [],
  classroom_id: "",
};

const HARDCODED_CLASSROOMS = [
  { id: 1, name: "Class A", description: "Advanced Mathematics Class" },
];

function CreateTask({ onClose, defaultClassroomId = null }) {
  const dispatch = useDispatch();
  const { classrooms, loading: classroomsLoading, error: classroomsError } = useSelector((state) => state.classroom);
  
  // Local state
  const [mode, setMode] = useState(TASK_MODES.SINGLE_STUDENT);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState(INITIAL_FORM_STATE);
  const [subjects, setSubjects] = useState([]);
  const [createdTaskId, setCreatedTaskId] = useState(null);
  const [showAttachments, setShowAttachments] = useState(false);
  const [attachmentFiles, setAttachmentFiles] = useState([]);
  const [subjectsLoading, setSubjectsLoading] = useState(true);
  const [subjectsError, setSubjectsError] = useState(null);
  const [students, setStudents] = useState([]);
  const [studentsLoading, setStudentsLoading] = useState(true);
  const [studentsError, setStudentsError] = useState(null);

  // Set default subject when component mounts
  useEffect(() => {
    if (subjects.length > 0 && !formData.subject_id) {
      setFormData(prev => ({ ...prev, subject_id: subjects[0].id }));
    }
  }, [subjects, formData.subject_id]);

  // Set default student when students load
  useEffect(() => {
    if (students.length > 0 && !formData.student_id && mode === TASK_MODES.SINGLE_STUDENT) {
      setFormData(prev => ({ ...prev, student_id: students[0].id }));
    }
  }, [students, formData.student_id, mode]);

  // Fetch subjects on mount
  useEffect(() => {
    const loadSubjects = async () => {
      try {
        setSubjectsLoading(true);
        setSubjectsError(null);
        const result = await dispatch(fetchSubjects()).unwrap();
        const subjectsData = result.subjects || result || [];
        setSubjects(subjectsData);
      } catch (error) {
        console.error("Failed to fetch subjects:", error);
        setSubjectsError("Failed to load subjects");
      } finally {
        setSubjectsLoading(false);
      }
    };

    loadSubjects();
  }, []); // Only run once on mount

  // Fetch students on mount
  useEffect(() => {
    const loadStudents = async () => {
      try {
        setStudentsLoading(true);
        setStudentsError(null);
        const result = await dispatch(fetchAllStudents()).unwrap();
        const studentsData = result.students || result || [];
        setStudents(studentsData);
      } catch (error) {
        console.error("Failed to fetch students:", error);
        setStudentsError("Failed to load students");
      } finally {
        setStudentsLoading(false);
      }
    };

    loadStudents();
  }, []); // Only run once on mount

  // Fetch classrooms on mount
  useEffect(() => {
    if (!classrooms || classrooms.length === 0) {
      dispatch(fetchAllOwnClasses());
    }
  }, [dispatch, classrooms]);

  // Set default classroom when classrooms load or defaultClassroomId is provided
  useEffect(() => {
    if (defaultClassroomId && !formData.classroom_id) {
      // Use the provided default classroom ID and set mode to classroom
      setFormData(prev => ({ ...prev, classroom_id: defaultClassroomId }));
      setMode(TASK_MODES.CLASSROOM);
    } else if (classrooms && classrooms.length > 0 && !formData.classroom_id && !defaultClassroomId) {
      // Fallback to first classroom if no default is provided
      setFormData(prev => ({ ...prev, classroom_id: classrooms[0].id }));
    }
  }, [classrooms, formData.classroom_id, defaultClassroomId]);

  // Event handlers
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  const handleMultiStudentChange = (e) => {
    const selectedOptions = Array.from(e.target.selectedOptions);
    const selectedIds = selectedOptions.map(option => option.value);
    setFormData(prev => ({ ...prev, student_ids: selectedIds }));
  };

  const handleModeChange = (newMode) => {
    setMode(newMode);
    // Reset student selection when mode changes
    setFormData(prev => ({
      ...prev,
      student_id: "",
      student_ids: []
    }));
  };

  // Handle file selection
  const handleFileSelection = (e) => {
    const files = Array.from(e.target.files);
    const validFiles = [];
    const errors = [];

    files.forEach(file => {
      // Validate file size (25MB limit)
      if (file.size > 25 * 1024 * 1024) {
        errors.push(`${file.name}: File size exceeds 25MB limit`);
        return;
      }

      // Validate file type
      const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'ppt', 'pptx', 'xls', 'xlsx', 'zip'];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        errors.push(`${file.name}: File type not supported`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      alert('Some files could not be added:\n' + errors.join('\n'));
    }

    setAttachmentFiles(prev => [...prev, ...validFiles]);
    // Clear the input
    e.target.value = '';
  };

  // Remove file from selection
  const removeFile = (index) => {
    setAttachmentFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Validation
  const validateForm = () => {
    if (!formData.name.trim()) {
      throw new Error("Task name is required");
    }
    if (!classrooms || classrooms.length === 0) {
      throw new Error("No classroom found. Please create a classroom first.");
    }
    if (!formData.subject_id) {
      throw new Error("Please select a subject.");
    }
    if (mode === TASK_MODES.SINGLE_STUDENT && !formData.student_id) {
      throw new Error("Please select a student.");
    }
    if (mode === TASK_MODES.MULTIPLE_STUDENTS && (!formData.student_ids || formData.student_ids.length === 0)) {
      throw new Error("Please select at least one student.");
    }
    if (!formData.deadline) {
      throw new Error("Please set a deadline.");
    }
    if (!formData.classroom_id) {
      throw new Error("No classroom selected.");
    }
  };

  // Submit handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      validateForm();
    } catch (error) {
      alert(error.message);
      return;
    }

    setIsSubmitting(true);

    try {
      let result;

      // Check if we have files to upload
      const hasFiles = attachmentFiles && attachmentFiles.length > 0;

      if (hasFiles) {
        // Use file upload endpoints
        const formDataPayload = new FormData();

        // Add basic task data
        formDataPayload.append('name', formData.name);
        formDataPayload.append('description', formData.description || '');
        formDataPayload.append('deadline', formData.deadline || '');
        formDataPayload.append('accept_after_deadline', formData.accept_after_deadline);
        formDataPayload.append('subject_id', formData.subject_id || '');
        formDataPayload.append('chapter_ids', JSON.stringify([]));
        formDataPayload.append('topic_ids', JSON.stringify([]));
        formDataPayload.append('subtopic_ids', JSON.stringify([]));

        // Add mode-specific data
        switch (mode) {
          case TASK_MODES.SINGLE_STUDENT:
            formDataPayload.append('student_id', formData.student_id);
            break;
          case TASK_MODES.CLASSROOM:
            formDataPayload.append('classroom_id', formData.classroom_id);
            break;
          case TASK_MODES.MULTIPLE_STUDENTS:
            formDataPayload.append('student_ids', JSON.stringify(formData.student_ids));
            break;
        }

        // Add files
        attachmentFiles.forEach((file) => {
          formDataPayload.append('files', file);
        });

        // Call appropriate file upload endpoint
        switch (mode) {
          case TASK_MODES.SINGLE_STUDENT:
            result = await dispatch(createTaskForStudentWithFiles(formDataPayload)).unwrap();
            break;
          case TASK_MODES.CLASSROOM:
            result = await dispatch(createTaskForClassroomWithFiles(formDataPayload)).unwrap();
            break;
          case TASK_MODES.MULTIPLE_STUDENTS:
            result = await dispatch(createTaskForMultipleStudentsWithFiles(formDataPayload)).unwrap();
            break;
          default:
            throw new Error("Invalid task mode");
        }
      } else {
        // Use regular endpoints without files
        const payload = {
          ...formData,
          classroom_id: formData.classroom_id
        };

        switch (mode) {
          case TASK_MODES.SINGLE_STUDENT:
            result = await dispatch(createTaskForStudent(payload)).unwrap();
            break;
          case TASK_MODES.CLASSROOM:
            result = await dispatch(createTaskForClassroom(payload)).unwrap();
            break;
          case TASK_MODES.MULTIPLE_STUDENTS:
            result = await dispatch(createTaskForMultipleStudents(payload)).unwrap();
            break;
          default:
            throw new Error("Invalid task mode");
        }
      }

      console.log("Task created successfully:", result);

      // Handle response - file upload endpoints return { task, uploaded_files }
      const taskData = result.task || result;
      const uploadedFiles = result.uploaded_files || [];

      if (taskData && taskData.id) {
        setCreatedTaskId(taskData.id);
        setShowAttachments(true);

        if (uploadedFiles.length > 0) {
          console.log('Files uploaded successfully:', uploadedFiles);
        }

        // Close modal after a short delay to show success
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        // If no task ID returned, close the modal
        onClose();
      }
    } catch (error) {
      const errorMessage = error?.message || error?.detail || "Failed to create task";
      alert(`Error: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state for classrooms, subjects, and students
  if (classroomsLoading || !classrooms || classrooms.length === 0 || subjectsLoading || studentsLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
        <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mb-4"></div>
          <div className="text-lg text-gray-700 dark:text-gray-200">
            {classroomsLoading || !classrooms || classrooms.length === 0 ? "Loading classrooms..." : subjectsLoading ? "Loading subjects..." : "Loading students..."}
          </div>
        </div>
      </div>
    );
  }

  // Error state for classrooms, subjects, or students
  if (classroomsError || subjectsError || studentsError) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
        <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl flex flex-col items-center">
          <div className="text-lg text-red-600 dark:text-red-400 font-semibold mb-2">
            Failed to load data
          </div>
          <div className="text-gray-700 dark:text-gray-200 text-sm mb-4">
            {getErrorMessage(classroomsError || subjectsError || studentsError)}
          </div>
          <button 
            className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/30 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative z-10 w-full max-w-xl bg-white dark:bg-gray-800 p-6 rounded-xl shadow-xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-violet-700 dark:text-violet-400">
            Create Task
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 text-2xl font-bold transition"
            disabled={isSubmitting}
          >
            &times;
          </button>
        </div>

        {/* Mode Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Task Type
          </label>
          <select
            value={mode}
            onChange={(e) => handleModeChange(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
            disabled={isSubmitting || showAttachments}
          >
            <option value={TASK_MODES.SINGLE_STUDENT}>For Single Student</option>
            <option value={TASK_MODES.CLASSROOM}>For Classroom</option>
            <option value={TASK_MODES.MULTIPLE_STUDENTS}>For Multiple Students</option>
          </select>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Task Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Task Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter task name"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 disabled:opacity-50"
              required
              disabled={isSubmitting || showAttachments}
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter task description"
              rows={3}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 disabled:opacity-50"
              disabled={isSubmitting || showAttachments}
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
              disabled={isSubmitting || showAttachments}
            >
              {TASK_STATUS_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Deadline */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Deadline *
            </label>
            <DateTimeInput
              name="deadline"
              value={formData.deadline}
              onChange={handleInputChange}
              className="w-full"
              inputClassName="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
              required
              disabled={isSubmitting || showAttachments}
            />
          </div>

          {/* Accept After Deadline */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="accept_after_deadline"
              checked={formData.accept_after_deadline}
              onChange={handleInputChange}
              className="accent-violet-600 dark:accent-violet-400 disabled:opacity-50"
              disabled={isSubmitting || showAttachments}
            />
            <label className="text-sm text-gray-700 dark:text-gray-300">
              Accept submissions after deadline
            </label>
          </div>

          {/* Subject Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Subject *
            </label>
            <select
              name="subject_id"
              value={formData.subject_id}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
              required
              disabled={isSubmitting || showAttachments}
            >
              <option value="">Select a subject</option>
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>
                  {subject.name}
                </option>
              ))}
            </select>
          </div>

          {/* Classroom Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Classroom *
            </label>
            <select
              name="classroom_id"
              value={formData.classroom_id}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
              required
              disabled={isSubmitting || showAttachments}
            >
              <option value="">Select a classroom</option>
              {classrooms.map(classroom => (
                <option key={classroom.id} value={classroom.id}>
                  {classroom.name}
                </option>
              ))}
            </select>
          </div>

          {/* Student Selection */}
          {mode === TASK_MODES.SINGLE_STUDENT && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Student *
              </label>
              <select
                name="student_id"
                value={formData.student_id}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50"
                required
                disabled={isSubmitting || showAttachments}
              >
                <option value="">Select a student</option>
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.username ?? student.email}
                  </option>
                ))}
              </select>
            </div>
          )}

          {mode === TASK_MODES.MULTIPLE_STUDENTS && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Students * (Hold Ctrl/Cmd to select multiple)
              </label>
              <select
                name="student_ids"
                multiple
                value={formData.student_ids}
                onChange={handleMultiStudentChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 min-h-[120px]"
                required
                disabled={isSubmitting || showAttachments}
              >
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.username ?? student.email}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Attachments Section - Always visible */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Attachments (Optional)
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Add files to help students with this assignment. Files will be uploaded after the task is created.
            </p>

            {!createdTaskId ? (
              // Before task creation - show file selection
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                  <div className="text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <div className="mt-4">
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900 dark:text-gray-100">
                          Select files to attach
                        </span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          multiple
                          className="sr-only"
                          onChange={handleFileSelection}
                          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.ppt,.pptx,.xls,.xlsx,.zip"
                        />
                      </label>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        PDF, DOC, images, presentations up to 25MB each
                      </p>
                    </div>
                  </div>
                </div>

                {/* Selected Files Preview */}
                {attachmentFiles.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Selected Files ({attachmentFiles.length})
                    </h4>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {attachmentFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="flex items-center space-x-2">
                            <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                            </svg>
                            <span className="text-sm text-gray-900 dark:text-gray-100 truncate">
                              {file.name}
                            </span>
                            <span className="text-xs text-gray-500">
                              ({(file.size / 1024 / 1024).toFixed(1)} MB)
                            </span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700 text-sm"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // After task creation - show TaskAttachments component
              <TaskAttachments
                taskId={createdTaskId}
                attachmentType="task"
                canUpload={true}
                canDelete={true}
                maxFileSize={25}
                allowedTypes={['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'ppt', 'pptx', 'xls', 'xlsx', 'zip']}
                className="mb-4"
              />
            )}
          </div>



          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg font-semibold shadow hover:bg-gray-400 dark:hover:bg-gray-600 transition disabled:opacity-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-violet-600 text-white rounded-lg font-semibold shadow hover:bg-violet-700 transition flex items-center justify-center disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {attachmentFiles.length > 0 ? 'Creating & Uploading...' : 'Creating...'}
                </>
              ) : (
                `Create Assignment${attachmentFiles.length > 0 ? ` (${attachmentFiles.length} files)` : ''}`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default CreateTask;
