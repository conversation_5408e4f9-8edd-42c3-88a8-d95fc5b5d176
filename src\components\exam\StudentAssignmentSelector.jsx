import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchTeacherStudents } from '../../store/slices/ClassroomSlice';
import { FiUsers, FiUser, FiSearch, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';

const StudentAssignmentSelector = ({
  assignmentType,
  onAssignmentTypeChange = () => {},
  selectedStudentIds = [],
  onSelectedStudentsChange = () => {},
  classId,
  onClassIdChange = () => {},
  classrooms = [],
  themeClasses = {
    bg: 'bg-white',
    text: 'text-gray-900',
    input: 'bg-gray-50 text-gray-900 border-gray-300',
    label: 'text-gray-700'
  },
  disabled = false
}) => {
  const dispatch = useDispatch();
  const { teacherStudents, loading } = useSelector(state => state.classroom);

  const [searchTerm, setSearchTerm] = useState('');
  const [showStudentList, setShowStudentList] = useState(false);

  // Ensure themeClasses has all required properties
  const safeThemeClasses = {
    bg: themeClasses?.bg || 'bg-white',
    text: themeClasses?.text || 'text-gray-900',
    input: themeClasses?.input || 'bg-gray-50 text-gray-900 border-gray-300',
    label: themeClasses?.label || 'text-gray-700'
  };

  // Fetch teacher's students when component mounts
  useEffect(() => {
    if (assignmentType === 'students') {
      dispatch(fetchTeacherStudents({ limit: 100 }));
    }
  }, [dispatch, assignmentType]);

  // Filter students based on search term
  const filteredStudents = useMemo(() => {
    if (!teacherStudents || !Array.isArray(teacherStudents)) return [];
    
    return teacherStudents.filter(student =>
      student.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [teacherStudents, searchTerm]);

  // Handle assignment type change
  const handleAssignmentTypeChange = (type) => {
    if (typeof onAssignmentTypeChange === 'function') {
      onAssignmentTypeChange(type);
    }
    if (type === 'classroom') {
      if (typeof onSelectedStudentsChange === 'function') {
        onSelectedStudentsChange([]);
      }
      setShowStudentList(false);
    } else {
      setShowStudentList(true);
    }
  };

  // Handle student selection
  const handleStudentToggle = (studentId) => {
    if (typeof onSelectedStudentsChange !== 'function') return;

    const isSelected = selectedStudentIds.includes(studentId);
    if (isSelected) {
      onSelectedStudentsChange(selectedStudentIds.filter(id => id !== studentId));
    } else {
      onSelectedStudentsChange([...selectedStudentIds, studentId]);
    }
  };

  // Handle select all students
  const handleSelectAll = () => {
    if (typeof onSelectedStudentsChange !== 'function') return;

    if (selectedStudentIds.length === filteredStudents.length) {
      onSelectedStudentsChange([]);
    } else {
      onSelectedStudentsChange(filteredStudents.map(student => student.id));
    }
  };

  return (
    <div className="space-y-4">
      {/* Assignment Type Selection */}
      <div>
        <label className={`block mb-3 font-medium ${safeThemeClasses.label} flex items-center gap-2`}>
          <FiUsers className="w-4 h-4" />
          Assignment Type <span className="text-red-500">*</span>
        </label>
        
        <div className="space-y-3">
          {/* Assign to Whole Class Option */}
          <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            assignmentType === 'classroom' 
              ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-violet-300'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
            <input
              type="radio"
              name="assignmentType"
              value="classroom"
              checked={assignmentType === 'classroom'}
              onChange={() => handleAssignmentTypeChange('classroom')}
              disabled={disabled}
              className="w-4 h-4 text-violet-600 focus:ring-violet-500"
            />
            <div className="ml-3">
              <div className="flex items-center gap-2">
                <FiUsers className="w-4 h-4 text-violet-600" />
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Assign to Entire Class
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                All students in the selected classroom will receive this exam
              </p>
            </div>
          </label>

          {/* Assign to Specific Students Option */}
          <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
            assignmentType === 'students' 
              ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-violet-300'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
            <input
              type="radio"
              name="assignmentType"
              value="students"
              checked={assignmentType === 'students'}
              onChange={() => handleAssignmentTypeChange('students')}
              disabled={disabled}
              className="w-4 h-4 text-violet-600 focus:ring-violet-500"
            />
            <div className="ml-3">
              <div className="flex items-center gap-2">
                <FiUser className="w-4 h-4 text-violet-600" />
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Assign to Specific Students
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Choose individual students from your classes to receive this exam
              </p>
            </div>
          </label>
        </div>
      </div>

      {/* Classroom Selection */}
      {assignmentType === 'classroom' && (
        <div>
          <label className={`block mb-3 font-medium ${safeThemeClasses.label} flex items-center gap-2`}>
            <FiUsers className="w-4 h-4" />
            Select Classroom <span className="text-red-500">*</span>
          </label>

          <select
            value={classId || ''}
            onChange={(e) => {
              if (typeof onClassIdChange === 'function') {
                onClassIdChange(e.target.value);
              }
            }}
            disabled={disabled}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${safeThemeClasses.input} ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <option value="">Select a classroom...</option>
            {classrooms.map((classroom) => (
              <option key={classroom.id} value={classroom.id}>
                {classroom.name || `Class ${classroom.class_number} - ${classroom.subject?.name || 'Unknown Subject'}`}
              </option>
            ))}
          </select>

          {assignmentType === 'classroom' && !classId && (
            <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg mt-3">
              <FiX className="w-4 h-4" />
              <span className="text-sm font-medium">Please select a classroom to assign the exam to.</span>
            </div>
          )}
        </div>
      )}

      {/* Student Selection List */}
      {assignmentType === 'students' && (
        <div className={`border-2 border-gray-300 dark:border-gray-600 rounded-lg ${safeThemeClasses.bg}`}>
          {/* Search Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">
                Select Students ({selectedStudentIds.length} selected)
              </h3>
              {filteredStudents.length > 0 && (
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="text-sm text-violet-600 hover:text-violet-700 font-medium"
                  disabled={disabled}
                >
                  {selectedStudentIds.length === filteredStudents.length ? 'Deselect All' : 'Select All'}
                </button>
              )}
            </div>
            
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search students by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={disabled}
                className={`w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg ${safeThemeClasses.input} focus:ring-2 focus:ring-violet-500 focus:border-transparent`}
              />
            </div>
          </div>

          {/* Student List */}
          <div className="max-h-64 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                Loading students...
              </div>
            ) : filteredStudents.length > 0 ? (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredStudents.map((student) => (
                  <label
                    key={student.id}
                    className={`flex items-center p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                      disabled ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedStudentIds.includes(student.id)}
                      onChange={() => handleStudentToggle(student.id)}
                      disabled={disabled}
                      className="w-4 h-4 text-violet-600 focus:ring-violet-500 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {student.username}
                        </span>
                        {selectedStudentIds.includes(student.id) && (
                          <FiCheck className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {student.email}
                      </p>
                    </div>
                  </label>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? 'No students found matching your search.' : 'No students available.'}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Validation Message */}
      {assignmentType === 'students' && selectedStudentIds.length === 0 && (
        <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
          <FiX className="w-4 h-4" />
          <span className="text-sm font-medium">Please select at least one student for the exam assignment.</span>
        </div>
      )}
    </div>
  );
};

export default StudentAssignmentSelector;
