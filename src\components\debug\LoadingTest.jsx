import React, { useState } from 'react';
import LoadingSpinner, { ChartSkeleton, CardSkeleton } from '../ui/LoadingSpinner';

/**
 * Loading Components Test
 * Tests all loading components to ensure they work properly
 */
const LoadingTest = () => {
  const [showLoading, setShowLoading] = useState(true);

  return (
    <div className="p-8 space-y-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          Loading Components Test
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Testing LoadingSpinner, ChartSkeleton, and CardSkeleton components
        </p>
        
        <button
          onClick={() => setShowLoading(!showLoading)}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {showLoading ? 'Hide Loading' : 'Show Loading'}
        </button>
      </div>

      {showLoading && (
        <>
          {/* Loading Spinner Tests */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Loading Spinner Variants
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Small Blue</h3>
                <LoadingSpinner 
                  size="sm" 
                  color="blue" 
                  text="Loading..." 
                  currentTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                />
              </div>
              
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Medium Green</h3>
                <LoadingSpinner 
                  size="md" 
                  color="green" 
                  text="Processing..." 
                  currentTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                />
              </div>
              
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Large Violet</h3>
                <LoadingSpinner 
                  size="lg" 
                  color="violet" 
                  text="Analyzing..." 
                  currentTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                />
              </div>
              
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">No Text</h3>
                <LoadingSpinner 
                  size="md" 
                  color="red" 
                  text="" 
                  currentTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                />
              </div>
            </div>
          </div>

          {/* Chart Skeleton Tests */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Chart Skeleton Variants
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Standard Chart</h3>
                <ChartSkeleton height="250px" />
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Tall Chart</h3>
                <ChartSkeleton height="300px" />
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Wide Chart</h3>
              <ChartSkeleton height="200px" width="100%" />
            </div>
          </div>

          {/* Card Skeleton Tests */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Card Skeleton Variants
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">3 Rows (Default)</h3>
                <CardSkeleton />
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">5 Rows</h3>
                <CardSkeleton rows={5} />
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">1 Row</h3>
                <CardSkeleton rows={1} />
              </div>
            </div>
          </div>
        </>
      )}

      {/* Success Message */}
      {!showLoading && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-xl p-8 text-center">
          <div className="text-green-600 dark:text-green-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-green-800 dark:text-green-200 mb-2">
            All Loading Components Working!
          </h3>
          <p className="text-green-600 dark:text-green-400">
            LoadingSpinner, ChartSkeleton, and CardSkeleton are all properly exported and functional.
          </p>
        </div>
      )}
    </div>
  );
};

export default LoadingTest;
