import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>Book<PERSON><PERSON>,
  FiUsers,
  FiEdit3,
  FiCheck,
  FiArrowRight,
  FiArrowLeft,
  FiSave,
  FiClock,
  FiTarget,
  FiSettings,
  FiCalendar
} from 'react-icons/fi';

// Import UI components
import { PageContainer, Card, Button } from '../ui';

// Import timezone utilities
import { validateUTCDateTime, logTimezoneInfo } from '../../utils/timezone';

// Import preserved question creation components
import QuestionFormManager from './QuestionFormManager';
import QuestionList from './QuestionList';

// Import Redux actions
import { createExamWithAssignment } from '../../store/slices/ExamSlice';
import { fetchChaptersBySubject } from '../../store/slices/ChapterSlice';
import { fetchTopicsByChapter } from '../../store/slices/TopicSlice';
import { fetchSubtopicsByTopic } from '../../store/slices/SubtopicSlice';
import { aiGenerateQuestions } from '../../store/slices/QuestionSlice';
import CustomDateTimePicker from '../ui/CustomDateTimePicker';
import useTimezone from '../../hooks/useTimezone';
import ExamAssignment from './ExamAssignment';

const ModernExamCreator = ({ userType = 'teacher', examId, isEditing = false }) => {
  // Note: examId and isEditing are kept for future edit functionality
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state with fallback values
  const subjects = useSelector(state => state.subjects?.subjects || []);
  const { classes = [] } = useSelector(state => state.classes || {});
  const { classrooms = [] } = useSelector(state => state.classroom || {});

  // Debug logging
  console.log('🔍 ModernExamCreator - Redux state debug:', {
    subjects,
    classes,
    classrooms,
    subjectsState: useSelector(state => state.subjects)
  });
  const {
    chaptersBySubject = [],
    loading: chaptersLoading = false
  } = useSelector(state => state.chapters || {});
  const {
    topicsByChapter = [],
    loading: topicsLoading = false
  } = useSelector(state => state.topics || {});
  const {
    subtopicsByTopic = [],
    loading: subtopicsLoading = false
  } = useSelector(state => state.subtopics || {});

  // Main state
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Exam data state
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    subjectId: '',
    classNumber: '',
    classroomId: '',
    startDateTimeUtc: '',
    duration: 60,
    totalMarks: 100
  });

  // Assignment state
  const [assignmentType, setAssignmentType] = useState('classroom');
  const [selectedStudentIds, setSelectedStudentIds] = useState([]);

  // Questions state
  const [questions, setQuestions] = useState([]);

  // Question categorization state
  const [subjectId, setSubjectId] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [topicId, setTopicId] = useState('');
  const [subtopicId, setSubtopicId] = useState('');

  // Theme classes
  const themeClasses = {
    input: 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100',
    label: 'text-gray-700 dark:text-gray-300',
    button: 'bg-blue-600 hover:bg-blue-700 text-white',
    card: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
    text: 'text-gray-900 dark:text-gray-100',
    bg: 'bg-white dark:bg-gray-800'
  };

  // Steps configuration
  const steps = [
    { id: 1, title: 'Exam Details', icon: FiBookOpen, description: 'Basic exam information' },
    { id: 2, title: 'Assignment', icon: FiUsers, description: 'Who takes this exam' },
    { id: 3, title: 'Questions', icon: FiEdit3, description: 'Add exam questions' },
    { id: 4, title: 'Review', icon: FiCheck, description: 'Final review and submit' }
  ];

  // Data fetching effects
  useEffect(() => {
    if (subjectId) {
      dispatch(fetchChaptersBySubject({ subjectId }));
    }
  }, [dispatch, subjectId]);

  useEffect(() => {
    if (chapterId) {
      dispatch(fetchTopicsByChapter({ chapterId }));
    }
  }, [dispatch, chapterId]);

  useEffect(() => {
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId }));
    }
  }, [dispatch, topicId]);

  // Handlers
  const handleExamDataChange = useCallback((field, value) => {
    setExamData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSubjectChange = useCallback((newSubjectId) => {
    setSubjectId(newSubjectId);
    setChapterId('');
    setTopicId('');
    setSubtopicId('');
    handleExamDataChange('subjectId', newSubjectId);
  }, [handleExamDataChange]);

  const handleClassNumberChange = useCallback((classNumber) => {
    handleExamDataChange('classNumber', classNumber);
  }, [handleExamDataChange]);

  const handleClassChange = useCallback((classroomId) => {
    handleExamDataChange('classroomId', classroomId);
  }, [handleExamDataChange]);

  const handleQuestionAdd = useCallback((newQuestion) => {
    setQuestions(prev => [...prev, newQuestion]);
  }, []);

  const handleQuestionsChange = useCallback((updatedQuestions) => {
    setQuestions(updatedQuestions);
  }, []);

  const handleAIGenerate = useCallback(async (aiParams) => {
    try {
      setIsSubmitting(true);
      
      // Find subject and chapter names
      const subject = subjects?.find(s => s.id === subjectId);
      const chapter = chaptersBySubject?.find(c => c.id === chapterId);
      const topic = topicsByChapter?.find(t => t.id === topicId);
      const subtopic = subtopicsByTopic?.find(st => st.id === subtopicId);

      const payload = {
        class: examData.classNumber || "10",
        subject: subject?.name || "",
        chapter: chapter?.name || "",
        topic: topic?.name || "",
        subtopic: subtopic?.name || "",
        no_of_questions: aiParams.aiNoOfQuestions || 3,
        ...(aiParams.aiDifficultyMode === 'custom' && {
          no_of_easy: aiParams.aiNoOfEasy,
          no_of_medium: aiParams.aiNoOfMedium,
          no_of_hard: aiParams.aiNoOfHard
        })
      };

      const result = await dispatch(aiGenerateQuestions(payload)).unwrap();

      if (result && result.questions && result.questions.length > 0) {
        const newQuestions = result.questions.map((question, index) => ({
          ...question,
          id: Date.now() + index,
          subject_id: subjectId,
          chapter_id: chapterId,
          topic_id: topicId || null,
          subtopic_id: subtopicId || null,
        }));

        setQuestions(prev => [...prev, ...newQuestions]);
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      setSubmitError(error.message || 'Failed to generate questions');
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, examData.classNumber, subjectId, chapterId, topicId, subtopicId, subjects, chaptersBySubject, topicsByChapter, subtopicsByTopic]);

  // Navigation handlers with auto-save and validation
  const handleNext = () => {
    const validation = getStepValidation(currentStep);
    if (validation.isValid && currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
      setSubmitError(''); // Clear any previous errors
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setSubmitError(''); // Clear any previous errors
    }
  };

  // Auto-calculate total marks when questions change
  useEffect(() => {
    const totalMarks = questions.reduce((sum, q) => sum + (parseInt(q.marks) || 1), 0);
    if (totalMarks !== examData.totalMarks) {
      handleExamDataChange('totalMarks', totalMarks);
    }
  }, [questions, examData.totalMarks, handleExamDataChange]);

  // Submit handler
  const handleSubmitExam = useCallback(async () => {
    try {
      setIsSubmitting(true);
      setSubmitError('');

      // Format questions for API
      const selectedClassObj = Array.isArray(classes)
        ? classes.find(c => c.ClassNo === examData.classNumber || c.class_number === parseInt(examData.classNumber))
        : null;

      // Helper function to map question types to backend-accepted values
      const mapQuestionType = (type) => {
        const normalizedType = String(type || '').toUpperCase();

        const typeMap = {
          'MCQS': 'MCQS', 'SHORT': 'SHORT', 'LONG': 'LONG',
          'MCQ': 'MCQS', 'MULTIPLE_CHOICE': 'MCQS',
          'SHORT_ANSWER': 'SHORT', 'LONG_ANSWER': 'LONG',
          'DESCRIPTIVE': 'LONG', 'DESCRIPTION': 'LONG', 'ESSAY': 'LONG',
          'TEXT': 'SHORT', 'TEXTUAL': 'SHORT'
        };

        const mappedType = typeMap[normalizedType];
        if (normalizedType !== mappedType) {
          console.log(`🔄 Question type mapped: ${type} → ${mappedType}`);
        }
        return mappedType || 'MCQS';
      };

      const formattedQuestions = questions.map(raw => {
        const originalType = raw.type || raw.Type || 'MCQS';
        const questionType = mapQuestionType(originalType); // ✅ Use mapped type

        // Normalize options to required shape
        const normalizedOptions = Array.isArray(raw.options)
          ? raw.options.map(opt => ({
              option_text: opt.option_text ?? opt.text ?? opt.value ?? '',
              is_correct: !!(opt.is_correct ?? opt.correct ?? false),
            }))
          : [];

        // Derive answer: for MCQS pick the correct option text; for DESCRIPTIVE use provided answer string
        const derivedAnswer = questionType === 'MCQS'
          ? (normalizedOptions.find(o => o.is_correct)?.option_text || raw.correct_answer || raw.answer || '')
          : (raw.answer || '');

        return {
          text: raw.text,
          answer: derivedAnswer,
          Type: questionType, // ✅ Use mapped type
          Level: (raw.Level || 'EASY').toUpperCase(),
          imageUrl: raw.imageUrl || undefined,
          class_id: selectedClassObj?.id || raw.class_id || null,
          subject_id: raw.subject_id || null,
          chapter_id: raw.chapter_id || null,
          topic_id: raw.topic_id || null,
          subtopic_id: raw.subtopic_id || null,
          marks: parseInt(raw.marks) || 1,
          options: questionType === 'MCQS' ? normalizedOptions : undefined,
        };
      });

      // Calculate total marks
      const totalMarks = formattedQuestions.reduce((sum, q) => sum + (parseInt(q.marks) || 1), 0);

      // Validate and prepare start_time in UTC
      const startTimeUTC = examData.startDateTimeUtc || new Date().toISOString();

      // DEBUG: Log the exact values being processed
      console.log('🔍 EXAM CREATION DEBUG:', {
        rawStartTime: examData.startDateTimeUtc,
        processedStartTime: startTimeUTC,
        userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        localDisplay: new Date(startTimeUTC).toLocaleString(),
        isUTCFormat: startTimeUTC.endsWith('Z') || startTimeUTC.includes('+')
      });

      // Validate timezone and log information
      const validation = validateUTCDateTime(startTimeUTC);
      logTimezoneInfo('ModernExamCreator', startTimeUTC, {
        examTitle: examData.title,
        isCreating: !isEditing
      });

      // Use corrected value if validation provided one
      const finalStartTime = validation.correctedValue || startTimeUTC;

      // Prepare exam payload
      const examPayload = {
        title: examData.title,
        description: examData.description,
        total_marks: totalMarks,
        total_duration: parseInt(examData.duration) || 60,
        start_time: finalStartTime, // Use validated and corrected UTC time
        questions: formattedQuestions,
        assignment: assignmentType === 'classroom'
          ? { classroom_id: examData.classroomId }
          : { student_ids: selectedStudentIds }
      };

      await dispatch(createExamWithAssignment(examPayload)).unwrap();
      navigate('/teacher/exams');
    } catch (error) {
      console.error('Failed to create exam:', error);
      setSubmitError(error.message || 'Failed to create exam. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, navigate, examData, questions, assignmentType, selectedStudentIds]);

  // Enhanced validation with detailed feedback
  const getStepValidation = (step) => {
    switch (step) {
      case 1:
        const errors = [];
        if (!examData.title?.trim()) errors.push('Exam title is required');
        if (!examData.subjectId) errors.push('Subject selection is required');
        if (!examData.classNumber) errors.push('Class/Grade selection is required');
        if (!examData.duration || examData.duration < 1) errors.push('Valid duration is required');
        if (!examData.startDateTimeUtc) errors.push('Start date & time is required');
        return { isValid: errors.length === 0, errors };

      case 2:
        const assignmentErrors = [];
        if (assignmentType === 'classroom' && !examData.classroomId) {
          assignmentErrors.push('Classroom selection is required');
        }
        if (assignmentType === 'students' && selectedStudentIds.length === 0) {
          assignmentErrors.push('At least one student must be selected');
        }
        return { isValid: assignmentErrors.length === 0, errors: assignmentErrors };

      case 3:
        const questionErrors = [];
        if (questions.length === 0) {
          questionErrors.push('At least one question is required');
        }
        // Validate each question
        questions.forEach((q, index) => {
          if (!q.text?.trim()) {
            questionErrors.push(`Question ${index + 1}: Question text is required`);
          }
          const qt = (q.type || q.Type || '').toUpperCase();
          if (qt === 'MCQS') {
            const validOptions = q.options?.filter(opt => opt.option_text?.trim()) || [];
            if (validOptions.length < 2) {
              questionErrors.push(`Question ${index + 1}: At least 2 options are required`);
            }
            const hasCorrectAnswer = validOptions.some(opt => opt.is_correct);
            if (!hasCorrectAnswer) {
              questionErrors.push(`Question ${index + 1}: Correct answer must be selected`);
            }
          }
          // Required IDs
          if (!q.subject_id) questionErrors.push(`Question ${index + 1}: subject_id is required`);
          if (!q.chapter_id) questionErrors.push(`Question ${index + 1}: chapter_id is required`);
        });
        return { isValid: questionErrors.length === 0, errors: questionErrors };

      default:
        return { isValid: true, errors: [] };
    }
  };

  const isStepValid = (step) => getStepValidation(step).isValid;

  return (
    <PageContainer>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {isEditing ? 'Edit Exam' : 'Create New Exam'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Build a comprehensive exam with questions, assignments, and settings
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                  currentStep === step.id
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : currentStep > step.id
                    ? 'bg-green-600 border-green-600 text-white'
                    : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <FiCheck className="w-5 h-5" />
                  ) : (
                    <step.icon className="w-5 h-5" />
                  )}
                </div>
                <div className="ml-4 min-w-0 flex-1">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {step.description}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-green-600' : 'bg-gray-300 dark:bg-gray-600'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Error Display */}
        {submitError && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
            <p className="text-red-700 dark:text-red-300">{submitError}</p>
          </div>
        )}

        {/* Step Content */}
        <div className="mb-8">
          {currentStep === 1 && (
            <ExamDetailsStep
              examData={examData}
              onExamDataChange={handleExamDataChange}
              subjects={subjects || []}
              classes={classes || []}
              onSubjectChange={handleSubjectChange}
              onClassNumberChange={handleClassNumberChange}
              themeClasses={themeClasses}
            />
          )}

          {currentStep === 2 && (
            <ExamAssignment
              assignmentType={assignmentType}
              onAssignmentTypeChange={setAssignmentType}
              classId={examData.classroomId}
              onClassChange={(e) => handleClassChange(e.target.value)}
              selectedStudentIds={selectedStudentIds}
              onSelectedStudentsChange={setSelectedStudentIds}
            />
          )}

          {currentStep === 3 && (
            <QuestionsStep
              subjects={subjects}
              subjectId={subjectId}
              setSubjectId={setSubjectId}
              chaptersBySubject={chaptersBySubject}
              topicsByChapter={topicsByChapter}
              subtopicsByTopic={subtopicsByTopic}
              chapterId={chapterId}
              topicId={topicId}
              subtopicId={subtopicId}
              setChapterId={setChapterId}
              setTopicId={setTopicId}
              setSubtopicId={setSubtopicId}
              chaptersLoading={chaptersLoading}
              topicsLoading={topicsLoading}
              subtopicsLoading={subtopicsLoading}
              themeClasses={themeClasses}
              userType={userType}
              gradeClasses={classes}
              onQuestionAdd={handleQuestionAdd}
              onAIGenerate={handleAIGenerate}
              isSubmitting={isSubmitting}
              questions={questions}
              onQuestionsChange={handleQuestionsChange}
            />
          )}

          {currentStep === 4 && (
            <ReviewStep
              examData={examData}
              questions={questions}
              assignmentType={assignmentType}
              classrooms={classrooms}
              subjects={subjects}
              classes={classes}
              themeClasses={themeClasses}
            />
          )}
        </div>

        {/* Validation Feedback */}
        {(() => {
          const validation = getStepValidation(currentStep);
          if (!validation.isValid && validation.errors.length > 0) {
            return (
              <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
                <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">
                  Please complete the following to continue:
                </h4>
                <ul className="text-sm text-yellow-700 dark:text-yellow-400 space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-1 h-1 bg-yellow-600 rounded-full"></span>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            );
          }
          return null;
        })()}

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            icon={FiArrowLeft}
          >
            Previous
          </Button>

          <div className="flex gap-3">
            {currentStep < steps.length ? (
              <Button
                onClick={handleNext}
                disabled={!isStepValid(currentStep)}
                icon={FiArrowRight}
                iconPosition="right"
              >
                Next Step
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handleSubmitExam}
                disabled={isSubmitting || !isStepValid(currentStep)}
                isLoading={isSubmitting}
                icon={FiSave}
              >
                {isSubmitting ? 'Creating Exam...' : 'Create Exam'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

// Step Components
const ExamDetailsStep = ({
  examData,
  onExamDataChange,
  subjects,
  classes,
  onSubjectChange,
  onClassNumberChange,
  themeClasses
}) => {
  console.log('🔍 ExamDetailsStep - Props debug:', { subjects, classes });
  
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Exam Details</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Exam Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={examData.title}
              onChange={(e) => onExamDataChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter exam title"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject <span className="text-red-500">*</span>
            </label>
            <select
              value={examData.subjectId}
              onChange={(e) => onSubjectChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Subject</option>
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>{subject.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Class/Grade <span className="text-red-500">*</span>
            </label>
            <select
              value={examData.classNumber}
              onChange={(e) => onClassNumberChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Select Class</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.ClassNo}>Grade {cls.ClassNo}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (minutes) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={examData.duration || ''}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '') {
                  onExamDataChange('duration', '');
                } else {
                  const numValue = parseInt(value);
                  if (!isNaN(numValue) && numValue >= 1 && numValue <= 300) {
                    onExamDataChange('duration', numValue);
                  }
                }
              }}
              onBlur={(e) => {
                if (e.target.value === '' || e.target.value === '0') {
                  onExamDataChange('duration', 60);
                }
              }}
              min={1}
              max={300}
              placeholder="60"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={examData.description}
              onChange={(e) => onExamDataChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the exam purpose and content"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date & Time
            </label>
            <CustomDateTimePicker
              value={examData.startDateTimeUtc}
              onChange={(e) => onExamDataChange('startDateTimeUtc', e.target.value)}
              placeholder="Select start date & time"
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const AssignmentStep = ({
  assignmentType,
  onAssignmentTypeChange,
  classroomId,
  onClassChange,
  selectedStudentIds = [], // eslint-disable-line no-unused-vars - Keep for future individual assignment feature
  onSelectedStudentsChange = () => {}, // eslint-disable-line no-unused-vars - Keep for future individual assignment feature
  classrooms,
  themeClasses
}) => (
  <Card className="p-8">
    <div className="mb-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-3">
        <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
          <FiUsers className="w-5 h-5 text-green-600 dark:text-green-400" />
        </div>
        Assignment
      </h2>
      <p className="text-gray-600 dark:text-gray-400">
        Choose who will take this exam
      </p>
    </div>

    <div className="space-y-6">
      {/* Assignment Type Selection */}
      <div>
        <label className={`block mb-4 font-medium ${themeClasses.label}`}>
          Assignment Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            type="button"
            onClick={() => onAssignmentTypeChange('classroom')}
            className={`p-6 rounded-xl border-2 transition-all duration-200 text-left ${
              assignmentType === 'classroom'
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
          >
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                assignmentType === 'classroom'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
              }`}>
                <FiUsers className="w-4 h-4" />
              </div>
              <h3 className={`font-semibold ${themeClasses.text}`}>Entire Classroom</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Assign to all students in a classroom
            </p>
          </button>

          <button
            type="button"
            onClick={() => onAssignmentTypeChange('individual')}
            className={`p-6 rounded-xl border-2 transition-all duration-200 text-left ${
              assignmentType === 'individual'
                ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
          >
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                assignmentType === 'individual'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
              }`}>
                <FiTarget className="w-4 h-4" />
              </div>
              <h3 className={`font-semibold ${themeClasses.text}`}>Individual Students</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Select specific students manually
            </p>
          </button>
        </div>
      </div>

      {/* Classroom Selection */}
      {assignmentType === 'classroom' && (
        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label}`}>
            Select Classroom <span className="text-red-500">*</span>
          </label>
          <select
            value={classroomId}
            onChange={(e) => onClassChange(e.target.value)}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            required
          >
            <option value="">Select a classroom</option>
            {classrooms.map(classroom => (
              <option key={classroom.id} value={classroom.id}>
                {classroom.name} ({classroom.student_count || 0} students)
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Individual Student Selection */}
      {assignmentType === 'individual' && (
        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label}`}>
            Select Students <span className="text-red-500">*</span>
          </label>
          <div className="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center">
            <p className="text-gray-500 dark:text-gray-400">
              Individual student selection will be implemented in the next update
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              For now, please use classroom assignment
            </p>
          </div>
        </div>
      )}
    </div>
  </Card>
);

const QuestionsStep = ({
  subjects,
  subjectId,
  setSubjectId,
  chaptersBySubject,
  topicsByChapter,
  subtopicsByTopic,
  chapterId,
  topicId,
  subtopicId,
  setChapterId,
  setTopicId,
  setSubtopicId,
  chaptersLoading,
  topicsLoading,
  subtopicsLoading,
  themeClasses,
  userType,
  gradeClasses,
  onQuestionAdd,
  onAIGenerate,
  isSubmitting,
  questions,
  onQuestionsChange
}) => (
  <div className="space-y-6">
    <Card className="p-8">
      <div className="mb-6">
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <FiEdit3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              Questions
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Add questions to your exam using AI generation or manual creation
            </p>
          </div>

          {/* Clean Total Marks Display */}
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <FiTarget className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {questions.reduce((sum, q) => sum + (parseInt(q.marks) || 1), 0)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Total Marks • {questions.length} question{questions.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Question Creation Form */}
      <QuestionFormManager
        subjects={subjects}
        subjectId={subjectId}
        setSubjectId={setSubjectId}
        chaptersBySubject={chaptersBySubject}
        topicsByChapter={topicsByChapter}
        subtopicsByTopic={subtopicsByTopic}
        chapterId={chapterId}
        topicId={topicId}
        subtopicId={subtopicId}
        setChapterId={setChapterId}
        setTopicId={setTopicId}
        setSubtopicId={setSubtopicId}
        chaptersLoading={chaptersLoading}
        topicsLoading={topicsLoading}
        subtopicsLoading={subtopicsLoading}
        themeClasses={themeClasses}
        userType={userType}
        gradeClasses={gradeClasses}
        onQuestionAdd={onQuestionAdd}
        onAIGenerate={onAIGenerate}
        isSubmitting={isSubmitting}
      />
    </Card>

    {/* Questions List */}
    <Card className="p-8">
      <QuestionList
        questions={questions}
        onQuestionsChange={onQuestionsChange}
        onEditQuestion={() => {}}
        onDeleteQuestion={(index) => {
          const updatedQuestions = questions.filter((_, i) => i !== index);
          onQuestionsChange(updatedQuestions);
        }}
        themeClasses={themeClasses}
      />
    </Card>
  </div>
);

const ReviewStep = ({
  examData,
  questions,
  assignmentType,
  classrooms,
  subjects,
  classes,
  themeClasses
}) => {
  const totalMarks = questions.reduce((sum, q) => sum + (parseInt(q.marks) || 1), 0);
  const selectedClassroom = classrooms?.find(c => c.id === examData.classroomId);
  const selectedSubject = subjects?.find(s => s.id === examData.subjectId);
  const selectedClass = classes?.find(c => c.ClassNo === examData.classNumber);

  return (
    <Card className="p-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-3">
          <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <FiCheck className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          Review & Submit
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Review your exam details before creating
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Exam Details */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Exam Details</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Title:</span>
                <span className={`font-medium ${themeClasses.text}`}>{examData.title}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Duration:</span>
                <span className={`font-medium ${themeClasses.text}`}>{examData.duration} minutes</span>
              </div>
              {examData.startDateTimeUtc && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Start Time:</span>
                  <span className={`font-medium ${themeClasses.text}`}>
                    {new Date(examData.startDateTimeUtc).toLocaleString()}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Subject:</span>
                <span className={`font-medium ${themeClasses.text}`}>{selectedSubject?.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Class:</span>
                <span className={`font-medium ${themeClasses.text}`}>{selectedClass?.ClassNo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Marks:</span>
                <span className={`font-medium ${themeClasses.text}`}>{totalMarks}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Assignment</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Type:</span>
                <span className={`font-medium ${themeClasses.text}`}>
                  {assignmentType === 'classroom' ? 'Entire Classroom' : 'Individual Students'}
                </span>
              </div>
              {assignmentType === 'classroom' && selectedClassroom && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Classroom:</span>
                  <span className={`font-medium ${themeClasses.text}`}>{selectedClassroom.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Questions Summary */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Questions Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Total Questions:</span>
              <span className={`font-medium ${themeClasses.text}`}>{questions.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Multiple Choice:</span>
              <span className={`font-medium ${themeClasses.text}`}>
                {questions.filter(q => q.type === 'MCQS').length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Descriptive:</span>
              <span className={`font-medium ${themeClasses.text}`}>
                {questions.filter(q => q.type === 'DESCRIPTIVE').length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Easy:</span>
              <span className={`font-medium ${themeClasses.text}`}>
                {questions.filter(q => q.Level === 'EASY').length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Medium:</span>
              <span className={`font-medium ${themeClasses.text}`}>
                {questions.filter(q => q.Level === 'MEDIUM').length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Hard:</span>
              <span className={`font-medium ${themeClasses.text}`}>
                {questions.filter(q => q.Level === 'HARD').length}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ModernExamCreator;
