# Exam Session Reconnection System - Integration Guide

## Overview

This guide provides comprehensive instructions for integrating the exam session reconnection system into your EduFair application. The system handles connection loss scenarios during exams and provides a seamless reconnection workflow for students, teachers, and administrators.

## Architecture

### Core Components

1. **Services Layer**
   - `ExamReconnectionService.js` - <PERSON>les reconnection API calls and status polling
   - `AdminSessionService.js` - Manages admin session monitoring and control

2. **Redux State Management**
   - `examReconnectionSlice.js` - Student reconnection state and actions
   - `examSessionAdminSlice.js` - Admin session management state

3. **UI Components**
   - Student reconnection components (request modal, status indicator, connection monitor)
   - Teacher reconnection management (dashboard, quick actions)
   - Admin session monitoring dashboard

## API Endpoints Integration

### Student Endpoints
```javascript
// Request reconnection
POST /api/exams/session/exam-session/{session_id}/request-reconnection
Body: { session_id: string, reason: string }

// Check reconnection status
GET /api/exams/session/exam-session/reconnection-status/{request_id}

// Resume exam session
GET /api/exams/session/exam-session/{session_id}/resume
```

### Teacher/Admin Endpoints
```javascript
// Get pending requests
GET /api/exams/session/admin/reconnection-requests

// Approve/deny request
POST /api/exams/session/admin/reconnection-request/{request_id}/approve
Body: { request_id: string, approved: boolean, reason: string }

// Admin session management
GET /api/exams/session/admin/exam-sessions/active
POST /api/exams/session/admin/exam-session/{session_id}/submit?reason={reason}
POST /api/exams/session/admin/exam-session/{session_id}/terminate?reason={reason}
```

## Integration Steps

### 1. Redux Store Setup

The Redux store has been updated to include the new slices:

```javascript
// src/store/index.js
import examReconnectionReducer from "./slices/exam/examReconnectionSlice";
import examSessionAdminReducer from "./slices/exam/examSessionAdminSlice";

const store = configureStore({
  reducer: {
    // ... existing reducers
    examReconnection: examReconnectionReducer,
    examSessionAdmin: examSessionAdminReducer,
  }
});
```

### 2. Student Exam Integration

The `StudentTakeExam` component has been wrapped with `ConnectionMonitor`:

```javascript
// src/pages/student/StudentTakeExam.jsx
import ConnectionMonitor from "../../components/exam/student/reconnection/ConnectionMonitor";

return (
  <ConnectionMonitor
    sessionId={examSession.sessionId}
    examTitle={currentExam.title}
    onReconnected={() => {
      dispatch(setConnectionStatus('connected'));
      showSuccess('Successfully reconnected to exam session');
    }}
  >
    {/* Existing exam content */}
  </ConnectionMonitor>
);
```

### 3. Teacher Dashboard Integration

Add reconnection management to teacher exam monitoring:

```javascript
// In teacher exam pages
import ReconnectionRequestsDashboard from "../components/exam/teacher/reconnection/ReconnectionRequestsDashboard";
import ReconnectionQuickActions from "../components/exam/teacher/reconnection/ReconnectionQuickActions";

// Full dashboard
<ReconnectionRequestsDashboard />

// Quick actions in header/toolbar
<ReconnectionQuickActions 
  examId={currentExamId} 
  onViewAll={() => navigate('/teacher/reconnection-requests')}
/>
```

### 4. Admin Dashboard Integration

Add session monitoring for administrators:

```javascript
// In admin pages
import AdminSessionMonitoringDashboard from "../components/exam/admin/AdminSessionMonitoringDashboard";

<AdminSessionMonitoringDashboard />
```

## Component Usage

### ConnectionMonitor

Automatically detects connection loss and handles reconnection workflow:

```javascript
<ConnectionMonitor
  sessionId="exam-session-id"
  examTitle="Mathematics Final Exam"
  onReconnected={() => {
    // Handle successful reconnection
    console.log('Reconnected successfully');
  }}
>
  {/* Your exam content */}
</ConnectionMonitor>
```

**Features:**
- Monitors online/offline status
- Detects WebSocket disconnections
- Heartbeat timeout detection
- Automatic reconnection requests
- Manual reconnection modal
- Status indicators and overlays

### ReconnectionRequestModal

Modal for students to request reconnection:

```javascript
<ReconnectionRequestModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  sessionId="exam-session-id"
  examTitle="Mathematics Final Exam"
  onReconnected={() => {
    // Handle successful reconnection
  }}
/>
```

### ReconnectionStatusIndicator

Shows current reconnection status:

```javascript
<ReconnectionStatusIndicator
  sessionId="exam-session-id"
  onResumeSuccess={(result) => {
    // Handle successful resume
  }}
  className="fixed top-4 right-4"
/>
```

### ReconnectionRequestsDashboard

Full dashboard for teachers to manage requests:

```javascript
<ReconnectionRequestsDashboard />
```

**Features:**
- View all pending requests
- Approve/deny with reasons
- Auto-refresh functionality
- Request details and history

### ReconnectionQuickActions

Quick action dropdown for teachers:

```javascript
<ReconnectionQuickActions
  examId="specific-exam-id" // Optional: filter by exam
  onViewAll={() => navigate('/reconnection-dashboard')}
  className="ml-4"
/>
```

### AdminSessionMonitoringDashboard

Comprehensive admin dashboard:

```javascript
<AdminSessionMonitoringDashboard />
```

**Features:**
- View all active sessions
- Force submit sessions
- Terminate sessions
- Session statistics
- Auto-refresh controls

## Service Usage

### ExamReconnectionService

```javascript
import examReconnectionService from "../services/exam/reconnection/ExamReconnectionService";

// Request reconnection
const request = await examReconnectionService.requestReconnection(
  sessionId, 
  "Connection lost due to network issues"
);

// Check status
const status = await examReconnectionService.checkReconnectionStatus(requestId);

// Resume session
const sessionData = await examReconnectionService.resumeExamSession(sessionId);

// Handle connection loss with auto-polling
await examReconnectionService.handleConnectionLoss(
  sessionId,
  "Network disconnection",
  (status) => {
    console.log('Status update:', status);
  }
);
```

### AdminSessionService

```javascript
import adminSessionService from "../services/exam/admin/AdminSessionService";

// Get active sessions
const sessions = await adminSessionService.getActiveExamSessions();

// View session details
const details = await adminSessionService.viewExamSession(sessionId);

// Force submit
await adminSessionService.forceSubmitExamSession(sessionId, "Time exceeded");

// Terminate session
await adminSessionService.terminateExamSession(sessionId, "Security violation");

// Auto-refresh with callback
adminSessionService.startAutoRefresh((sessions) => {
  console.log('Updated sessions:', sessions);
});
```

## Redux Actions

### Student Actions

```javascript
import { useDispatch } from 'react-redux';
import {
  requestReconnection,
  checkReconnectionStatus,
  resumeExamSession,
  startStatusPolling,
  stopStatusPolling
} from '../store/slices/exam/examReconnectionSlice';

const dispatch = useDispatch();

// Request reconnection
dispatch(requestReconnection({ sessionId, reason }));

// Check status
dispatch(checkReconnectionStatus({ requestId }));

// Resume session
dispatch(resumeExamSession({ sessionId }));

// Start/stop polling
dispatch(startStatusPolling({ interval: 5000 }));
dispatch(stopStatusPolling());
```

### Teacher Actions

```javascript
import {
  getPendingReconnectionRequests,
  approveReconnectionRequest
} from '../store/slices/exam/examReconnectionSlice';

// Get pending requests
dispatch(getPendingReconnectionRequests());

// Approve/deny request
dispatch(approveReconnectionRequest({
  requestId,
  approved: true,
  reason: "Connection issue resolved"
}));
```

### Admin Actions

```javascript
import {
  getActiveExamSessions,
  viewExamSession,
  forceSubmitExamSession,
  terminateExamSession
} from '../store/slices/exam/examSessionAdminSlice';

// Get active sessions
dispatch(getActiveExamSessions());

// View session
dispatch(viewExamSession({ sessionId }));

// Force submit
dispatch(forceSubmitExamSession({ sessionId, reason }));

// Terminate
dispatch(terminateExamSession({ sessionId, reason }));
```

## State Selectors

```javascript
import { useSelector } from 'react-redux';
import {
  selectReconnectionState,
  selectCurrentRequest,
  selectRequestStatus,
  selectPendingRequests
} from '../store/slices/exam/examReconnectionSlice';

import {
  selectActiveExamSessions,
  selectExamSessionAdminState
} from '../store/slices/exam/examSessionAdminSlice';

// Student selectors
const reconnectionState = useSelector(selectReconnectionState);
const currentRequest = useSelector(selectCurrentRequest);
const requestStatus = useSelector(selectRequestStatus);

// Teacher selectors
const pendingRequests = useSelector(selectPendingRequests);

// Admin selectors
const activeSessions = useSelector(selectActiveExamSessions);
const adminState = useSelector(selectExamSessionAdminState);
```

## Error Handling

The system includes comprehensive error handling:

1. **Network Errors**: Automatic retry with exponential backoff
2. **API Errors**: User-friendly error messages via notification system
3. **Timeout Handling**: Configurable timeouts for all operations
4. **State Recovery**: Proper cleanup and state reset on errors

## Security Considerations

1. **Authentication**: All API calls include Bearer token authentication
2. **Input Validation**: All user inputs are validated and sanitized
3. **Rate Limiting**: Built-in retry limits to prevent abuse
4. **Session Validation**: Server-side validation of session ownership

## Testing

Test the integration by:

1. **Connection Loss Simulation**: Disable network during exam
2. **WebSocket Disconnection**: Close WebSocket connection manually
3. **Heartbeat Timeout**: Stop heartbeat responses
4. **Manual Reconnection**: Test manual reconnection flow
5. **Teacher Approval**: Test teacher approval/denial workflow
6. **Admin Controls**: Test admin session management

## Troubleshooting

Common issues and solutions:

1. **Reconnection Not Working**: Check API endpoints and authentication
2. **Status Not Updating**: Verify WebSocket connections and polling intervals
3. **UI Not Responding**: Check Redux state updates and component re-renders
4. **Service Errors**: Review browser console and network tab for errors

## Next Steps

1. **Monitoring**: Add logging and analytics for reconnection events
2. **Performance**: Optimize polling intervals and caching strategies
3. **Features**: Add bulk operations and advanced filtering
4. **Testing**: Implement comprehensive unit and integration tests

This integration provides a robust, user-friendly reconnection system that maintains exam integrity while handling real-world connection issues gracefully.
