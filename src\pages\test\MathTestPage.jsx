/**
 * Math Test Page
 * Demonstrates mathematical expression rendering
 */

import React from 'react';
import MathText, { MathBlock } from '../../components/ui/MathText';

const MathTestPage = () => {
  const testExpressions = [
    {
      title: "Basic Exponents",
      text: "Simplify the complex number expression i^{2023}.",
      expected: "Should show proper superscripts"
    },
    {
      title: "Polynomial",
      text: "Given the polynomial P(x) = x^4 - 2x^3 + ax^2 - 8x + 4. If (x-2) is a factor of P(x), determine the value of a.",
      expected: "Should show proper polynomial formatting"
    },
    {
      title: "Fractions",
      text: "Calculate 3/4 + 1/2 = ?",
      expected: "Should show proper fraction formatting"
    },
    {
      title: "Mathematical Symbols",
      text: "Find x where x >= 5 and x != 7, approximately x ~= 6",
      expected: "Should show proper mathematical symbols"
    },
    {
      title: "Square Roots",
      text: "Calculate sqrt(16) + sqrt(25)",
      expected: "Should show proper square root symbols"
    },
    {
      title: "Complex Expression",
      text: "P(x) = x^4 - 2x^3 + ax^2 - 8x + 4",
      expected: "Should handle polynomial expressions with proper formatting"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Mathematical Expression Rendering Test
          </h1>
          
          <div className="space-y-8">
            {testExpressions.map((expr, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">
                  {expr.title}
                </h3>
                
                <div className="bg-blue-50 p-4 rounded-lg mb-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Original Text:</h4>
                  <code className="text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded">
                    {expr.text}
                  </code>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg mb-4">
                  <h4 className="text-sm font-medium text-green-800 mb-2">Rendered Result:</h4>
                  <div className="text-lg question-text">
                    <MathText>{expr.text}</MathText>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Expected:</h4>
                  <p className="text-sm text-gray-600">{expr.expected}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 border-t pt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Block Math Examples
            </h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Quadratic Formula:</h3>
                <MathBlock>
                  {'$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$'}
                </MathBlock>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Complex Polynomial:</h3>
                <MathBlock>
                  {'$P(x) = x^4 - 2x^3 + ax^2 - 8x + 4$'}
                </MathBlock>
              </div>
            </div>
          </div>

          <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Usage Instructions:
            </h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Use ^ for exponents: x^2 becomes x²</li>
              <li>• Use _ for subscripts: H_2O becomes H₂O</li>
              <li>• Use * for multiplication: 2*3 becomes 2×3</li>
              <li>• Use &gt;= for greater than or equal: &gt;= becomes ≥</li>
              <li>• Use != for not equal: != becomes ≠</li>
              <li>• Use sqrt() for square roots: sqrt(16) becomes √16</li>
              <li>• Mathematical expressions are automatically detected and formatted</li>
              <li>• Complex expressions work seamlessly in question and option text</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MathTestPage;
