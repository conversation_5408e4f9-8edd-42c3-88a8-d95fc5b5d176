/**
 * Recent Activity Component
 * 
 * Displays student's recent academic activities including:
 * - Task submissions
 * - Exam completions
 * - Grade updates
 * - Class participation
 */

import React from 'react';
import { 
  FiCheckCircle, 
  FiFileText, 
  FiCalendar, 
  FiTrendingUp,
  FiBookOpen,
  FiUsers,
  FiAward,
  FiClock
} from 'react-icons/fi';
import { Card } from '../ui/layout';
import { formatDistanceToNow, format } from 'date-fns';

const RecentActivity = ({ activities = [], loading = false }) => {
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Recent Activity
        </h3>
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <FiClock className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No recent activity to display</p>
        </div>
      </Card>
    );
  }

  const getActivityIcon = (type) => {
    switch (type) {
      case 'task_completed':
      case 'assignment_submitted':
        return FiCheckCircle;
      case 'exam_taken':
      case 'exam_completed':
        return FiCalendar;
      case 'grade_received':
      case 'grade_updated':
        return FiTrendingUp;
      case 'class_joined':
      case 'class_attended':
        return FiUsers;
      case 'badge_earned':
      case 'achievement_unlocked':
        return FiAward;
      case 'material_accessed':
      case 'lesson_completed':
        return FiBookOpen;
      default:
        return FiFileText;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'task_completed':
      case 'assignment_submitted':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'exam_taken':
      case 'exam_completed':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'grade_received':
      case 'grade_updated':
        return 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30';
      case 'class_joined':
      case 'class_attended':
        return 'text-indigo-600 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30';
      case 'badge_earned':
      case 'achievement_unlocked':
        return 'text-amber-600 dark:text-amber-400 bg-amber-100 dark:bg-amber-900/30';
      case 'material_accessed':
      case 'lesson_completed':
        return 'text-teal-600 dark:text-teal-400 bg-teal-100 dark:bg-teal-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const formatActivityTime = (timestamp) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        return formatDistanceToNow(date, { addSuffix: true });
      } else {
        return format(date, 'MMM d, yyyy');
      }
    } catch (error) {
      return 'Recently';
    }
  };

  const getActivityDescription = (activity) => {
    if (activity.description) {
      return activity.description;
    }

    // Generate description based on type and metadata
    switch (activity.type) {
      case 'task_completed':
        return `Completed assignment: ${activity.metadata?.task_name || 'Task'}`;
      case 'exam_taken':
        return `Took exam: ${activity.metadata?.exam_name || 'Exam'}`;
      case 'grade_received':
        return `Received grade: ${activity.metadata?.grade || 'N/A'} for ${activity.metadata?.subject || 'subject'}`;
      case 'class_joined':
        return `Joined class: ${activity.metadata?.class_name || 'Class'}`;
      case 'badge_earned':
        return `Earned badge: ${activity.metadata?.badge_name || 'Achievement'}`;
      default:
        return activity.title || 'Activity completed';
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Recent Activity
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Last {activities.length} activities
        </span>
      </div>

      <div className="space-y-4">
        {activities.map((activity, index) => {
          const IconComponent = getActivityIcon(activity.type);
          const colorClasses = getActivityColor(activity.type);
          const timeAgo = formatActivityTime(activity.timestamp);
          const description = getActivityDescription(activity);

          return (
            <div key={activity.id || index} className="flex items-start gap-4">
              {/* Icon */}
              <div className={`p-2 rounded-full ${colorClasses} flex-shrink-0`}>
                <IconComponent className="w-4 h-4" />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                      {activity.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                      {description}
                    </p>
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-3 flex-shrink-0">
                    {timeAgo}
                  </span>
                </div>

                {/* Additional metadata */}
                {activity.metadata && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {activity.metadata.subject && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                        {activity.metadata.subject}
                      </span>
                    )}
                    {activity.metadata.score && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                        Score: {activity.metadata.score}
                      </span>
                    )}
                    {activity.metadata.points && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                        +{activity.metadata.points} pts
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Show more activities link */}
      {activities.length >= 5 && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button className="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors">
            View All Activity
          </button>
        </div>
      )}
    </Card>
  );
};

export default RecentActivity;
