import { Fi<PERSON>rrowLeft, Fi<PERSON>rrowRight, FiSend, FiCheck } from "react-icons/fi";
import { useThemeProvider } from "../../../providers/ThemeContext";

function ExamNavigation({ 
  currentQuestionIndex, 
  totalQuestions, 
  answers, 
  questions,
  onPreviousQuestion, 
  onNextQuestion, 
  onSubmitExam,
  isSubmitting 
}) {
  const { cardBg, themeText } = useThemeProvider();

  const getQuestionStatus = (index) => {
    const question = questions[index];
    const answer = answers[question?.id];
    
    if (answer && answer.trim() !== '') {
      return 'answered';
    }
    return 'unanswered';
  };

  const getStatusColor = (status, isActive = false) => {
    if (isActive) {
      return 'bg-violet-600 text-white border-violet-600';
    }
    
    switch (status) {
      case 'answered':
        return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400';
      case 'unanswered':
        return 'bg-gray-100 text-gray-600 border-gray-300 dark:bg-gray-800 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-300 dark:bg-gray-800 dark:text-gray-400';
    }
  };

  const answeredCount = questions.filter(q => {
    const answer = answers[q?.id];
    return answer && answer.trim() !== '';
  }).length;

  return (
    <div className={`${cardBg} rounded-xl p-6 shadow-lg`}>
      {/* Progress Summary */}
      <div className="mb-6">
        <h3 className={`text-lg font-semibold mb-3 ${themeText}`}>Progress</h3>
        <div className="flex items-center justify-between text-sm">
          <span className={themeText}>
            {answeredCount} of {totalQuestions} answered
          </span>
          <span className="text-violet-600 font-medium">
            {Math.round((answeredCount / totalQuestions) * 100)}% complete
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
          <div 
            className="bg-violet-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(answeredCount / totalQuestions) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Question Grid */}
      <div className="mb-6">
        <h4 className={`text-sm font-medium mb-3 ${themeText}`}>Questions</h4>
        <div className="grid grid-cols-5 gap-2">
          {questions.map((question, index) => {
            const status = getQuestionStatus(index);
            const isActive = index === currentQuestionIndex;
            
            return (
              <button
                key={question.id}
                className={`w-10 h-10 rounded-lg border text-sm font-medium transition-colors ${getStatusColor(status, isActive)}`}
                onClick={() => {
                  // Navigate to specific question logic would go here
                }}
              >
                {index + 1}
              </button>
            );
          })}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between">
        <button
          onClick={onPreviousQuestion}
          disabled={currentQuestionIndex === 0}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Previous</span>
        </button>

        {currentQuestionIndex === totalQuestions - 1 ? (
          <button
            onClick={onSubmitExam}
            disabled={isSubmitting}
            className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <FiSend className="w-4 h-4" />
                <span>Submit Exam</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={onNextQuestion}
            className="flex items-center space-x-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            <span>Next</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
}

export default ExamNavigation;
