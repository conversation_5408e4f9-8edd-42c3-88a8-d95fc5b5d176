import React from 'react';
import { <PERSON><PERSON>ook<PERSON><PERSON>, FiTarget, <PERSON>L<PERSON>ers, FiHash, FiCheckCircle } from 'react-icons/fi';
import MathText from '../ui/MathText';

/**
 * Component to display exam questions in a structured format
 * Used in exam detail view for teachers
 */
const QuestionsDisplay = ({ questions = [], className = "" }) => {
  if (!questions || questions.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Questions</h3>
        <div className="text-center py-8">
          <FiBookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">No questions available for this exam.</p>
        </div>
      </div>
    );
  }

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'hard':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getQuestionTypeColor = (type) => {
    switch (type?.toUpperCase()) {
      case 'MCQS':
      case 'MCQ':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'SHORT':
      case 'SHORT_ANSWER':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'LONG':
      case 'LONG_ANSWER':
      case 'DESCRIPTIVE':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      case 'TRUE_FALSE':
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  const getQuestionTypeDisplayText = (type) => {
    switch (type?.toUpperCase()) {
      case 'MCQS':
      case 'MCQ':
        return 'Multiple Choice';
      case 'SHORT':
      case 'SHORT_ANSWER':
        return 'Short Answer';
      case 'LONG':
      case 'LONG_ANSWER':
      case 'DESCRIPTIVE':
        return 'Long Answer';
      case 'TRUE_FALSE':
        return 'True/False';
      default:
        return type?.replace('_', ' ').toUpperCase() || 'Unknown';
    }
  };

  const totalMarks = questions.reduce((sum, q) => sum + (q.marks || 0), 0);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Questions ({questions.length})
          </h3>
          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
            <span className="flex items-center gap-1">
              <FiTarget className="w-4 h-4" />
              Total Marks: {totalMarks}
            </span>
          </div>
        </div>
      </div>

      {/* Questions List */}
      <div className="p-6 space-y-4">
        {questions.map((question, index) => (
          <div key={question.id || index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:border-blue-300 dark:hover:border-blue-600">
            {/* Question Header */}
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-3">
              <div className="flex items-center gap-3">
                <span className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-semibold flex-shrink-0">
                  {index + 1}
                </span>
                <div className="flex flex-wrap items-center gap-2">
                  {question.marks && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                      <FiHash className="w-3 h-3" />
                      {question.marks} marks
                    </span>
                  )}
                  {(question.Level || question.difficulty_level) && (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.Level || question.difficulty_level)}`}>
                      {question.Level || question.difficulty_level}
                    </span>
                  )}
                  {(question.Type || question.question_type) && (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getQuestionTypeColor(question.Type || question.question_type)}`}>
                      {getQuestionTypeDisplayText(question.Type || question.question_type)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Question Text */}
            <div className="mb-4 pl-11">
              <p className="text-gray-900 dark:text-gray-100 leading-relaxed question-text">
                <MathText>{question.text || question.question_text || 'No question text available'}</MathText>
              </p>
            </div>

            {/* Subject/Chapter/Topic Information */}
            {(question.subject?.name || question.chapter?.name || question.topic?.name || question.subtopic?.name ||
              question.subject_name || question.chapter_name || question.topic_name || question.subtopic_name) && (
              <div className="mb-4 pl-11 flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                {(question.subject?.name || question.subject_name) && (
                  <span className="flex items-center gap-1">
                    <FiBookOpen className="w-4 h-4" />
                    {question.subject?.name || question.subject_name}
                  </span>
                )}
                {(question.chapter?.name || question.chapter_name) && (
                  <span className="flex items-center gap-1">
                    <FiLayers className="w-4 h-4" />
                    {question.chapter?.name || question.chapter_name}
                  </span>
                )}
                {(question.topic?.name || question.topic_name) && (
                  <span>Topic: {question.topic?.name || question.topic_name}</span>
                )}
                {(question.subtopic?.name || question.subtopic_name) && (
                  <span>Subtopic: {question.subtopic?.name || question.subtopic_name}</span>
                )}
              </div>
            )}

            {/* Options for MCQ */}
            {(question.Type === 'MCQS' || question.question_type === 'mcq') && question.options && question.options.length > 0 && (
              <div className="pl-11 space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Options:</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {question.options.map((option, optIndex) => {
                    const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D
                    const isCorrect = option.is_correct || (typeof option === 'object' && option.correct);
                    const optionText = option.option_text || option.text || option;

                    return (
                      <div
                        key={optIndex}
                        className={`p-3 rounded-lg border transition-colors ${
                          isCorrect
                            ? 'border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20'
                            : 'border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700/50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold ${
                              isCorrect
                                ? 'bg-green-200 text-green-800 dark:bg-green-700 dark:text-green-200'
                                : 'bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                            }`}>
                              {optionLetter}
                            </span>
                            <span className="text-gray-900 dark:text-gray-100 option-text">
                              <MathText>{optionText}</MathText>
                            </span>
                          </div>
                          {isCorrect && (
                            <span className="text-green-600 dark:text-green-400 text-xs font-medium flex items-center gap-1">
                              <FiCheckCircle className="w-3 h-3" />
                              Correct
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Correct Answer for non-MCQ questions */}
            {(question.Type !== 'MCQS' && question.question_type !== 'mcq') && (question.answer || question.correct_answer) && (
              <div className="mt-4 pl-11">
                <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <FiCheckCircle className="w-4 h-4 text-green-600" />
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">Correct Answer:</p>
                  </div>
                  <p className="text-green-900 dark:text-green-100">{question.answer || question.correct_answer}</p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default QuestionsDisplay;
