import React, { useState, useEffect } from 'react';
import {
  FiDownload,
  FiMail,
  FiCalendar,
  FiMapPin,
  FiUser,
  FiRefreshCw,
  FiCheck,
  FiClock,
  FiInfo,
  FiHash,
  FiGrid,
  FiCreditCard
} from 'react-icons/fi';
import { LoadingSpinner } from '../ui';
import { useNotification } from '../../contexts/NotificationContext';
import { BASE_API } from '../../utils/api/API_URL';
import { downloadTicketPDF } from '../../utils/ticketPdfGenerator';

const TicketViewer = ({ registrationId, eventData, onClose }) => {
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [resendingEmail, setResendingEmail] = useState(false);
  const { showSuccess, showError } = useNotification();

  useEffect(() => {
    if (registrationId) {
      fetchTicket();
    }
  }, [registrationId]);

  const fetchTicket = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const response = await fetch(`${BASE_API}/api/tickets/registrations/${registrationId}/ticket`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch ticket');
      }

      const ticketData = await response.json();
      setTicket(ticketData);
    } catch (err) {
      console.error('Error fetching ticket:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTicket = async () => {
    try {
      showInfo('Generating ticket PDF...');

      // Use the ticket data that's already loaded
      if (ticket) {
        const result = downloadTicketPDF(ticket);

        if (result.success) {
          showSuccess('Ticket PDF downloaded successfully!');
        } else {
          throw new Error(result.error || 'Failed to generate PDF');
        }
      } else {
        throw new Error('Ticket data not available');
      }
    } catch (err) {
      console.error('Error downloading ticket:', err);
      showError('Failed to download ticket');
    }
  };

  const handleResendConfirmation = async () => {
    try {
      setResendingEmail(true);
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      
      const response = await fetch(`${BASE_API}/api/tickets/registrations/${registrationId}/resend-confirmation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to resend confirmation email');
      }

      showSuccess('Confirmation email sent successfully!');
    } catch (err) {
      console.error('Error resending confirmation:', err);
      showError('Failed to resend confirmation email');
    } finally {
      setResendingEmail(false);
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Not specified';
    try {
      return new Date(dateString).toLocaleString('en-ZA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">Loading your ticket...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiInfo className="w-8 h-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Load Ticket</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex space-x-3">
              <button
                onClick={fetchTicket}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiRefreshCw className="w-4 h-4 inline mr-2" />
                Retry
              </button>
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FiCreditCard className="w-8 h-8 mr-3" />
              <div>
                <h2 className="text-xl font-bold">Your Event Ticket</h2>
                <p className="text-blue-100">Digital ticket confirmation</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* Ticket Content */}
        <div className="p-6 space-y-6">
          {/* Event Information */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start">
                <FiCalendar className="w-5 h-5 text-gray-400 mt-0.5 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">{ticket?.event?.title || eventData?.title}</p>
                  <p className="text-sm text-gray-600">
                    {formatDateTime(ticket?.event?.start_datetime || eventData?.start_datetime)}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <FiMapPin className="w-5 h-5 text-gray-400 mt-0.5 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Location</p>
                  <p className="text-sm text-gray-600">{ticket?.event?.location || eventData?.location}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Ticket Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ticket Information</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Ticket Type:</span>
                <span className="font-medium">{ticket?.ticket?.name || 'General Admission'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Holder:</span>
                <span className="font-medium">{ticket?.user?.first_name} {ticket?.user?.last_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Registration ID:</span>
                <span className="font-mono text-sm">{registrationId}</span>
              </div>
              {ticket?.check_in_code && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Check-in Code:</span>
                  <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                    {ticket.check_in_code}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Check-in Code Section */}
          {ticket?.check_in_code && (
            <div className="text-center bg-white border border-gray-200 rounded-lg p-6">
              <FiGrid className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Check-in Code</h3>
              <p className="text-sm text-gray-600 mb-4">
                Show this code at the event entrance for quick check-in
              </p>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="font-mono text-lg">{ticket.check_in_code}</p>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleDownloadTicket}
              className="flex-1 flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiDownload className="w-4 h-4 mr-2" />
              Download Ticket
            </button>
            <button
              onClick={handleResendConfirmation}
              disabled={resendingEmail}
              className="flex-1 flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {resendingEmail ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <FiMail className="w-4 h-4 mr-2" />
                  Resend Email
                </>
              )}
            </button>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-500 border-t pt-4">
            <p>Keep this ticket safe and bring it to the event.</p>
            <p>Generated on {formatDateTime(ticket?.generated_at)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicketViewer;
