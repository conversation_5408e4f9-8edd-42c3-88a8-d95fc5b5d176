/**
 * Quick Exam API Test
 * Simple test to check if the exam APIs are working
 */

import React, { useState } from 'react';
import { <PERSON>P<PERSON>, <PERSON><PERSON>heck, FiX } from 'react-icons/fi';

const QuickExamAPITest = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState([]);

  const TEST_EXAM_ID = "dc5aede9-e966-4d28-96ea-568718994656";

  const addResult = (step, success, message, data = null) => {
    setResults(prev => [...prev, {
      id: Date.now(),
      step,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testAPI = async () => {
    setIsRunning(true);
    setResults([]);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        addResult('Auth', false, 'No token found in localStorage');
        setIsRunning(false);
        return;
      }

      addResult('Auth', true, 'Token found');

      // Test 1: Request Session ID
      addResult('Session Request', null, 'Requesting session ID...');
      try {
        const sessionResponse = await fetch(`http://127.0.0.1:8000/exam-session/request/${TEST_EXAM_ID}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const sessionText = await sessionResponse.text();
        console.log('Session response:', sessionResponse.status, sessionText);

        if (!sessionResponse.ok) {
          let errorMsg = `HTTP ${sessionResponse.status}`;
          try {
            const errorData = JSON.parse(sessionText);
            errorMsg = errorData.detail || errorData.message || errorMsg;
          } catch (e) {
            errorMsg = sessionText || errorMsg;
          }
          addResult('Session Request', false, errorMsg, { status: sessionResponse.status, response: sessionText });
          setIsRunning(false);
          return;
        }

        const sessionData = JSON.parse(sessionText);
        addResult('Session Request', true, `Got session ID: ${sessionData.session_id}`, sessionData);

        // Test 2: Get Exam Data
        addResult('Exam Data', null, 'Getting exam data...');
        try {
          const examResponse = await fetch(`http://127.0.0.1:8000/attempt/${sessionData.session_id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          const examText = await examResponse.text();
          console.log('Exam response:', examResponse.status, examText);

          if (!examResponse.ok) {
            let errorMsg = `HTTP ${examResponse.status}`;
            try {
              const errorData = JSON.parse(examText);
              errorMsg = errorData.detail || errorData.message || errorMsg;
            } catch (e) {
              errorMsg = examText || errorMsg;
            }
            addResult('Exam Data', false, errorMsg, { status: examResponse.status, response: examText });
          } else {
            const examData = JSON.parse(examText);
            addResult('Exam Data', true, `Got exam: ${examData.title} with ${examData.questions?.length || 0} questions`, {
              title: examData.title,
              questionCount: examData.questions?.length || 0,
              duration: examData.total_duration,
              remainingTime: examData.remaining_time_seconds
            });
          }
        } catch (error) {
          addResult('Exam Data', false, `Network error: ${error.message}`, error);
        }

      } catch (error) {
        addResult('Session Request', false, `Network error: ${error.message}`, error);
      }

    } catch (error) {
      addResult('General', false, `Unexpected error: ${error.message}`, error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Quick Exam API Test</h1>
        
        <div className="mb-6">
          <button
            onClick={testAPI}
            disabled={isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <FiPlay className="w-4 h-4" />
            <span>{isRunning ? 'Testing...' : 'Test API'}</span>
          </button>
        </div>

        <div className="space-y-3">
          {results.map((result) => (
            <div
              key={result.id}
              className={`p-4 rounded-lg border ${
                result.success === true ? 'bg-green-50 border-green-200' :
                result.success === false ? 'bg-red-50 border-red-200' :
                'bg-blue-50 border-blue-200'
              }`}
            >
              <div className="flex items-start space-x-3">
                {result.success === true ? (
                  <FiCheck className="w-5 h-5 text-green-600 mt-0.5" />
                ) : result.success === false ? (
                  <FiX className="w-5 h-5 text-red-600 mt-0.5" />
                ) : (
                  <div className="w-5 h-5 mt-0.5 rounded-full bg-blue-400 animate-pulse" />
                )}
                <div className="flex-1">
                  <h3 className={`font-medium ${
                    result.success === true ? 'text-green-800' :
                    result.success === false ? 'text-red-800' :
                    'text-blue-800'
                  }`}>
                    {result.step}
                  </h3>
                  <p className={`text-sm ${
                    result.success === true ? 'text-green-600' :
                    result.success === false ? 'text-red-600' :
                    'text-blue-600'
                  }`}>
                    {result.message}
                  </p>
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-500 cursor-pointer">View Details</summary>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-32">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    {result.timestamp}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {results.length === 0 && !isRunning && (
          <div className="text-center py-8 text-gray-500">
            Click "Test API" to check if the exam endpoints are working
          </div>
        )}

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Debug Info:</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Test Exam ID:</strong> {TEST_EXAM_ID}</p>
            <p><strong>API Base URL:</strong> http://127.0.0.1:8000</p>
            <p><strong>Token Available:</strong> {localStorage.getItem('token') ? 'Yes' : 'No'}</p>
            <p><strong>Current URL:</strong> {window.location.href}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickExamAPITest;
