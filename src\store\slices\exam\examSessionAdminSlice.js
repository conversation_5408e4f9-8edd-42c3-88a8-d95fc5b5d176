/**
 * Exam Session Admin Redux Slice
 * Manages admin session monitoring, control, and management operations
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getAuthToken } from '../../../utils/helpers/authHelpers';

const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';

// Get all active exam sessions (admin only)
export const getActiveExamSessions = createAsyncThunk(
  'examSessionAdmin/getActiveSessions',
  async (_, { rejectWithValue }) => {
    try {
      console.log('📊 Fetching active exam sessions');
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/exam-sessions/active`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch active sessions');
      }

      const data = await response.json();
      console.log('📊 Active sessions fetched:', data);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch active sessions:', error);
      return rejectWithValue(error.message);
    }
  }
);

// View specific exam session details (admin only)
export const viewExamSession = createAsyncThunk(
  'examSessionAdmin/viewSession',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('🔍 Viewing exam session details:', sessionId);
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/exam-session/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to view session details');
      }

      const data = await response.json();
      console.log('🔍 Session details:', data);
      return { sessionId, details: data };
    } catch (error) {
      console.error('❌ Failed to view session:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Force submit exam session (admin only)
export const forceSubmitExamSession = createAsyncThunk(
  'examSessionAdmin/forceSubmit',
  async ({ sessionId, reason }, { rejectWithValue }) => {
    try {
      console.log('🚨 Force submitting exam session:', { sessionId, reason });
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/exam-session/${sessionId}/submit?reason=${encodeURIComponent(reason)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to force submit session');
      }

      const data = await response.json();
      console.log('✅ Session force submitted:', data);
      return { sessionId, reason, result: data };
    } catch (error) {
      console.error('❌ Force submit failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Terminate exam session (admin only)
export const terminateExamSession = createAsyncThunk(
  'examSessionAdmin/terminateSession',
  async ({ sessionId, reason }, { rejectWithValue }) => {
    try {
      console.log('🛑 Terminating exam session:', { sessionId, reason });
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/exam-session/${sessionId}/terminate?reason=${encodeURIComponent(reason)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to terminate session');
      }

      const data = await response.json();
      console.log('✅ Session terminated:', data);
      return { sessionId, reason, result: data };
    } catch (error) {
      console.error('❌ Termination failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  // Active sessions data
  activeSessions: [],
  sessionDetails: {},
  
  // Admin actions
  actionHistory: [],
  
  // UI state
  loading: false,
  error: null,
  success: null,
  
  // Monitoring
  autoRefresh: false,
  refreshInterval: 30000, // 30 seconds
  lastRefresh: null,
  
  // Filters and search
  filters: {
    status: 'all', // all, active, paused, disconnected
    examId: null,
    studentId: null,
    timeRange: 'today' // today, week, month
  },
  searchTerm: '',
  
  // Selected sessions for bulk actions
  selectedSessions: []
};

const examSessionAdminSlice = createSlice({
  name: 'examSessionAdmin',
  initialState,
  reducers: {
    // Clear messages
    clearMessages: (state) => {
      state.error = null;
      state.success = null;
    },
    
    // Set auto refresh
    setAutoRefresh: (state, action) => {
      state.autoRefresh = action.payload.enabled;
      if (action.payload.interval) {
        state.refreshInterval = action.payload.interval;
      }
    },
    
    // Update filters
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // Set search term
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
    },
    
    // Select/deselect sessions
    toggleSessionSelection: (state, action) => {
      const sessionId = action.payload;
      const index = state.selectedSessions.indexOf(sessionId);
      if (index > -1) {
        state.selectedSessions.splice(index, 1);
      } else {
        state.selectedSessions.push(sessionId);
      }
    },
    
    // Select all sessions
    selectAllSessions: (state) => {
      state.selectedSessions = state.activeSessions.map(session => session.session_id);
    },
    
    // Clear selection
    clearSelection: (state) => {
      state.selectedSessions = [];
    },
    
    // Add action to history
    addActionToHistory: (state, action) => {
      state.actionHistory.unshift({
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload
      });
      // Keep only last 100 actions
      if (state.actionHistory.length > 100) {
        state.actionHistory = state.actionHistory.slice(0, 100);
      }
    },
    
    // Update last refresh time
    updateLastRefresh: (state) => {
      state.lastRefresh = new Date().toISOString();
    }
  },
  extraReducers: (builder) => {
    builder
      // Get active sessions
      .addCase(getActiveExamSessions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getActiveExamSessions.fulfilled, (state, action) => {
        state.loading = false;
        state.activeSessions = action.payload;
        state.lastRefresh = new Date().toISOString();
      })
      .addCase(getActiveExamSessions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // View session details
      .addCase(viewExamSession.pending, (state) => {
        state.loading = true;
      })
      .addCase(viewExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.sessionDetails[action.payload.sessionId] = action.payload.details;
      })
      .addCase(viewExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Force submit session
      .addCase(forceSubmitExamSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(forceSubmitExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.success = `Session ${action.payload.sessionId} force submitted successfully`;
        
        // Add to action history
        state.actionHistory.unshift({
          id: Date.now(),
          timestamp: new Date().toISOString(),
          action: 'force_submit',
          sessionId: action.payload.sessionId,
          reason: action.payload.reason,
          result: 'success'
        });
        
        // Remove from active sessions
        state.activeSessions = state.activeSessions.filter(
          session => session.session_id !== action.payload.sessionId
        );
      })
      .addCase(forceSubmitExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Terminate session
      .addCase(terminateExamSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(terminateExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.success = `Session ${action.payload.sessionId} terminated successfully`;
        
        // Add to action history
        state.actionHistory.unshift({
          id: Date.now(),
          timestamp: new Date().toISOString(),
          action: 'terminate',
          sessionId: action.payload.sessionId,
          reason: action.payload.reason,
          result: 'success'
        });
        
        // Remove from active sessions
        state.activeSessions = state.activeSessions.filter(
          session => session.session_id !== action.payload.sessionId
        );
      })
      .addCase(terminateExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const {
  clearMessages,
  setAutoRefresh,
  updateFilters,
  setSearchTerm,
  toggleSessionSelection,
  selectAllSessions,
  clearSelection,
  addActionToHistory,
  updateLastRefresh
} = examSessionAdminSlice.actions;

// Selectors
export const selectAdminSessionState = (state) => state.examSessionAdmin;
export const selectActiveSessions = (state) => state.examSessionAdmin.activeSessions;
export const selectSessionDetails = (state) => state.examSessionAdmin.sessionDetails;
export const selectActionHistory = (state) => state.examSessionAdmin.actionHistory;
export const selectSelectedSessions = (state) => state.examSessionAdmin.selectedSessions;
export const selectFilters = (state) => state.examSessionAdmin.filters;

export default examSessionAdminSlice.reducer;
