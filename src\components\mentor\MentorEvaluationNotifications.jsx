import React, { useState, useEffect } from 'react';
import { 
  FiCheckCircle, 
  FiAlertCircle, 
  FiInfo, 
  FiX,
  FiAlertTriangle 
} from 'react-icons/fi';

const MentorEvaluationNotifications = ({ notifications, onDismiss }) => {
  const [visibleNotifications, setVisibleNotifications] = useState([]);

  useEffect(() => {
    if (notifications && notifications.length > 0) {
      setVisibleNotifications(notifications);
    }
  }, [notifications]);

  const handleDismiss = (id) => {
    setVisibleNotifications(prev => prev.filter(notif => notif.id !== id));
    if (onDismiss) {
      onDismiss(id);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <FiCheckCircle className="h-5 w-5 text-green-400" />;
      case 'error':
        return <FiAlertCircle className="h-5 w-5 text-red-400" />;
      case 'warning':
        return <FiAlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'info':
      default:
        return <FiInfo className="h-5 w-5 text-blue-400" />;
    }
  };

  const getNotificationStyles = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (!visibleNotifications.length) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {visibleNotifications.map((notification) => (
        <div
          key={notification.id}
          className={`rounded-md border p-4 shadow-lg transition-all duration-300 ${getNotificationStyles(notification.type)}`}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="ml-3 flex-1">
              {notification.title && (
                <h3 className="text-sm font-medium mb-1">
                  {notification.title}
                </h3>
              )}
              <p className="text-sm">
                {notification.message}
              </p>
              {notification.details && (
                <p className="text-xs mt-1 opacity-75">
                  {notification.details}
                </p>
              )}
            </div>
            <div className="ml-4 flex-shrink-0">
              <button
                onClick={() => handleDismiss(notification.id)}
                className="inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current"
              >
                <FiX className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Hook for managing notifications
export const useMentorEvaluationNotifications = () => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = (notification) => {
    const id = Date.now() + Math.random();
    const newNotification = { ...notification, id };
    
    setNotifications(prev => [...prev, newNotification]);
    
    // Auto-dismiss after 5 seconds for success/info, 8 seconds for warnings/errors
    const timeout = notification.type === 'error' || notification.type === 'warning' ? 8000 : 5000;
    setTimeout(() => {
      dismissNotification(id);
    }, timeout);
    
    return id;
  };

  const dismissNotification = (id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Convenience methods
  const showSuccess = (message, title = 'Success', details = null) => {
    return addNotification({ type: 'success', title, message, details });
  };

  const showError = (message, title = 'Error', details = null) => {
    return addNotification({ type: 'error', title, message, details });
  };

  const showWarning = (message, title = 'Warning', details = null) => {
    return addNotification({ type: 'warning', title, message, details });
  };

  const showInfo = (message, title = 'Info', details = null) => {
    return addNotification({ type: 'info', title, message, details });
  };

  return {
    notifications,
    addNotification,
    dismissNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};

export default MentorEvaluationNotifications;
