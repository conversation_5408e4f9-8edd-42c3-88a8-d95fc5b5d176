/**
 * MVP Ticket Selection Component
 * 
 * Simple ticket selection that acts as a cashier:
 * - User browses tickets and clicks "Buy Ticket"
 * - Frontend sends ticket_id to backend
 * - Backend responds with PayFast link
 * - Frontend redirects to PayFast
 * 
 * No complex state management - just pass ticket ID and redirect.
 */

import React, { useState } from 'react';
import {
  FiCreditCard,
  FiCheck,
  FiShield,
  FiInfo,
  FiUser,
  FiMail,
  FiPhone,
  FiLoader
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import mvpPaymentService from '../../services/mvpPaymentService';

const MVPTicketSelection = ({ event, onSuccess, onError }) => {
  const { showSuccess, showError, showInfo } = useNotification();
  
  // Simple state - just track loading and selected ticket
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * Handle ticket selection
   */
  const handleTicketSelect = (ticket) => {
    setSelectedTicket(ticket);
  };

  /**
   * Handle buy ticket - the main MVP flow
   */
  const handleBuyTicket = async () => {
    if (!selectedTicket) {
      showError('Please select a ticket first');
      return;
    }

    // Check if user is logged in
    if (!mvpPaymentService.isAuthenticated()) {
      showError('Please log in to purchase tickets');
      return;
    }

    setIsProcessing(true);
    
    try {
      // Log the payment attempt
      mvpPaymentService.logPaymentEvent(selectedTicket.id, 'create_link', {
        ticket_name: selectedTicket.name,
        ticket_price: selectedTicket.price
      });

      showInfo('Creating payment link...');

      // Step 1: Request payment link from backend
      // Frontend acts as cashier - just passes ticket ID, backend decides price
      const paymentResponse = await mvpPaymentService.createPaymentLink(selectedTicket.id);
      
      if (!paymentResponse.payment_url) {
        throw new Error('No payment URL received from server');
      }

      // Log successful link creation
      mvpPaymentService.logPaymentEvent(selectedTicket.id, 'redirect', {
        payment_url: paymentResponse.payment_url
      });

      showSuccess('Redirecting to secure payment...');

      // Step 2: Redirect to PayFast immediately
      // No complex state management - just redirect
      setTimeout(() => {
        mvpPaymentService.redirectToPayFast(paymentResponse.payment_url);
      }, 1000); // Small delay to show success message

      // Call success callback if provided
      if (onSuccess) {
        onSuccess({
          ticket: selectedTicket,
          payment_url: paymentResponse.payment_url
        });
      }

    } catch (error) {
      console.error('Payment creation failed:', error);
      
      // Log the error
      mvpPaymentService.logPaymentEvent(selectedTicket.id, 'error', {
        error: error.message
      });

      showError(error.message || 'Failed to create payment. Please try again.');
      
      // Call error callback if provided
      if (onError) {
        onError(error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * Format currency display
   */
  const formatCurrency = (amount, currency = 'PKR') => {
    if (amount === 0) return 'Free';

    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  /**
   * Get user info for display
   */
  const getUserInfo = () => {
    return mvpPaymentService.getUserInfo();
  };

  // Show login prompt if not authenticated
  if (!mvpPaymentService.isAuthenticated()) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <FiUser className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Login Required</h3>
          <p className="text-gray-600 mb-4">
            Please log in to purchase tickets for this event.
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Show no tickets message if none available
  if (!event?.tickets || event.tickets.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <FiInfo className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-600">No tickets available for this event</p>
        </div>
      </div>
    );
  }

  const userInfo = getUserInfo();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Select Tickets</h3>

      {/* User Info Display */}
      {userInfo && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Purchasing as:</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <div className="flex items-center">
              <FiUser className="w-4 h-4 mr-2" />
              {userInfo.username || userInfo.name || 'Unknown User'}
            </div>
            <div className="flex items-center">
              <FiMail className="w-4 h-4 mr-2" />
              {userInfo.email || 'No email'}
            </div>
            {userInfo.mobile && (
              <div className="flex items-center">
                <FiPhone className="w-4 h-4 mr-2" />
                {userInfo.mobile}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Ticket Options */}
      <div className="space-y-4 mb-6">
        {event.tickets.map((ticket) => (
          <div
            key={ticket.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedTicket?.id === ticket.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleTicketSelect(ticket)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                    selectedTicket?.id === ticket.id
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedTicket?.id === ticket.id && (
                      <FiCheck className="w-2 h-2 text-white m-0.5" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{ticket.name}</h4>
                    <p className="text-sm text-gray-600">{ticket.description}</p>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900">
                  {formatCurrency(ticket.price, ticket.currency)}
                </p>
                {ticket.available_quantity !== undefined && (
                  <p className="text-sm text-gray-500">
                    {ticket.available_quantity} available
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Buy Button */}
      {selectedTicket && (
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Selected Ticket:</p>
              <p className="font-medium text-gray-900">{selectedTicket.name}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Total:</p>
              <p className="text-xl font-bold text-gray-900">
                {formatCurrency(selectedTicket.price, selectedTicket.currency)}
              </p>
            </div>
          </div>

          <button
            onClick={handleBuyTicket}
            disabled={isProcessing}
            className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <FiLoader className="w-5 h-5 mr-2 animate-spin" />
            ) : selectedTicket.price === 0 ? (
              <FiCheck className="w-5 h-5 mr-2" />
            ) : (
              <FiCreditCard className="w-5 h-5 mr-2" />
            )}
            {isProcessing
              ? 'Creating Payment...'
              : selectedTicket.price === 0
                ? 'Get Free Ticket'
                : 'Buy Ticket'
            }
          </button>

          {selectedTicket.price > 0 && (
            <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
              <FiShield className="w-4 h-4 mr-1" />
              Secure payment powered by PayFast
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MVPTicketSelection;
