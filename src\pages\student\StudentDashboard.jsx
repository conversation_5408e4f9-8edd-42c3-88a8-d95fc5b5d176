import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getStudentUpcomingExams } from '../../store/slices/ExamSlice';
import { fetchMyClassrooms } from '../../store/slices/ClassroomSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import { useStudentDashboard } from '../../hooks/useStudentDashboard';
import { useStudentAnalytics } from '../../hooks/useStudentAnalytics';
import { FluidPageContainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import PerformanceMetrics from '../../components/dashboard/PerformanceMetrics';
import QuickActionsPanel from '../../components/dashboard/QuickActionsPanel';
import RecentActivity from '../../components/dashboard/RecentActivity';
import UpcomingSchedule from '../../components/dashboard/UpcomingSchedule';
import AnalyticsSummary from '../../components/dashboard/AnalyticsSummary';
import ComprehensiveAnalytics from '../../components/dashboard/ComprehensiveAnalytics';
import {
  FiCalendar,
  FiBookOpen,
  FiTrendingUp,
  FiUsers,
  FiPlay,
  FiEye,
  FiBell
} from 'react-icons/fi';

function StudentDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { exams: reduxExams, loading: examsLoading } = useSelector((state) => state.exams);
  const { myClassrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { currentUser } = useSelector((state) => state.users);

  const {
    dashboardData,
    summary,
    quickActions,
    performance,
    studyMetrics,
    schedule,
    student,
    classes,
    exams: dashboardExams,
    assignments,
    recentActivity,
    loading,
    error,
    refreshData,
    hasErrors,
    hasData
  } = useStudentDashboard({
    autoFetch: false,
    fetchAll: true
  });

  // Fetch analytics data for the ComprehensiveAnalytics component
  const {
    fetch: { all: fetchAllAnalytics }
  } = useStudentAnalytics({
    autoFetch: false, // We'll fetch manually to control timing
    fetchAll: false
  });

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(getStudentUpcomingExams());
      dispatch(fetchMyClassrooms());
      refreshData();

      // Only fetch analytics if user is authenticated and has a token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (token) {
        fetchAllAnalytics();
      } else {
        console.warn('No authentication token found, skipping analytics fetch');
      }
    }
  }, [dispatch, currentUser]); // Removed refreshData from dependencies

  const stats = useMemo(() => {
    // Use both exam sources - prioritize dashboard exams if available
    const examsList = dashboardExams?.length ? dashboardExams : reduxExams;
    const upcomingExams = Array.isArray(examsList) ? examsList.filter(exam => {
      const now = new Date();
      const startTime = new Date(exam.start_time);
      return startTime > now;
    }).length : 0;

    const totalClasses = classes?.length ||
                        (Array.isArray(myClassrooms) ? myClassrooms.length :
                        myClassrooms?.results?.length || myClassrooms?.data?.length || 0);

    const pendingAssignments = assignments?.filter(assignment =>
      assignment.status === 'pending'
    )?.length || 0;

    const unreadNotifications = dashboardData?.unread_notifications_count || 0;

    return [
      {
        key: 'classes',
        title: 'Total Classes',
        value: totalClasses,
        icon: FiUsers,
        color: 'blue',
        onClick: () => navigate('/student/classrooms')
      },
      {
        key: 'exams',
        title: 'Upcoming Exams',
        value: upcomingExams,
        icon: FiCalendar,
        color: 'green',
        onClick: () => navigate('/student/exams')
      },
      {
        key: 'performance',
        title: 'Average Score',
        value: performance?.overall_grade ? `${performance.overall_grade.toFixed(1)}%` : '0%',
        icon: FiTrendingUp,
        color: 'purple'
      },
      {
        key: 'assignments',
        title: 'Pending Tasks',
        value: pendingAssignments,
        icon: FiBookOpen,
        color: 'indigo',
        onClick: () => navigate('/student/tasks')
      },
      {
        key: 'notifications',
        title: 'Notifications',
        value: unreadNotifications,
        icon: FiBell,
        color: 'orange',
        onClick: () => navigate('/student/notifications')
      }
    ];
  }, [reduxExams, dashboardExams, classes, myClassrooms, assignments, performance, dashboardData, navigate]);

  // Use quickActions from the hook instead of defining locally

  const isLoading = examsLoading || classroomsLoading || loading.dashboard;

  // Show error state if there are critical errors
  if (hasErrors && !hasData) {
    return (
      <FluidPageContainer>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <FiBell className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
            Unable to Load Dashboard
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            There was an issue loading your dashboard data. Please try refreshing the page.
          </p>
          <button
            onClick={() => {
              refreshData();
              dispatch(getStudentUpcomingExams());
              dispatch(fetchMyClassrooms());
            }}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry Loading
          </button>
        </div>
      </FluidPageContainer>
    );
  }

  return (
    <FluidPageContainer>
      <div className="mb-8">
        <div className="text-center md:text-left">
          <h1 className="text-3xl md:text-4xl text-gray-800 dark:text-gray-100 font-bold">
            Student Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-3 text-lg">
            Welcome back, {currentUser?.username || 'Student'}! Here's your learning overview.
          </p>
          {isLoading && (
            <div className="mt-2 flex items-center justify-center md:justify-start">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-sm text-gray-500 dark:text-gray-400">Loading dashboard data...</span>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-8">
        <DashboardGrid stats={stats} loading={isLoading} />

        {/* Performance Overview */}
        <PerformanceMetrics
          performance={performance}
          studyMetrics={studyMetrics}
          loading={loading.performance || loading.dashboard}
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <QuickActionsPanel
              quickActions={quickActions}
              loading={loading.quickActions || loading.dashboard}
            />
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-1">
            <RecentActivity
              activities={recentActivity}
              loading={loading.dashboard}
            />
          </div>

          {/* Upcoming Schedule */}
          <div className="lg:col-span-1 xl:col-span-1">
            <UpcomingSchedule
              schedule={schedule}
              loading={loading.schedule || loading.dashboard}
            />
          </div>
        </div>

        {/* Comprehensive Analytics */}
        <ComprehensiveAnalytics />
      </div>
    </FluidPageContainer>
  );
}

export default StudentDashboard;
