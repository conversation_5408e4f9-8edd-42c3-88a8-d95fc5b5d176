import jsPDF from 'jspdf';
import { format } from 'date-fns';

/**
 * Generate a professional ticket PDF from JSON data
 * @param {Object} ticketData - The ticket data from the backend
 * @returns {Blob} PDF blob for download
 */
export const generateTicketPDF = (ticketData) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  
  // Colors
  const primaryColor = [59, 130, 246]; // Blue-600
  const secondaryColor = [107, 114, 128]; // Gray-500
  const accentColor = [16, 185, 129]; // Emerald-500
  const textColor = [31, 41, 55]; // Gray-800
  
  // Helper function to add colored rectangle
  const addColoredRect = (x, y, width, height, color) => {
    pdf.setFillColor(...color);
    pdf.rect(x, y, width, height, 'F');
  };
  
  // Helper function to add text with color
  const addText = (text, x, y, fontSize = 12, color = textColor, style = 'normal') => {
    pdf.setTextColor(...color);
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', style);
    // Ensure text is always a string
    const textString = String(text || '');
    pdf.text(textString, x, y);
  };
  
  // Helper function to add centered text
  const addCenteredText = (text, y, fontSize = 12, color = textColor, style = 'normal') => {
    pdf.setTextColor(...color);
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', style);
    // Ensure text is always a string
    const textString = String(text || '');
    const textWidth = pdf.getStringUnitWidth(textString) * fontSize / pdf.internal.scaleFactor;
    const x = (pageWidth - textWidth) / 2;
    pdf.text(textString, x, y);
  };
  
  // Header with gradient effect (simulated with rectangles)
  addColoredRect(0, 0, pageWidth, 40, primaryColor);
  addColoredRect(0, 35, pageWidth, 5, [37, 99, 235]); // Darker blue
  
  // Event Ticket Title
  addCenteredText('EVENT TICKET', 25, 20, [255, 255, 255], 'bold');
  
  // Ticket border
  pdf.setDrawColor(...primaryColor);
  pdf.setLineWidth(2);
  pdf.rect(margin, 50, pageWidth - 2 * margin, pageHeight - 100);
  
  // Ticket content area
  let yPos = 70;
  
  // Event Title
  const eventTitle = String(ticketData.event?.title || 'Event');
  addCenteredText(eventTitle, yPos, 18, primaryColor, 'bold');
  yPos += 25;

  // Status badge
  const status = String(ticketData.registration?.status || 'CONFIRMED');
  const statusColor = status === 'CONFIRMED' ? accentColor : [239, 68, 68]; // Red for other statuses
  addColoredRect(pageWidth/2 - 25, yPos - 8, 50, 15, statusColor);
  addCenteredText(status, yPos, 10, [255, 255, 255], 'bold');
  yPos += 30;
  
  // Ticket Information Section
  addText('TICKET INFORMATION', margin + 10, yPos, 14, primaryColor, 'bold');
  yPos += 20;
  
  // Registration details
  const details = [
    { label: 'Registration ID:', value: String(ticketData.registration?.id || 'N/A') },
    { label: 'Registration Number:', value: String(ticketData.registration?.registration_number || 'N/A') },
    { label: 'Ticket Type:', value: String(ticketData.ticket?.name || 'General Admission') },
    { label: 'Quantity:', value: String(ticketData.registration?.quantity || 1) },
    { label: 'Attendee Name:', value: String(ticketData.user?.display_name || ticketData.user?.username || 'N/A') },
    { label: 'Email:', value: String(ticketData.user?.email || 'N/A') },
    { label: 'Total Amount:', value: `${ticketData.registration?.total_amount || 0} ${ticketData.registration?.currency || 'PKR'}` }
  ];
  
  details.forEach(detail => {
    addText(String(detail.label), margin + 10, yPos, 11, secondaryColor, 'bold');
    addText(String(detail.value), margin + 80, yPos, 11, textColor);
    yPos += 15;
  });
  
  yPos += 10;
  
  // Event Information Section
  addText('EVENT INFORMATION', margin + 10, yPos, 14, primaryColor, 'bold');
  yPos += 20;
  
  const eventDetails = [
    {
      label: 'Start Date & Time:',
      value: String(ticketData.event?.start_datetime
        ? format(new Date(ticketData.event.start_datetime), 'PPP p')
        : 'TBD')
    },
    {
      label: 'End Date & Time:',
      value: String(ticketData.event?.end_datetime
        ? format(new Date(ticketData.event.end_datetime), 'PPP p')
        : 'TBD')
    },
    {
      label: 'Location:',
      value: String(ticketData.event?.location || 'TBD')
    },
    {
      label: 'Category:',
      value: String(ticketData.event?.category || 'General')
    },
    {
      label: 'Registered At:',
      value: String(ticketData.registration?.registered_at
        ? format(new Date(ticketData.registration.registered_at), 'PPP p')
        : 'N/A')
    }
  ];
  
  eventDetails.forEach(detail => {
    addText(String(detail.label), margin + 10, yPos, 11, secondaryColor, 'bold');
    addText(String(detail.value), margin + 80, yPos, 11, textColor);
    yPos += 15;
  });
  
  // Check-in Code section (if available)
  if (ticketData.check_in_code || ticketData.registration?.check_in_code) {
    yPos += 20;
    addText('CHECK-IN CODE', margin + 10, yPos, 14, primaryColor, 'bold');
    yPos += 15;

    // Check-in code box
    pdf.setDrawColor(...secondaryColor);
    pdf.setLineWidth(1);
    pdf.rect(margin + 10, yPos, 60, 60);

    const checkInCode = ticketData.check_in_code || ticketData.registration?.check_in_code;
    addText(String(checkInCode), margin + 15, yPos + 35, 8, textColor, 'bold');

    // Instructions
    addText('Present this code at the event entrance', margin + 80, yPos + 20, 10, secondaryColor);
    addText('for quick check-in and verification.', margin + 80, yPos + 35, 10, secondaryColor);
  } else {
    // Add a note about check-in
    yPos += 20;
    addText('CHECK-IN INFORMATION', margin + 10, yPos, 14, primaryColor, 'bold');
    yPos += 15;
    addText('Present this ticket and a valid ID at the event entrance.', margin + 10, yPos, 10, secondaryColor);
    addText('Check-in code will be provided closer to the event date.', margin + 10, yPos + 12, 10, secondaryColor);
  }
  
  // Footer section
  const footerY = pageHeight - 40;
  
  // Footer line
  pdf.setDrawColor(...primaryColor);
  pdf.setLineWidth(1);
  pdf.line(margin, footerY - 10, pageWidth - margin, footerY - 10);
  
  // Footer text
  addText('Generated on:', margin, footerY, 9, secondaryColor);
  addText(format(new Date(ticketData.generated_at || new Date()), 'PPP p'), margin + 35, footerY, 9, textColor);

  addText('Valid for:', pageWidth - margin - 60, footerY, 9, secondaryColor);
  addText(`${ticketData.registration?.quantity || 1} Entry`, pageWidth - margin - 25, footerY, 9, textColor);
  
  // Important notes
  const notes = [
    '• Please bring a valid ID for verification',
    '• This ticket is non-transferable and non-refundable',
    '• Arrive 30 minutes before the event start time',
    '• Keep this ticket safe - lost tickets cannot be replaced'
  ];
  
  addText('IMPORTANT NOTES:', margin, footerY + 15, 10, primaryColor, 'bold');
  notes.forEach((note, index) => {
    addText(note, margin, footerY + 30 + (index * 10), 8, secondaryColor);
  });
  
  return pdf;
};

/**
 * Download ticket as PDF
 * @param {Object} ticketData - The ticket data from the backend
 * @param {string} filename - Optional custom filename
 */
export const downloadTicketPDF = (ticketData, filename = null) => {
  try {
    const pdf = generateTicketPDF(ticketData);
    
    // Generate filename
    const eventTitle = (ticketData.event?.title || 'Event').replace(/[^a-zA-Z0-9]/g, '_');
    const regNumber = ticketData.registration?.registration_number || ticketData.registration?.id || 'ticket';
    const defaultFilename = `${eventTitle}_Ticket_${regNumber}.pdf`;
    
    // Save the PDF
    pdf.save(filename || defaultFilename);
    
    return { success: true, filename: filename || defaultFilename };
  } catch (error) {
    console.error('Error generating ticket PDF:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Generate ticket PDF blob for preview or custom handling
 * @param {Object} ticketData - The ticket data from the backend
 * @returns {Blob} PDF blob
 */
export const generateTicketPDFBlob = (ticketData) => {
  try {
    const pdf = generateTicketPDF(ticketData);
    return pdf.output('blob');
  } catch (error) {
    console.error('Error generating ticket PDF blob:', error);
    throw error;
  }
};
