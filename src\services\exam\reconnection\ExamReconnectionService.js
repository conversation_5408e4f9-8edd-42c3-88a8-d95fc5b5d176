/**
 * Exam Session Reconnection Service
 * Handles reconnection requests, status polling, and session resumption
 */

import { getAuthToken } from '../../../utils/helpers/authHelpers';

class ExamReconnectionService {
  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';
    this.statusPollingIntervals = new Map();
    this.reconnectionAttempts = new Map();
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds
  }

  /**
   * Request reconnection to an exam session
   * @param {string} sessionId - The exam session ID
   * @param {string} reason - Reason for disconnection
   * @returns {Promise<Object>} Reconnection request response
   */
  async requestReconnection(sessionId, reason) {
    try {
      console.log('🔄 Requesting reconnection:', { sessionId, reason });

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/request-reconnection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          session_id: sessionId,
          reason: reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to request reconnection');
      }

      const data = await response.json();
      console.log('✅ Reconnection requested:', data);

      // Start status polling for this request
      this.startStatusPolling(data.request_id, sessionId);

      return data;
    } catch (error) {
      console.error('❌ Reconnection request failed:', error);
      throw error;
    }
  }

  /**
   * Check reconnection status
   * @param {string} requestId - The reconnection request ID
   * @returns {Promise<Object>} Status response
   */
  async checkReconnectionStatus(requestId) {
    try {
      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/reconnection-status/${requestId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to check status');
      }

      const data = await response.json();
      console.log('📊 Reconnection status:', data);
      return data;
    } catch (error) {
      console.error('❌ Status check failed:', error);
      throw error;
    }
  }

  /**
   * Resume exam session after approval
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Resumed session data
   */
  async resumeExamSession(sessionId) {
    try {
      console.log('▶️ Resuming exam session:', sessionId);

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/resume`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to resume session');
      }

      const data = await response.json();
      console.log('✅ Session resumed:', data);

      // Stop status polling since session is resumed
      this.stopStatusPolling(sessionId);

      return data;
    } catch (error) {
      console.error('❌ Session resume failed:', error);
      throw error;
    }
  }

  /**
   * Start polling reconnection status
   * @param {string} requestId - The reconnection request ID
   * @param {string} sessionId - The exam session ID
   * @param {Function} onStatusChange - Callback for status changes
   * @param {number} interval - Polling interval in milliseconds
   */
  startStatusPolling(requestId, sessionId, onStatusChange = null, interval = 5000) {
    // Clear existing polling for this session
    this.stopStatusPolling(sessionId);

    console.log('🔄 Starting status polling for request:', requestId);

    const pollInterval = setInterval(async () => {
      try {
        const status = await this.checkReconnectionStatus(requestId);
        
        if (onStatusChange) {
          onStatusChange(status);
        }

        // Stop polling if request is resolved
        if (status.status === 'approved' || status.status === 'denied') {
          console.log('✅ Status polling completed:', status.status);
          this.stopStatusPolling(sessionId);
          
          // If approved, automatically attempt to resume
          if (status.status === 'approved') {
            try {
              await this.resumeExamSession(sessionId);
            } catch (error) {
              console.error('❌ Auto-resume failed:', error);
            }
          }
        }
      } catch (error) {
        console.error('❌ Status polling error:', error);
        
        // Increment retry count
        const attempts = this.reconnectionAttempts.get(sessionId) || 0;
        this.reconnectionAttempts.set(sessionId, attempts + 1);
        
        // Stop polling if max retries reached
        if (attempts >= this.maxRetries) {
          console.error('❌ Max polling retries reached for session:', sessionId);
          this.stopStatusPolling(sessionId);
        }
      }
    }, interval);

    this.statusPollingIntervals.set(sessionId, pollInterval);
  }

  /**
   * Stop status polling for a session
   * @param {string} sessionId - The exam session ID
   */
  stopStatusPolling(sessionId) {
    const interval = this.statusPollingIntervals.get(sessionId);
    if (interval) {
      clearInterval(interval);
      this.statusPollingIntervals.delete(sessionId);
      this.reconnectionAttempts.delete(sessionId);
      console.log('⏹️ Status polling stopped for session:', sessionId);
    }
  }

  /**
   * Stop all status polling
   */
  stopAllPolling() {
    console.log('⏹️ Stopping all status polling');
    for (const [sessionId, interval] of this.statusPollingIntervals) {
      clearInterval(interval);
    }
    this.statusPollingIntervals.clear();
    this.reconnectionAttempts.clear();
  }

  /**
   * Handle connection loss and automatic reconnection request
   * @param {string} sessionId - The exam session ID
   * @param {string} reason - Reason for disconnection
   * @param {Function} onStatusChange - Callback for status updates
   * @returns {Promise<Object>} Reconnection request response
   */
  async handleConnectionLoss(sessionId, reason = 'Connection lost', onStatusChange = null) {
    try {
      console.log('🚨 Handling connection loss for session:', sessionId);

      // Request reconnection
      const request = await this.requestReconnection(sessionId, reason);

      // Start status polling with callback
      if (onStatusChange) {
        this.startStatusPolling(request.request_id, sessionId, onStatusChange);
      }

      return request;
    } catch (error) {
      console.error('❌ Failed to handle connection loss:', error);
      throw error;
    }
  }

  /**
   * Check if session has active reconnection request
   * @param {string} sessionId - The exam session ID
   * @returns {boolean} True if actively polling for reconnection
   */
  hasActiveReconnection(sessionId) {
    return this.statusPollingIntervals.has(sessionId);
  }

  /**
   * Get retry count for session
   * @param {string} sessionId - The exam session ID
   * @returns {number} Number of retry attempts
   */
  getRetryCount(sessionId) {
    return this.reconnectionAttempts.get(sessionId) || 0;
  }

  /**
   * Reset retry count for session
   * @param {string} sessionId - The exam session ID
   */
  resetRetryCount(sessionId) {
    this.reconnectionAttempts.delete(sessionId);
  }

  /**
   * Cleanup service resources
   */
  cleanup() {
    console.log('🧹 Cleaning up ExamReconnectionService');
    this.stopAllPolling();
  }
}

// Create singleton instance
const examReconnectionService = new ExamReconnectionService();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    examReconnectionService.cleanup();
  });
}

export default examReconnectionService;
