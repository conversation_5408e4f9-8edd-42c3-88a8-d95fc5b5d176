import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiEdit,
  FiTrash2,
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiShare2,
  FiEye,
  FiSettings,
  FiTrendingUp,
  FiDollarSign,
  FiMail,
  FiPhone,
  FiGlobe,
  FiLinkedin,
  FiTwitter,
  FiTag,
  FiInfo
} from 'react-icons/fi';
import { format } from 'date-fns';
import {
  fetchInstituteEventById,
  deleteInstituteEvent,
  publishInstituteEvent,
  selectCurrentEvent,
  selectCurrentEventLoading,
  selectCurrentEventError,
  clearErrors
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { getCategoryColor, getStatusBadgeColor } from '../../utils/eventUtils';

const InstituteEventDetailsPage = ({ eventId: propEventId }) => {
  const { eventId: paramEventId } = useParams();
  const eventId = propEventId || paramEventId; // Use prop first, fallback to params
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Redux state
  const event = useSelector(selectCurrentEvent);
  const loading = useSelector(selectCurrentEventLoading);
  const error = useSelector(selectCurrentEventError);

  // Helper function to validate UUID
  const isValidUUID = (str) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  };

  // Load event details on mount
  useEffect(() => {
    if (eventId && isValidUUID(eventId)) {
      dispatch(fetchInstituteEventById(eventId));
    }
  }, [dispatch, eventId]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  const formatDate = (dateString) => {
    if (!dateString) return 'TBD';
    try {
      return format(new Date(dateString), 'PPP p');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const handleDelete = async () => {
    try {
      await dispatch(deleteInstituteEvent(eventId));
      navigate('/institute/events');
    } catch (error) {
      console.error('Failed to delete event:', error);
    }
  };

  const handlePublish = async () => {
    try {
      await dispatch(publishInstituteEvent(eventId));
    } catch (error) {
      console.error('Failed to publish event:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  // Check for invalid eventId
  if (eventId && !isValidUUID(eventId)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Event ID</h2>
          <p className="text-gray-600 mb-4">The event ID provided is not valid.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Event not found</h2>
          <p className="text-gray-600 mb-4">The event you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/institute/events')}
            className="text-blue-600 hover:text-blue-800"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/institute/events')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FiArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{event.title}</h1>
                <div className="flex items-center space-x-3 mt-1">
                  <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getCategoryColor(event.category)}`}>
                    {event.category}
                  </span>
                  <span className={`px-3 py-1 text-xs font-bold rounded-full ${getStatusBadgeColor(event.status)}`}>
                    {event.status?.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(`/institute/events/${eventId}/edit`)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiEdit className="h-4 w-4 mr-2" />
                Edit Event
              </button>
              
              {event.status?.toLowerCase() === 'draft' && (
                <button
                  onClick={handlePublish}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <FiTrendingUp className="h-4 w-4 mr-2" />
                  Publish
                </button>
              )}

              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <FiTrash2 className="h-4 w-4 mr-2" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Banner Image */}
            {event.banner_image_url && (
              <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden">
                <img
                  src={event.banner_image_url}
                  alt={event.title}
                  className="w-full h-64 object-cover"
                />
              </div>
            )}

            {/* Description */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">About This Event</h2>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">{event.description}</p>
                {event.short_description && (
                  <p className="text-gray-600 mt-4 italic">{event.short_description}</p>
                )}
              </div>
            </div>

            {/* Requirements */}
            {event.requirements && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Requirements</h2>
                <p className="text-gray-700">{event.requirements}</p>
              </div>
            )}

            {/* Speakers Section */}
            {event.speakers && event.speakers.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-6">Speakers ({event.speakers.length})</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {event.speakers.map((speaker) => (
                    <div key={speaker.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                          {speaker.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900">{speaker.name}</h3>
                            {speaker.is_featured && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <FiStar className="h-3 w-3 mr-1 fill-current" />
                                Featured
                              </span>
                            )}
                          </div>
                          <p className="text-sm font-medium text-blue-600 mb-1">{speaker.title}</p>
                          <p className="text-sm text-gray-600 mb-2">{speaker.company}</p>
                          <p className="text-sm text-gray-700 mb-3">{speaker.bio}</p>

                          {/* Expertise Areas */}
                          {speaker.expertise_areas && speaker.expertise_areas.length > 0 && (
                            <div className="mb-3">
                              <div className="flex flex-wrap gap-1">
                                {speaker.expertise_areas.map((area, index) => (
                                  <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <FiTag className="h-3 w-3 mr-1" />
                                    {area}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Contact Information */}
                          <div className="flex space-x-3 text-gray-500">
                            {speaker.email && (
                              <a href={`mailto:${speaker.email}`} className="hover:text-blue-600 transition-colors">
                                <FiMail className="h-4 w-4" />
                              </a>
                            )}
                            {speaker.phone && (
                              <a href={`tel:${speaker.phone}`} className="hover:text-blue-600 transition-colors">
                                <FiPhone className="h-4 w-4" />
                              </a>
                            )}
                            {speaker.website && (
                              <a href={speaker.website} target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 transition-colors">
                                <FiGlobe className="h-4 w-4" />
                              </a>
                            )}
                            {speaker.linkedin_url && (
                              <a href={speaker.linkedin_url} target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 transition-colors">
                                <FiLinkedin className="h-4 w-4" />
                              </a>
                            )}
                            {speaker.twitter_url && (
                              <a href={speaker.twitter_url} target="_blank" rel="noopener noreferrer" className="hover:text-blue-600 transition-colors">
                                <FiTwitter className="h-4 w-4" />
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tickets Section */}
            {event.tickets && event.tickets.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-6">Tickets ({event.tickets.length})</h2>
                <div className="space-y-4">
                  {event.tickets.map((ticket) => (
                    <div key={ticket.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h3 className="font-semibold text-gray-900">{ticket.name}</h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            ticket.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {ticket.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-gray-900">{ticket.currency} {ticket.price}</p>
                          <p className="text-sm text-gray-600">{ticket.sold_quantity || 0} / {ticket.total_quantity} sold</p>
                        </div>
                      </div>

                      {ticket.description && (
                        <p className="text-sm text-gray-700 mb-3">{ticket.description}</p>
                      )}

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="font-medium text-gray-900">Available</p>
                          <p className="text-gray-600">{ticket.total_quantity - (ticket.sold_quantity || 0)}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Min Order</p>
                          <p className="text-gray-600">{ticket.min_quantity_per_order}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Max Order</p>
                          <p className="text-gray-600">{ticket.max_quantity_per_order}</p>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Transferable</p>
                          <p className="text-gray-600">{ticket.is_transferable ? 'Yes' : 'No'}</p>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mt-3">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Sales Progress</span>
                          <span>{Math.round(((ticket.sold_quantity || 0) / ticket.total_quantity) * 100)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(((ticket.sold_quantity || 0) / ticket.total_quantity) * 100, 100)}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Event Details */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Event Details</h3>
              <div className="space-y-4">
                <div className="flex items-center text-gray-700">
                  <FiCalendar className="h-5 w-5 mr-3 text-blue-600" />
                  <div>
                    <p className="font-medium">Start Date</p>
                    <p className="text-sm text-gray-600">{formatDate(event.start_datetime)}</p>
                  </div>
                </div>
                
                <div className="flex items-center text-gray-700">
                  <FiClock className="h-5 w-5 mr-3 text-green-600" />
                  <div>
                    <p className="font-medium">End Date</p>
                    <p className="text-sm text-gray-600">{formatDate(event.end_datetime)}</p>
                  </div>
                </div>

                <div className="flex items-center text-gray-700">
                  <FiMapPin className="h-5 w-5 mr-3 text-red-600" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-gray-600">
                      {event.location || 'TBD'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center text-gray-700">
                  <FiUsers className="h-5 w-5 mr-3 text-purple-600" />
                  <div>
                    <p className="font-medium">Capacity</p>
                    <p className="text-sm text-gray-600">
                      {event.max_attendees || 'Unlimited'} attendees
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Registration Details */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Registration Details</h3>
              <div className="space-y-4">
                <div className="flex items-center text-gray-700">
                  <FiClock className="h-5 w-5 mr-3 text-green-600" />
                  <div>
                    <p className="font-medium">Registration Start</p>
                    <p className="text-sm text-gray-600">{formatDate(event.registration_start)}</p>
                  </div>
                </div>

                <div className="flex items-center text-gray-700">
                  <FiClock className="h-5 w-5 mr-3 text-red-600" />
                  <div>
                    <p className="font-medium">Registration End</p>
                    <p className="text-sm text-gray-600">{formatDate(event.registration_end)}</p>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Total Registrations</span>
                    <span className="font-semibold">{event.total_registrations || 0}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Available Tickets</span>
                    <span className="font-semibold">{event.available_tickets || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Available Spots</span>
                    <span className="font-semibold">
                      {event.max_attendees ? event.max_attendees - (event.total_registrations || 0) : '∞'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Event Settings */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Event Settings</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Public Event</span>
                  <span className={`font-semibold ${event.is_public ? 'text-green-600' : 'text-red-600'}`}>
                    {event.is_public ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Featured</span>
                  <span className={`font-semibold ${event.is_featured ? 'text-yellow-600' : 'text-gray-600'}`}>
                    {event.is_featured ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Requires Approval</span>
                  <span className={`font-semibold ${event.requires_approval ? 'text-orange-600' : 'text-green-600'}`}>
                    {event.requires_approval ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Competition</span>
                  <span className={`font-semibold ${event.is_competition ? 'text-purple-600' : 'text-gray-600'}`}>
                    {event.is_competition ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            {/* Admin Information */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Admin Information</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <p className="font-medium text-gray-900">Event ID</p>
                  <p className="text-gray-600 font-mono break-all">{event.id}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Organizer ID</p>
                  <p className="text-gray-600 font-mono break-all">{event.organizer_id}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Created</p>
                  <p className="text-gray-600">
                    {event.created_at ? new Date(event.created_at).toLocaleString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Last Updated</p>
                  <p className="text-gray-600">
                    {event.updated_at ? new Date(event.updated_at).toLocaleString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Event</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this event? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstituteEventDetailsPage;
