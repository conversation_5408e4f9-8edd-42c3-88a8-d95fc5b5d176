import React, { useState } from 'react';
import { getAuthToken, getUserRole, getUserData } from '../../utils/helpers/authHelpers';

const AdminAuthTest = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results = {};

    // Test 1: Check localStorage
    const token = getAuthToken();
    const role = getUserRole();
    const userData = getUserData();
    
    results.localStorage = {
      token: token ? 'Present' : 'Missing',
      tokenPreview: token ? token.substring(0, 20) + '...' : 'null',
      role: role || 'Not set',
      userData: userData ? 'Present' : 'Missing'
    };

    // Test 2: Check backend connectivity
    try {
      const response = await fetch('http://127.0.0.1:8000/api/health', {
        method: 'GET'
      });
      results.backend = {
        status: response.status,
        reachable: response.ok,
        message: response.ok ? 'Backend is reachable' : 'Backend returned error'
      };
    } catch (error) {
      results.backend = {
        status: 'Error',
        reachable: false,
        message: `Backend not reachable: ${error.message}`
      };
    }

    // Test 3: Test admin API with current token
    if (token) {
      try {
        const response = await fetch('http://127.0.0.1:8000/api/admin/exam-sessions/active', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        const responseText = await response.text();
        let responseData;
        try {
          responseData = JSON.parse(responseText);
        } catch {
          responseData = responseText;
        }

        results.adminAPI = {
          status: response.status,
          success: response.ok,
          message: response.ok ? 'Admin API accessible' : `Error: ${response.status}`,
          response: responseData
        };
      } catch (error) {
        results.adminAPI = {
          status: 'Error',
          success: false,
          message: `Admin API error: ${error.message}`,
          response: null
        };
      }
    } else {
      results.adminAPI = {
        status: 'Skipped',
        success: false,
        message: 'No token available for testing',
        response: null
      };
    }

    setTestResults(results);
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            🔍 Admin Authentication Test
          </h1>
          
          <button
            onClick={runTests}
            disabled={loading}
            className="mb-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '🔄 Running Tests...' : '🧪 Run Authentication Tests'}
          </button>

          {Object.keys(testResults).length > 0 && (
            <div className="space-y-6">
              {/* localStorage Test */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  📱 LocalStorage Test
                </h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Token:</strong> {testResults.localStorage?.token}</div>
                  <div><strong>Token Preview:</strong> {testResults.localStorage?.tokenPreview}</div>
                  <div><strong>Role:</strong> {testResults.localStorage?.role}</div>
                  <div><strong>User Data:</strong> {testResults.localStorage?.userData}</div>
                </div>
              </div>

              {/* Backend Test */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  🌐 Backend Connectivity Test
                </h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Status:</strong> {testResults.backend?.status}</div>
                  <div><strong>Reachable:</strong> {testResults.backend?.reachable ? '✅ Yes' : '❌ No'}</div>
                  <div><strong>Message:</strong> {testResults.backend?.message}</div>
                </div>
              </div>

              {/* Admin API Test */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  🔐 Admin API Test
                </h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Status:</strong> {testResults.adminAPI?.status}</div>
                  <div><strong>Success:</strong> {testResults.adminAPI?.success ? '✅ Yes' : '❌ No'}</div>
                  <div><strong>Message:</strong> {testResults.adminAPI?.message}</div>
                  {testResults.adminAPI?.response && (
                    <div>
                      <strong>Response:</strong>
                      <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-auto">
                        {JSON.stringify(testResults.adminAPI.response, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>

              {/* Recommendations */}
              <div className="border border-yellow-200 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-3">
                  💡 Recommendations
                </h3>
                <div className="space-y-2 text-sm text-yellow-700 dark:text-yellow-300">
                  {testResults.localStorage?.token === 'Missing' && (
                    <div>• Please login to get an authentication token</div>
                  )}
                  {testResults.localStorage?.role !== 'admin' && (
                    <div>• Please login with an admin account (current role: {testResults.localStorage?.role})</div>
                  )}
                  {!testResults.backend?.reachable && (
                    <div>• Please ensure the backend server is running on http://127.0.0.1:8000</div>
                  )}
                  {testResults.adminAPI?.status === 401 && (
                    <div>• Authentication failed - token may be expired or invalid</div>
                  )}
                  {testResults.adminAPI?.status === 403 && (
                    <div>• Access denied - admin privileges required</div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminAuthTest;
