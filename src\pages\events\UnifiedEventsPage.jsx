/**
 * UnifiedEventsPage Component
 * 
 * Combined events page with sub-navigation for Browse Events and My Events
 */

import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiUser,
  FiSearch,
  FiGrid,
  FiList
} from 'react-icons/fi';
import EventsPage from './EventsPage';
import UserEventDashboard from '../../components/events/UserEventDashboard';
import { PageContainer } from '../../components/ui/layout';

const UnifiedEventsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Determine active tab from URL or default to 'browse'
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/my-events') || path.includes('/events/my')) {
      return 'my-events';
    }
    return 'browse';
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());
  const [viewMode, setViewMode] = useState('grid'); // grid or list

  // Events data state for statistics
  const [eventsData, setEventsData] = useState({
    availableCount: 0,
    registeredCount: 0,
    featuredCount: 0,
    upcomingCount: 0,
    thisWeekCount: 0,
    completedCount: 0
  });

  // Load events statistics
  useEffect(() => {
    const loadEventsStatistics = async () => {
      try {
        // TODO: Replace with actual API calls when backend endpoints are available
        // const response = await fetch('/api/events/statistics');
        // const data = await response.json();
        // setEventsData(data);

        // For now, set some realistic mock data
        setEventsData({
          availableCount: 12,
          registeredCount: 3,
          featuredCount: 5,
          upcomingCount: 2,
          thisWeekCount: 8,
          completedCount: 1
        });
      } catch (error) {
        console.error('Failed to load events statistics:', error);
        // Keep default values on error
      }
    };

    loadEventsStatistics();
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    
    // Update URL without full navigation to maintain state
    const basePath = location.pathname.split('/events')[0] + '/events';
    const newPath = tab === 'my-events' ? `${basePath}/my` : basePath;
    
    // Use replace to avoid adding to history stack
    window.history.replaceState(null, '', newPath);
  };

  const tabs = [
    {
      id: 'browse',
      label: 'Browse Events',
      icon: FiSearch,
      description: 'Discover and register for events'
    },
    {
      id: 'my-events',
      label: 'My Events',
      icon: FiUser,
      description: 'Manage your registrations'
    }
  ];

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                Events
              </h1>
              <p className="text-gray-600">
                {activeTab === 'browse'
                  ? 'Discover and register for upcoming events'
                  : 'Manage your event registrations and tickets'
                }
              </p>
            </div>
            <div className="hidden md:block">
              <div className="bg-gray-100 rounded-lg p-3">
                <FiCalendar className="h-8 w-8 text-gray-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Controls Section */}
        <div className="flex items-center justify-between">
          <div></div>

          {/* View Mode Toggle (only for browse tab) */}
          {activeTab === 'browse' && (
            <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
                title="Grid View"
              >
                <FiGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
                title="List View"
              >
                <FiList className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      isActive
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className={`mr-2 h-5 w-5 ${
                      isActive 
                        ? 'text-blue-500 dark:text-blue-400' 
                        : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                    }`} />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'browse' ? (
              <div className="space-y-4">
                {/* Tab Description */}
                <div className="text-center py-4">
                  <p className="text-gray-600 dark:text-gray-400">
                    Discover upcoming events, workshops, and competitions. Register with just a few clicks.
                  </p>
                </div>
                
                {/* Events Content */}
                <div className="min-h-[400px]">
                  <EventsPage viewMode={viewMode} embedded={true} />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Tab Description */}
                <div className="text-center py-4">
                  <p className="text-gray-600 dark:text-gray-400">
                    View your registered events, download tickets, and track your event history.
                  </p>
                </div>
                
                {/* My Events Content */}
                <div className="min-h-[400px]">
                  <UserEventDashboard embedded={true} />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Stats (visible on both tabs) */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCalendar className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {activeTab === 'browse' ? 'Available Events' : 'Registered Events'}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  {activeTab === 'browse' ? eventsData.availableCount : eventsData.registeredCount}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiUser className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {activeTab === 'browse' ? 'Featured Events' : 'Upcoming Events'}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  {activeTab === 'browse' ? eventsData.featuredCount : eventsData.upcomingCount}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                  <FiCalendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {activeTab === 'browse' ? 'This Week' : 'Completed'}
                </p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  {activeTab === 'browse' ? eventsData.thisWeekCount : eventsData.completedCount}
                </p>
              </div>
            </div>
          </div>
        </div>

       

        {activeTab === 'my-events' && (
          <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Event Reminders</h3>
                <p className="text-green-100 mt-1">
                  Don't forget about your upcoming events. Check your calendar and prepare!
                </p>
              </div>
              <button className="bg-white text-green-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                View Calendar
              </button>
            </div>
          </div>
        )}
      </div>
    </PageContainer>
  );
};

export default UnifiedEventsPage;
