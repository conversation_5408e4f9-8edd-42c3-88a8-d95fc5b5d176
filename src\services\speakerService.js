/**
 * Event Speaker Service
 * Handles all speaker-related API operations for events
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class SpeakerService {
  constructor() {
    this.baseUrl = BASE_API;
    this.apiPrefix = '/api/events/speakers';
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Handle API errors
   */
  handleApiError(error) {
    if (error.response) {
      const { status, data } = error.response;
      logger.error('Speaker API Error:', { status, data });
      
      if (status === 422) {
        throw new Error(data.detail?.[0]?.msg || 'Validation error');
      }
      if (status === 401) {
        throw new Error('Authentication required');
      }
      if (status === 403) {
        throw new Error('Only institutes can manage speakers');
      }
      if (status === 404) {
        throw new Error('Speaker not found');
      }
      
      throw new Error(data.detail || data.message || 'API request failed');
    }
    
    if (error.request) {
      throw new Error('Network error. Please check your connection.');
    }
    
    throw new Error(error.message || 'An unexpected error occurred.');
  }

  /**
   * Create a new event speaker
   * POST /api/events/speakers/
   */
  async createSpeaker(speakerData) {
    try {
      logger.info('Creating speaker:', speakerData);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/`,
        speakerData,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Speaker created successfully:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to create speaker:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get all event speakers
   * GET /api/events/speakers/
   */
  async getAllSpeakers(skip = 0, limit = 100) {
    try {
      logger.info('Fetching speakers:', { skip, limit });

      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/`,
        {
          headers: this.getAuthHeaders(),
          params: { skip, limit }
        }
      );

      logger.info('Speakers fetched successfully:', response.data);

      // Ensure we return an array
      const speakers = response.data;
      if (!Array.isArray(speakers)) {
        logger.warn('API returned non-array response:', speakers);
        return [];
      }

      return speakers;
    } catch (error) {
      logger.error('Failed to fetch speakers:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get speaker by ID
   * GET /api/events/speakers/{speaker_id}
   */
  async getSpeakerById(speakerId) {
    try {
      logger.info('Fetching speaker by ID:', speakerId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${speakerId}`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Speaker fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch speaker:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Update an existing event speaker
   * PUT /api/events/speakers/{speaker_id}
   */
  async updateSpeaker(speakerId, speakerData) {
    try {
      logger.info('Updating speaker:', { speakerId, speakerData });
      
      const response = await axios.put(
        `${this.baseUrl}${this.apiPrefix}/${speakerId}`,
        speakerData,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Speaker updated successfully:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to update speaker:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete an event speaker
   * DELETE /api/events/speakers/{speaker_id}
   */
  async deleteSpeaker(speakerId) {
    try {
      logger.info('Deleting speaker:', speakerId);
      
      await axios.delete(
        `${this.baseUrl}${this.apiPrefix}/${speakerId}`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Speaker deleted successfully');
      return { success: true };
    } catch (error) {
      logger.error('Failed to delete speaker:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Validate speaker data before submission
   */
  validateSpeakerData(speakerData) {
    const errors = [];
    
    if (!speakerData.name || speakerData.name.trim() === '') {
      errors.push('Speaker name is required');
    }
    
    if (!speakerData.title || speakerData.title.trim() === '') {
      errors.push('Speaker title is required');
    }
    
    if (speakerData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(speakerData.email)) {
      errors.push('Invalid email format');
    }
    
    if (speakerData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(speakerData.phone.replace(/\s/g, ''))) {
      errors.push('Invalid phone number format');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format speaker data for API submission
   */
  formatSpeakerData(speakerData) {
    return {
      name: speakerData.name?.trim() || '',
      title: speakerData.title?.trim() || '',
      bio: speakerData.bio?.trim() || '',
      profile_image_url: speakerData.profile_image_url?.trim() || '',
      company: speakerData.company?.trim() || '',
      website: speakerData.website?.trim() || '',
      linkedin_url: speakerData.linkedin_url?.trim() || '',
      twitter_url: speakerData.twitter_url?.trim() || '',
      email: speakerData.email?.trim() || '',
      phone: speakerData.phone?.trim() || '',
      expertise_areas: Array.isArray(speakerData.expertise_areas) 
        ? speakerData.expertise_areas.filter(area => area.trim() !== '') 
        : [],
      is_featured: Boolean(speakerData.is_featured)
    };
  }
}

// Create and export service instance
const speakerService = new SpeakerService();
export default speakerService;

// Export individual methods for convenience
export const {
  createSpeaker,
  getAllSpeakers,
  getSpeakerById,
  updateSpeaker,
  deleteSpeaker,
  validateSpeakerData,
  formatSpeakerData
} = speakerService;
