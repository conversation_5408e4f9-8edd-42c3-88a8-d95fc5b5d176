import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiUsers, FiUser, FiSearch } from 'react-icons/fi';
import { fetchAllOwnClasses, fetchTeacherStudents } from '../../store/slices/ClassroomSlice';

const ExamAssignment = ({
  assignmentType = 'classroom',
  onAssignmentTypeChange,
  classId = '',
  onClassChange,
  selectedStudentIds = [],
  onSelectedStudentsChange
}) => {
  const dispatch = useDispatch();
  const { 
    classrooms = [], 
    teacherStudents = [], 
    loading: classroomsLoading,
    studentsLoading 
  } = useSelector(state => state.classroom);
  
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch classrooms when component mounts
  useEffect(() => {
    dispatch(fetchAllOwnClasses());
  }, [dispatch]);

  // Fetch students when assignment type is 'students'
  useEffect(() => {
    if (assignmentType === 'students') {
      dispatch(fetchTeacherStudents());
    }
  }, [dispatch, assignmentType]);

  // Filter students based on search term
  const filteredStudents = teacherStudents.filter(student =>
    student.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle student selection
  const handleStudentToggle = (studentId) => {
    const newSelectedIds = selectedStudentIds.includes(studentId)
      ? selectedStudentIds.filter(id => id !== studentId)
      : [...selectedStudentIds, studentId];
    onSelectedStudentsChange(newSelectedIds);
  };

  const handleSelectAll = () => {
    onSelectedStudentsChange(filteredStudents.map(student => student.id));
  };

  const handleClearAll = () => {
    onSelectedStudentsChange([]);
  };

  return (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <FiUsers className="text-purple-600" />
        Assign Exam To
      </h3>

      {/* Assignment Type Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-3">
          Assignment Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-2 gap-4">
          <label className="cursor-pointer">
            <input
              type="radio"
              name="assignmentType"
              value="classroom"
              checked={assignmentType === 'classroom'}
              onChange={(e) => onAssignmentTypeChange(e.target.value)}
              className="sr-only"
            />
            <div className={`p-4 border-2 rounded-lg transition-all ${
              assignmentType === 'classroom' 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-blue-300'
            }`}>
              <div className="flex items-center gap-3">
                <FiUsers className="text-blue-600" />
                <div>
                  <div className="font-medium">Entire Classroom</div>
                  <div className="text-sm text-gray-600">Assign to all students in a classroom</div>
                </div>
              </div>
            </div>
          </label>

          <label className="cursor-pointer">
            <input
              type="radio"
              name="assignmentType"
              value="students"
              checked={assignmentType === 'students'}
              onChange={(e) => onAssignmentTypeChange(e.target.value)}
              className="sr-only"
            />
            <div className={`p-4 border-2 rounded-lg transition-all ${
              assignmentType === 'students' 
                ? 'border-green-500 bg-green-50' 
                : 'border-gray-300 hover:border-green-300'
            }`}>
              <div className="flex items-center gap-3">
                <FiUser className="text-green-600" />
                <div>
                  <div className="font-medium">Specific Students</div>
                  <div className="text-sm text-gray-600">Choose individual students</div>
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* Classroom Selection */}
      {assignmentType === 'classroom' && (
        <div>
          <label className="block text-sm font-medium mb-2">
            Select Classroom <span className="text-red-500">*</span>
          </label>
          
          {classroomsLoading ? (
            <div className="p-4 text-center text-gray-500">Loading classrooms...</div>
          ) : (
            <>
              <select
                value={classId}
                onChange={onClassChange}
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select Classroom</option>
                {classrooms.map(classroom => (
                  <option key={classroom.id} value={classroom.id}>
                    {classroom.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {classrooms.length} classroom(s) available
              </p>
            </>
          )}
        </div>
      )}

      {/* Student Selection */}
      {assignmentType === 'students' && (
        <div>
          <div className="flex justify-between items-center mb-3">
            <label className="block text-sm font-medium">
              Select Students <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleSelectAll}
                className="text-xs px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                disabled={filteredStudents.length === 0}
              >
                Select All
              </button>
              <button
                type="button"
                onClick={handleClearAll}
                className="text-xs px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                disabled={selectedStudentIds.length === 0}
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Search Input */}
          <div className="relative mb-3">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search students by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Students List */}
          <div className="border rounded-lg max-h-64 overflow-y-auto">
            {studentsLoading ? (
              <div className="p-4 text-center text-gray-500">Loading students...</div>
            ) : filteredStudents.length > 0 ? (
              <div className="p-2">
                {filteredStudents.map(student => (
                  <label 
                    key={student.id} 
                    className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer transition-colors"
                  >
                    <input
                      type="checkbox"
                      checked={selectedStudentIds.includes(student.id)}
                      onChange={() => handleStudentToggle(student.id)}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium">
                        {student.username || student.name || 'Unknown'}
                      </div>
                      {student.email && (
                        <div className="text-xs text-gray-500">{student.email}</div>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? 'No students found matching your search' : 'No students available'}
              </div>
            )}
          </div>

          {/* Selection Summary */}
          <div className="mt-3 flex justify-between items-center text-sm">
            <span className="text-gray-600">
              Showing {filteredStudents.length} of {teacherStudents.length} students
            </span>
            {selectedStudentIds.length > 0 && (
              <span className="text-green-600 font-medium">
                {selectedStudentIds.length} student(s) selected
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamAssignment;
