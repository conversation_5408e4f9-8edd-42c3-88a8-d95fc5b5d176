import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  getMentorDashboard,
  getAssignedCompetitions,
  getCompetitionSubmissions,
  getSubmissionDetails,
  markSubmission,
  getCompetitionRankings,
  generateWinnerCertificate,
  getCompetitionCertificates
} from '../../services/mentorEvaluationService';

// Async Thunks

// Fetch mentor dashboard data
export const fetchMentorDashboard = createAsyncThunk(
  'mentorEvaluation/fetchDashboard',
  async (_, { rejectWithValue }) => {
    try {
      return await getMentorDashboard();
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch assigned competitions
export const fetchAssignedCompetitions = createAsyncThunk(
  'mentorEvaluation/fetchAssignedCompetitions',
  async (_, { rejectWithValue }) => {
    try {
      return await getAssignedCompetitions();
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch competition submissions
export const fetchCompetitionSubmissions = createAsyncThunk(
  'mentorEvaluation/fetchCompetitionSubmissions',
  async ({ competitionId, params }, { rejectWithValue }) => {
    try {
      return await getCompetitionSubmissions(competitionId, params);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch submission details
export const fetchSubmissionDetails = createAsyncThunk(
  'mentorEvaluation/fetchSubmissionDetails',
  async (attemptId, { rejectWithValue }) => {
    try {
      return await getSubmissionDetails(attemptId);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Submit marking
export const submitMarking = createAsyncThunk(
  'mentorEvaluation/submitMarking',
  async (markingData, { rejectWithValue }) => {
    try {
      return await markSubmission(markingData);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch competition rankings
export const fetchCompetitionRankings = createAsyncThunk(
  'mentorEvaluation/fetchCompetitionRankings',
  async (competitionId, { rejectWithValue }) => {
    try {
      return await getCompetitionRankings(competitionId);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Generate certificate
export const generateCertificate = createAsyncThunk(
  'mentorEvaluation/generateCertificate',
  async ({ competitionId, certificateData }, { rejectWithValue }) => {
    try {
      return await generateWinnerCertificate(competitionId, certificateData);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Fetch certificates
export const fetchCertificates = createAsyncThunk(
  'mentorEvaluation/fetchCertificates',
  async (competitionId, { rejectWithValue }) => {
    try {
      return await getCompetitionCertificates(competitionId);
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Initial state
const initialState = {
  // Dashboard
  dashboard: {
    data: null,
    loading: false,
    error: null
  },
  
  // Competitions
  competitions: {
    data: [],
    loading: false,
    error: null
  },
  
  // Submissions
  submissions: {
    data: [],
    totalCount: 0,
    currentPage: 1,
    loading: false,
    error: null
  },
  
  // Current submission details
  currentSubmission: {
    data: null,
    loading: false,
    error: null
  },
  
  // Marking
  marking: {
    loading: false,
    error: null,
    success: false
  },
  
  // Rankings
  rankings: {
    data: [],
    loading: false,
    error: null
  },
  
  // Certificates
  certificates: {
    data: [],
    loading: false,
    error: null,
    generating: false,
    generateError: null
  }
};

// Slice
const mentorEvaluationSlice = createSlice({
  name: 'mentorEvaluation',
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.dashboard.error = null;
      state.competitions.error = null;
      state.submissions.error = null;
      state.currentSubmission.error = null;
      state.marking.error = null;
      state.rankings.error = null;
      state.certificates.error = null;
      state.certificates.generateError = null;
    },
    
    // Clear marking success
    clearMarkingSuccess: (state) => {
      state.marking.success = false;
    },
    
    // Set submissions page
    setSubmissionsPage: (state, action) => {
      state.submissions.currentPage = action.payload;
    },
    
    // Clear current submission
    clearCurrentSubmission: (state) => {
      state.currentSubmission.data = null;
      state.currentSubmission.error = null;
    }
  },
  extraReducers: (builder) => {
    // Dashboard
    builder
      .addCase(fetchMentorDashboard.pending, (state) => {
        state.dashboard.loading = true;
        state.dashboard.error = null;
      })
      .addCase(fetchMentorDashboard.fulfilled, (state, action) => {
        state.dashboard.loading = false;
        state.dashboard.data = action.payload;
      })
      .addCase(fetchMentorDashboard.rejected, (state, action) => {
        state.dashboard.loading = false;
        state.dashboard.error = action.payload;
      });

    // Competitions
    builder
      .addCase(fetchAssignedCompetitions.pending, (state) => {
        state.competitions.loading = true;
        state.competitions.error = null;
      })
      .addCase(fetchAssignedCompetitions.fulfilled, (state, action) => {
        state.competitions.loading = false;
        state.competitions.data = action.payload;
      })
      .addCase(fetchAssignedCompetitions.rejected, (state, action) => {
        state.competitions.loading = false;
        state.competitions.error = action.payload;
      });

    // Submissions
    builder
      .addCase(fetchCompetitionSubmissions.pending, (state) => {
        state.submissions.loading = true;
        state.submissions.error = null;
      })
      .addCase(fetchCompetitionSubmissions.fulfilled, (state, action) => {
        state.submissions.loading = false;
        state.submissions.data = action.payload.submissions || [];
        state.submissions.totalCount = action.payload.total_count || 0;
      })
      .addCase(fetchCompetitionSubmissions.rejected, (state, action) => {
        state.submissions.loading = false;
        state.submissions.error = action.payload;
      });

    // Submission details
    builder
      .addCase(fetchSubmissionDetails.pending, (state) => {
        state.currentSubmission.loading = true;
        state.currentSubmission.error = null;
      })
      .addCase(fetchSubmissionDetails.fulfilled, (state, action) => {
        state.currentSubmission.loading = false;
        state.currentSubmission.data = action.payload;
      })
      .addCase(fetchSubmissionDetails.rejected, (state, action) => {
        state.currentSubmission.loading = false;
        state.currentSubmission.error = action.payload;
      });

    // Marking
    builder
      .addCase(submitMarking.pending, (state) => {
        state.marking.loading = true;
        state.marking.error = null;
        state.marking.success = false;
      })
      .addCase(submitMarking.fulfilled, (state, action) => {
        state.marking.loading = false;
        state.marking.success = true;
      })
      .addCase(submitMarking.rejected, (state, action) => {
        state.marking.loading = false;
        state.marking.error = action.payload;
      });

    // Rankings
    builder
      .addCase(fetchCompetitionRankings.pending, (state) => {
        state.rankings.loading = true;
        state.rankings.error = null;
      })
      .addCase(fetchCompetitionRankings.fulfilled, (state, action) => {
        state.rankings.loading = false;
        state.rankings.data = action.payload;
      })
      .addCase(fetchCompetitionRankings.rejected, (state, action) => {
        state.rankings.loading = false;
        state.rankings.error = action.payload;
      });

    // Certificate generation
    builder
      .addCase(generateCertificate.pending, (state) => {
        state.certificates.generating = true;
        state.certificates.generateError = null;
      })
      .addCase(generateCertificate.fulfilled, (state, action) => {
        state.certificates.generating = false;
        // Add the new certificate to the list if it exists
        if (state.certificates.data && Array.isArray(state.certificates.data)) {
          state.certificates.data.push(action.payload);
        }
      })
      .addCase(generateCertificate.rejected, (state, action) => {
        state.certificates.generating = false;
        state.certificates.generateError = action.payload;
      });

    // Certificates
    builder
      .addCase(fetchCertificates.pending, (state) => {
        state.certificates.loading = true;
        state.certificates.error = null;
      })
      .addCase(fetchCertificates.fulfilled, (state, action) => {
        state.certificates.loading = false;
        state.certificates.data = action.payload;
      })
      .addCase(fetchCertificates.rejected, (state, action) => {
        state.certificates.loading = false;
        state.certificates.error = action.payload;
      });
  }
});

// Actions
export const { 
  clearErrors, 
  clearMarkingSuccess, 
  setSubmissionsPage, 
  clearCurrentSubmission 
} = mentorEvaluationSlice.actions;

// Selectors
export const selectDashboard = (state) => state.mentorEvaluation.dashboard;
export const selectCompetitions = (state) => state.mentorEvaluation.competitions;
export const selectSubmissions = (state) => state.mentorEvaluation.submissions;
export const selectCurrentSubmission = (state) => state.mentorEvaluation.currentSubmission;
export const selectMarking = (state) => state.mentorEvaluation.marking;
export const selectRankings = (state) => state.mentorEvaluation.rankings;
export const selectCertificates = (state) => state.mentorEvaluation.certificates;

export default mentorEvaluationSlice.reducer;
