mentorEvaluation


GET
/api/mentor/evaluation/dashboard
Get Mentor Dashboard


Get dashboard statistics for the current mentor. Shows overview of assigned competitions, submissions, and progress.

Parameters
Try it out
No parameters

Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
{
  "total_competitions": 0,
  "active_competitions": 0,
  "total_submissions": 0,
  "pending_submissions": 0,
  "completed_submissions": 0,
  "average_score": 0,
  "total_workload": 0,
  "current_workload": 0
}
No links

GET
/api/mentor/evaluation/competitions
Get Assigned Competitions


Get all competitions assigned to the current mentor. Returns basic information and evaluation progress for each competition.

Parameters
Try it out
No parameters

Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "assignment_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "competition_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "competition_title": "string",
    "exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "assignment_status": "string",
    "total_submissions": 0,
    "checked_submissions": 0,
    "progress_percentage": 0,
    "assigned_at": "2025-09-20T08:31:50.379Z",
    "workload_capacity": 0,
    "current_workload": 0
  }
]
No links

GET
/api/mentor/evaluation/competitions/{competition_id}/submissions
Get Competition Submissions


Get all submissions for a specific competition that the mentor is assigned to evaluate.

Query Parameters:

skip: Number of records to skip for pagination
limit: Maximum number of records to return (1-100)
status: Filter submissions by status ('pending', 'checked', 'all')
Parameters
Try it out
Name	Description
competition_id *
string($uuid)
(path)
competition_id
skip
integer
(query)
Number of records to skip

Default value : 0

0
minimum: 0
limit
integer
(query)
Maximum number of records to return

Default value : 50

50
maximum: 100
minimum: 1
status
string | (string | null)
(query)
Filter by status

status
pattern: ^(pending|checked|all)$
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
{
  "submissions": [
    {
      "attempt_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "student_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "student_name": "string",
      "student_email": "string",
      "exam_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "exam_title": "string",
      "submitted_at": "2025-09-20T08:31:50.389Z",
      "duration_seconds": 0,
      "answer_count": 0,
      "is_evaluated_by_mentor": true,
      "mentor_score": 0,
      "mentor_feedback": "string",
      "evaluated_at": "2025-09-20T08:31:50.389Z"
    }
  ],
  "total_count": 0,
  "page": 0,
  "limit": 0,
  "has_next": true,
  "has_previous": true,
  "competition_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_title": "string",
  "mentor_assignment_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

GET
/api/mentor/evaluation/submissions/{attempt_id}
Get Submission Details


Get detailed information about a specific submission for evaluation. Includes all student answers, questions, and current evaluation status.

Parameters
Try it out
Name	Description
attempt_id *
string($uuid)
(path)
attempt_id
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
{
  "attempt_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "student": {
    "additionalProp1": {}
  },
  "exam": {
    "additionalProp1": {}
  },
  "competition": {
    "additionalProp1": {}
  },
  "submission": {
    "additionalProp1": {}
  },
  "answers": [
    {
      "additionalProp1": {}
    }
  ],
  "mentor_evaluation": {
    "additionalProp1": {}
  }
}
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

POST
/api/mentor/evaluation/submissions/mark
Mark Submission


Mark a competition submission with scores and feedback.

Request Body:

attempt_id: ID of the submission to mark
total_score: Total score awarded
overall_feedback: General feedback for the submission
question_scores: Individual scores and feedback for each question
Note: Total score must equal the sum of individual question scores.

Parameters
Try it out
No parameters

Request body

application/json
Example Value
Schema
{
  "attempt_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "total_score": 0,
  "overall_feedback": "stringstri",
  "question_scores": [
    {
      "question_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "score": 0,
      "feedback": "string"
    }
  ]
}
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
{
  "success": true,
  "message": "string",
  "attempt_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "total_score": 0,
  "max_possible_score": 0,
  "percentage": 0,
  "marked_at": "2025-09-20T08:31:50.395Z",
  "mentor_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

GET
/api/mentor/evaluation/submissions/{attempt_id}/status
Get Submission Evaluation Status


Get the current evaluation status of a submission. Returns whether the submission has been evaluated and by whom.

Parameters
Try it out
Name	Description
attempt_id *
string($uuid)
(path)
attempt_id
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
"string"
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

GET
/api/mentor/evaluation/competitions/{competition_id}/progress
Get Competition Evaluation Progress


Get detailed evaluation progress for a specific competition. Shows statistics about submissions and mentor progress.

Parameters
Try it out
Name	Description
competition_id *
string($uuid)
(path)
competition_id
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
"string"
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

GET
/api/mentor/evaluation/competitions/{competition_id}/rankings
Get Competition Rankings


Get the current rankings for a competition based on mentor evaluations. Shows all participants ranked by their mentor-evaluated scores.

Parameters
Try it out
Name	Description
competition_id *
string($uuid)
(path)
competition_id
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
"string"
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

POST
/api/mentor/evaluation/competitions/{competition_id}/certificates/generate
Generate Winner Certificate


Generate a certificate for a competition winner.

Request Body:

student_id: ID of the student to award certificate to
position: Position achieved (1=First, 2=Second, 3=Third)
certificate_type: Type of certificate to generate
custom_message: Optional custom message for the certificate
Note: Only mentors assigned to the competition can generate certificates. The student's position will be verified against current rankings.

Parameters
Try it out
Name	Description
competition_id *
string($uuid)
(path)
competition_id
Request body

application/json
Example Value
Schema
{
  "competition_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "student_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "position": 1,
  "certificate_type": "string",
  "custom_message": "string"
}
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
{
  "success": true,
  "message": "string",
  "certificate_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "certificate_url": "string",
  "student_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "competition_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "position": 0,
  "generated_at": "2025-09-20T08:31:50.404Z"
}
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}
No links

GET
/api/mentor/evaluation/competitions/{competition_id}/certificates
Get Competition Certificates List


Get all certificates issued for a competition. Shows all certificates with participant details and status.

Parameters
Try it out
Name	Description
competition_id *
string($uuid)
(path)
competition_id
Responses
Code	Description	Links
200	
Successful Response

Media type

application/json
Controls Accept header.
Example Value
Schema
"string"
No links
422	
Validation Error

Media type

application/json
Example Value
Schema
{
  "detail": [
    {
      "loc": [
        "string",
        0
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}