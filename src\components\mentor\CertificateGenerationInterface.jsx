import React, { useState, useEffect } from 'react';
import {
  FiA<PERSON>,
  FiUser,
  FiFileText,
  FiDownload,
  FiX,
  FiCheck,
  FiStar,
  FiCircle
} from 'react-icons/fi';
import { 
  generateWinnerCertificate, 
  getCompetitionCertificates,
  getPositionSuffix 
} from '../../services/mentorEvaluationService';
import { LoadingSpinner, ErrorMessage } from '../ui';

const CertificateGenerationInterface = ({ 
  competitionId, 
  participant, 
  position, 
  isOpen, 
  onClose, 
  onCertificateGenerated 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [existingCertificates, setExistingCertificates] = useState([]);
  const [certificateData, setCertificateData] = useState({
    certificate_type: 'winner',
    custom_message: ''
  });

  useEffect(() => {
    if (isOpen && competitionId) {
      loadExistingCertificates();
      initializeCertificateData();
    }
  }, [isOpen, competitionId, participant, position]);

  const loadExistingCertificates = async () => {
    try {
      const certificates = await getCompetitionCertificates(competitionId);
      setExistingCertificates(Array.isArray(certificates) ? certificates : []);
    } catch (err) {
      console.error('Error loading certificates:', err);
      // Don't show error for this, as it's not critical
    }
  };

  const initializeCertificateData = () => {
    if (!participant || !position) return;
    
    const positionText = getPositionSuffix(position);
    const defaultMessage = `Congratulations on achieving ${positionText} place in this competition! Your outstanding performance demonstrates exceptional skill and dedication.`;
    
    setCertificateData({
      certificate_type: position === 1 ? 'first_place' : position === 2 ? 'second_place' : position === 3 ? 'third_place' : 'winner',
      custom_message: defaultMessage
    });
  };

  const handleGenerateCertificate = async () => {
    if (!participant || !position) return;

    try {
      setLoading(true);
      setError(null);

      // Debug: Log participant structure
      console.log('Certificate generation - participant data:', participant);
      console.log('Certificate generation - position:', position);

      // Extract student ID from various possible field names
      const studentId = participant.student_id ||
                       participant.participant_id ||
                       participant.id ||
                       participant.user_id;

      if (!studentId) {
        console.error('Student ID not found. Participant object:', participant);
        throw new Error(`Student ID not found in participant data. Available fields: ${Object.keys(participant).join(', ')}`);
      }

      const requestData = {
        competition_id: competitionId,  // ✅ Required in request body
        student_id: studentId,          // ✅ Required in request body
        position: position,
        certificate_type: certificateData.certificate_type,
        custom_message: certificateData.custom_message.trim() || undefined
      };

      console.log('Certificate generation - request data:', requestData);
      
      const response = await generateWinnerCertificate(competitionId, requestData);
      
      if (onCertificateGenerated) {
        onCertificateGenerated(response);
      }
      
      // Reload certificates list
      await loadExistingCertificates();
      
    } catch (err) {
      console.error('Error generating certificate:', err);
      setError(err.response?.data?.message || 'Failed to generate certificate');
    } finally {
      setLoading(false);
    }
  };

  const getPositionIcon = (pos) => {
    switch (pos) {
      case 1:
        return <FiAward className="h-6 w-6 text-yellow-500" />;
      case 2:
        return <FiCircle className="h-6 w-6 text-gray-400" />;
      case 3:
        return <FiStar className="h-6 w-6 text-amber-600" />;
      default:
        return <FiUser className="h-6 w-6 text-blue-500" />;
    }
  };

  const getPositionColor = (pos) => {
    switch (pos) {
      case 1:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 2:
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 3:
        return 'text-amber-600 bg-amber-50 border-amber-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const certificateTypes = [
    { value: 'first_place', label: 'First Place Certificate', description: 'Gold medal design for 1st place' },
    { value: 'second_place', label: 'Second Place Certificate', description: 'Silver medal design for 2nd place' },
    { value: 'third_place', label: 'Third Place Certificate', description: 'Bronze medal design for 3rd place' },
    { value: 'winner', label: 'Winner Certificate', description: 'General winner certificate' },
    { value: 'participation', label: 'Participation Certificate', description: 'Certificate of participation' }
  ];

  const hasExistingCertificate = existingCertificates.some(cert => 
    cert.student_id === (participant?.participant_id || participant?.student_id) && 
    cert.position === position
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <FiAward className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Generate Certificate</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {hasExistingCertificate && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              A certificate has already been generated for this participant and position.
            </p>
          </div>
        )}

        {/* Participant Information */}
        <div className={`mb-6 p-4 border rounded-lg ${getPositionColor(position)}`}>
          <div className="flex items-center space-x-4">
            {getPositionIcon(position)}
            <div className="flex-1">
              <h4 className="font-semibold text-gray-900">
                {participant?.participant_name || participant?.name || 'Unknown Participant'}
              </h4>
              <p className="text-sm text-gray-600">
                {participant?.participant_email || participant?.email || 'No email'}
              </p>
              <p className="text-sm font-medium mt-1">
                Position: {getPositionSuffix(position)} Place
              </p>
            </div>
          </div>
        </div>

        {/* Certificate Type Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Certificate Type
          </label>
          <div className="space-y-2">
            {certificateTypes.map((type) => (
              <label key={type.value} className="flex items-center">
                <input
                  type="radio"
                  name="certificate_type"
                  value={type.value}
                  checked={certificateData.certificate_type === type.value}
                  onChange={(e) => setCertificateData(prev => ({ ...prev, certificate_type: e.target.value }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="ml-3">
                  <span className="text-sm font-medium text-gray-900">{type.label}</span>
                  <p className="text-xs text-gray-500">{type.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Custom Message */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Custom Message (Optional)
          </label>
          <textarea
            value={certificateData.custom_message}
            onChange={(e) => setCertificateData(prev => ({ ...prev, custom_message: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter a custom message for the certificate..."
          />
          <p className="text-xs text-gray-500 mt-1">
            This message will appear on the certificate. Leave blank for default message.
          </p>
        </div>

        {/* Existing Certificates */}
        {existingCertificates.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Existing Certificates</h4>
            <div className="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
              {existingCertificates.map((cert, index) => (
                <div key={index} className="flex items-center justify-between py-1">
                  <span className="text-sm text-gray-600">
                    {cert.student_name} - {getPositionSuffix(cert.position)} Place
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(cert.generated_at).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            onClick={handleGenerateCertificate}
            disabled={loading || !participant}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading && <LoadingSpinner size="sm" className="mr-2" />}
            <FiDownload className="h-4 w-4 mr-2" />
            {loading ? 'Generating...' : 'Generate Certificate'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CertificateGenerationInterface;
