import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { isFormReadyForPublishing, getMissingRequiredFields } from '../../utils/eventValidation';
import useEventForm from '../../hooks/useEventForm';

// Import tab components
import EventHeader from '../../components/events/create/EventHeader';
import TabNavigation from '../../components/events/create/TabNavigation';
import BasicInfoTab from '../../components/events/create/BasicInfoTab';
import DetailsTab from '../../components/events/create/DetailsTab';
import PeopleTab from '../../components/events/create/PeopleTab';
import SettingsTab from '../../components/events/create/SettingsTab';
import TicketsTab from '../../components/events/create/TicketsTab';
import ActionButtons from '../../components/events/create/ActionButtons';

// Validation summary component
const ValidationSummary = ({ formData, isVisible }) => {
  const missingFields = getMissingRequiredFields(formData);

  if (!isVisible || missingFields.length === 0) return null;

  const fieldLabels = {
    title: 'Event Title',
    description: 'Description',
    short_description: 'Short Description',
    start_datetime: 'Start Date & Time',
    end_datetime: 'End Date & Time',
    category: 'Category',
    registration_start: 'Registration Start',
    registration_end: 'Registration Deadline',
    exam_id: 'Competition Exam'
  };

  return (
    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Complete required fields to publish
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <ul className="list-disc pl-5 space-y-1">
              {missingFields.map((field) => (
                <li key={field}>{fieldLabels[field] || field}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

const CreateEventPage = () => {
  const navigate = useNavigate();
  const {
    activeTab,
    formData,
    selectedSpeakers,
    selectedMentors,
    tickets,
    referenceExams,
    showValidationSummary,
    touchedFields,
    hasAttemptedSubmit,
    isSubmitting,
    createError,
    createSuccess,
    setActiveTab,
    handleFieldChange,
    handleFieldTouch,
    setSelectedSpeakers,
    setSelectedMentors,
    setTickets,
    handleAddTicket,
    handleRemoveTicket,
    handleSubmit,
    handleShowValidation
  } = useEventForm();

  // Handle successful creation
  useEffect(() => {
    if (createSuccess) {
      navigate('/institute/events');
    }
  }, [createSuccess, navigate]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <BasicInfoTab
            formData={formData}
            touchedFields={touchedFields}
            hasAttemptedSubmit={hasAttemptedSubmit}
            referenceExams={referenceExams}
            onChange={handleFieldChange}
            onFieldTouch={handleFieldTouch}
          />
        );
      case 'details':
        return (
          <DetailsTab
            formData={formData}
            touchedFields={touchedFields}
            hasAttemptedSubmit={hasAttemptedSubmit}
            onChange={handleFieldChange}
            onFieldTouch={handleFieldTouch}
          />
        );
      case 'people':
        return (
          <PeopleTab
            formData={formData}
            selectedSpeakers={selectedSpeakers}
            selectedMentors={selectedMentors}
            onSpeakersChange={setSelectedSpeakers}
            onMentorsChange={setSelectedMentors}
          />
        );
      case 'settings':
        return (
          <SettingsTab
            formData={formData}
            touchedFields={touchedFields}
            hasAttemptedSubmit={hasAttemptedSubmit}
            onChange={handleFieldChange}
            onFieldTouch={handleFieldTouch}
          />
        );
      case 'tickets':
        return (
          <TicketsTab
            tickets={tickets}
            formData={formData}
            onTicketsChange={setTickets}
            onAddTicket={handleAddTicket}
            onRemoveTicket={handleRemoveTicket}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <EventHeader onBack={() => navigate(-1)} />
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">{renderTabContent()}</div>
        </div>
        <ValidationSummary formData={formData} isVisible={showValidationSummary} />
        <ActionButtons
          onSaveDraft={() => handleSubmit('DRAFT')}
          onPublish={() => handleSubmit('PUBLISHED')}
          onShowValidation={handleShowValidation}
          isSubmitting={isSubmitting}
          formData={formData}
        />
      </div>
      {isSubmitting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <LoadingSpinner size="lg" />
        </div>
      )}
      {createError && (
        <div className="fixed bottom-4 right-4 max-w-md">
          <ErrorMessage message={createError} />
        </div>
      )}
    </>
  );
};

export default CreateEventPage;