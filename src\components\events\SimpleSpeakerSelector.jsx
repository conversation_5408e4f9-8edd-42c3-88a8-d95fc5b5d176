/**
 * Professional Speaker Multi-Select Component
 * Beautiful UI with complete speaker information
 */

import { useState, useEffect } from 'react';
import { Fi<PERSON>ser, FiMail, FiPhone, FiGlobe, FiLinkedin, FiTwitter, FiStar, FiCheck } from 'react-icons/fi';
import speakerService from '../../services/speakerService';

const SimpleSpeakerSelector = ({ 
  selectedSpeakers = [], 
  onSpeakersChange 
}) => {
  const [speakers, setSpeakers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadSpeakers();
  }, []);

  const loadSpeakers = async () => {
    try {
      setLoading(true);
      const speakers = await speakerService.getAllSpeakers();
      console.log('📢 Loaded speakers:', speakers);
      setSpeakers(speakers || []);
    } catch (err) {
      console.error('❌ Error loading speakers:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSpeakerChange = (speakerId, isChecked) => {
    if (isChecked) {
      onSpeakersChange([...selectedSpeakers, speakerId]);
    } else {
      onSpeakersChange(selectedSpeakers.filter(id => id !== speakerId));
    }
  };

  if (loading) return <div>Loading speakers...</div>;
  if (error) return <div>Error loading speakers: {error}</div>;

  console.log('📢 SimpleSpeakerSelector render:', {
    speakersCount: speakers?.length,
    selectedSpeakers,
    loading,
    error
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Event Speakers</h3>
          <p className="text-sm text-gray-600 mt-1">
            Select speakers for your event ({speakers?.length || 0} available)
          </p>
        </div>
        {selectedSpeakers.length > 0 && (
          <div className="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
            {selectedSpeakers.length} selected
          </div>
        )}
      </div>

      {/* Empty State */}
      {speakers?.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <FiUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No speakers available</h3>
          <p className="text-gray-500">Add speakers to your system to select them for events.</p>
        </div>
      )}

      {/* Speaker Grid */}
      {speakers?.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {speakers.map((speaker) => {
            const isSelected = selectedSpeakers.includes(speaker.id);

            return (
              <div
                key={speaker.id}
                className={`relative bg-white rounded-xl border-2 transition-all duration-200 hover:shadow-lg cursor-pointer ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleSpeakerChange(speaker.id, !isSelected)}
              >
                {/* Selection Indicator */}
                <div className={`absolute top-4 right-4 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                  isSelected
                    ? 'bg-blue-500 border-blue-500'
                    : 'border-gray-300 bg-white'
                }`}>
                  {isSelected && <FiCheck className="w-4 h-4 text-white" />}
                </div>

                {/* Featured Badge */}
                {speaker.is_featured && (
                  <div className="absolute top-4 left-4 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                    <FiStar className="w-3 h-3 mr-1" />
                    Featured
                  </div>
                )}

                <div className="p-6">
                  {/* Profile Section */}
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="flex-shrink-0">
                      {speaker.profile_image_url ? (
                        <img
                          src={speaker.profile_image_url}
                          alt={speaker.name}
                          className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                          <FiUser className="w-8 h-8 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-lg font-semibold text-gray-900 truncate">
                        {speaker.name}
                      </h4>
                      <p className="text-blue-600 font-medium text-sm truncate">
                        {speaker.title}
                      </p>
                      {speaker.company && (
                        <p className="text-gray-500 text-sm truncate">
                          {speaker.company}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Bio */}
                  {speaker.bio && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed">
                      {speaker.bio}
                    </p>
                  )}

                  {/* Expertise Areas */}
                  {speaker.expertise_areas && speaker.expertise_areas.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {speaker.expertise_areas.slice(0, 3).map((area, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {area}
                          </span>
                        ))}
                        {speaker.expertise_areas.length > 3 && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            +{speaker.expertise_areas.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Contact Information */}
                  <div className="flex items-center space-x-3 text-gray-400">
                    {speaker.email && (
                      <a
                        href={`mailto:${speaker.email}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-blue-600 transition-colors"
                        title={speaker.email}
                      >
                        <FiMail className="w-4 h-4" />
                      </a>
                    )}
                    {speaker.phone && (
                      <a
                        href={`tel:${speaker.phone}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-blue-600 transition-colors"
                        title={speaker.phone}
                      >
                        <FiPhone className="w-4 h-4" />
                      </a>
                    )}
                    {speaker.website && (
                      <a
                        href={speaker.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-blue-600 transition-colors"
                        title="Website"
                      >
                        <FiGlobe className="w-4 h-4" />
                      </a>
                    )}
                    {speaker.linkedin_url && (
                      <a
                        href={speaker.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-blue-600 transition-colors"
                        title="LinkedIn"
                      >
                        <FiLinkedin className="w-4 h-4" />
                      </a>
                    )}
                    {speaker.twitter_url && (
                      <a
                        href={speaker.twitter_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-blue-600 transition-colors"
                        title="Twitter"
                      >
                        <FiTwitter className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Selection Summary */}
      {selectedSpeakers.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold text-blue-900">
                {selectedSpeakers.length} Speaker{selectedSpeakers.length !== 1 ? 's' : ''} Selected
              </h4>
              <p className="text-blue-700 text-sm mt-1">
                These speakers will be associated with your event
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <FiCheck className="w-5 h-5 text-blue-600" />
              <span className="text-blue-600 font-medium">Ready to proceed</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleSpeakerSelector;
