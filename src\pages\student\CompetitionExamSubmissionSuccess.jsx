import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  FiCheckCircle,
  FiClock,
  FiAward,
  FiUsers,
  FiCalendar,
  FiArrowRight,
  FiHome,
  FiEye
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../../components/ui';

/**
 * Competition Exam Submission Success Page
 * 
 * Displays success message and next steps after completing a competition exam.
 * Shows competition-specific information and guidance.
 */
const CompetitionExamSubmissionSuccess = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccess } = useNotification();
  
  // Get submission data from navigation state
  const {
    examTitle = 'Competition Exam',
    submissionTime,
    competitionEvent,
    eventId,
    autoSubmitted = false,
    autoAITriggered = false
  } = location.state || {};

  const [timeElapsed, setTimeElapsed] = useState('');

  useEffect(() => {
    // Show success notification
    showSuccess('Competition exam submitted successfully!');
    
    // Calculate time elapsed if submission time is available
    if (submissionTime) {
      const submitted = new Date(submissionTime);
      const now = new Date();
      const diffMinutes = Math.floor((now - submitted) / (1000 * 60));
      
      if (diffMinutes < 1) {
        setTimeElapsed('Just now');
      } else if (diffMinutes < 60) {
        setTimeElapsed(`${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`);
      } else {
        const diffHours = Math.floor(diffMinutes / 60);
        setTimeElapsed(`${diffHours} hour${diffHours > 1 ? 's' : ''} ago`);
      }
    }
  }, [submissionTime, showSuccess]);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleViewMyEvents = () => {
    navigate('/student/events/my');
  };

  const handleViewCompetitionDetails = () => {
    if (eventId) {
      navigate(`/events/${eventId}`);
    } else {
      navigate('/student/events/my');
    }
  };

  const handleGoHome = () => {
    navigate('/student/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <FiCheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Competition Exam Submitted!
          </h1>
          <p className="text-lg text-gray-600">
            Your answers have been successfully recorded and are being processed.
          </p>
        </div>

        {/* Main Content Card */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Competition Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-8 text-white">
            <div className="flex items-center mb-4">
              <FiAward className="h-6 w-6 mr-3" />
              <h2 className="text-xl font-semibold">Competition Details</h2>
            </div>
            
            {competitionEvent ? (
              <div className="space-y-2">
                <h3 className="text-2xl font-bold">{competitionEvent.title}</h3>
                <p className="text-purple-100">{examTitle}</p>
                {competitionEvent.description && (
                  <p className="text-purple-100 text-sm mt-2">
                    {competitionEvent.description}
                  </p>
                )}
              </div>
            ) : (
              <div>
                <h3 className="text-2xl font-bold">{examTitle}</h3>
                <p className="text-purple-100">Competition Exam</p>
              </div>
            )}
          </div>

          {/* Submission Details */}
          <div className="p-6 space-y-6">
            {/* Submission Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <FiClock className="h-5 w-5 text-gray-600 mr-2" />
                  <span className="font-medium text-gray-900">Submitted</span>
                </div>
                <p className="text-gray-600">
                  {submissionTime ? formatDate(submissionTime) : 'Just now'}
                  {timeElapsed && (
                    <span className="text-sm text-gray-500 block">{timeElapsed}</span>
                  )}
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <FiAward className="h-5 w-5 text-gray-600 mr-2" />
                  <span className="font-medium text-gray-900">Status</span>
                </div>
                <p className="text-green-600 font-medium">
                  {autoSubmitted ? 'Auto-submitted' : 'Successfully Submitted'}
                </p>
              </div>
            </div>

            {/* AI Processing Notice */}
            {autoAITriggered && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <LoadingSpinner size="sm" className="text-blue-600" />
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-blue-900">
                      AI Evaluation in Progress
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Your exam is being automatically evaluated. Results will be available shortly.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Next Steps */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What happens next?</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-purple-600">1</span>
                  </div>
                  <div>
                    <p className="text-gray-900 font-medium">Automatic Evaluation</p>
                    <p className="text-gray-600 text-sm">Your answers are being processed by our AI system for immediate scoring.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-purple-600">2</span>
                  </div>
                  <div>
                    <p className="text-gray-900 font-medium">Results & Ranking</p>
                    <p className="text-gray-600 text-sm">Competition results will be published according to the event schedule.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-purple-600">3</span>
                  </div>
                  <div>
                    <p className="text-gray-900 font-medium">Prize Distribution</p>
                    <p className="text-gray-600 text-sm">Winners will be contacted directly for prize distribution.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Prize Information */}
            {competitionEvent?.prize_details && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-900 mb-3 flex items-center">
                  <FiAward className="h-4 w-4 mr-2" />
                  Competition Prizes
                </h4>
                <div className="space-y-1 text-sm text-yellow-800">
                  {competitionEvent.prize_details.first_place && (
                    <p>🥇 First Place: {competitionEvent.prize_details.first_place}</p>
                  )}
                  {competitionEvent.prize_details.second_place && (
                    <p>🥈 Second Place: {competitionEvent.prize_details.second_place}</p>
                  )}
                  {competitionEvent.prize_details.third_place && (
                    <p>🥉 Third Place: {competitionEvent.prize_details.third_place}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={handleViewMyEvents}
                className="flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
              >
                <FiEye className="h-4 w-4 mr-2" />
                View My Events
              </button>
              
              <button
                onClick={handleViewCompetitionDetails}
                className="flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 bg-white rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                <FiArrowRight className="h-4 w-4 mr-2" />
                Competition Details
              </button>
              
              <button
                onClick={handleGoHome}
                className="flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 bg-white rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                <FiHome className="h-4 w-4 mr-2" />
                Dashboard
              </button>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 text-sm">
            Good luck! Check your events page for updates on competition results.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CompetitionExamSubmissionSuccess;
