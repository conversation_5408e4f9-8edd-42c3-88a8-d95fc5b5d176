import React from 'react';
import { 
  FiTrendingUp, 
  FiTrendingDown, 
  FiTarget, 
  FiAward, 
  FiUsers, 
  FiBookOpen,
  FiActivity,
  FiClock
} from 'react-icons/fi';

/**
 * Analytics Quick View Component
 * Provides a condensed overview of key analytics metrics for the dashboard
 */
const AnalyticsQuickView = ({ 
  analyticsSummary, 
  recommendations, 
  upcomingCompetitions, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Mock data if no analytics summary is provided
  const defaultSummary = {
    overall_performance: 85.2,
    performance_trend: 2.3,
    total_study_hours: 42,
    completed_assignments: 18,
    total_assignments: 20,
    upcoming_exams: 3,
    class_rank: 5,
    total_students: 28
  };

  const summary = analyticsSummary || defaultSummary;

  const getTrendIcon = (trend) => {
    if (trend > 0) return <FiTrendingUp className="w-4 h-4 text-green-500" />;
    if (trend < 0) return <FiTrendingDown className="w-4 h-4 text-red-500" />;
    return <FiActivity className="w-4 h-4 text-gray-500" />;
  };

  const getTrendColor = (trend) => {
    if (trend > 0) return 'text-green-600 dark:text-green-400';
    if (trend < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
            Performance Overview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Your academic progress at a glance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {getTrendIcon(summary.performance_trend)}
          <span className={`text-sm font-medium ${getTrendColor(summary.performance_trend)}`}>
            {summary.performance_trend > 0 ? '+' : ''}{summary.performance_trend}%
          </span>
        </div>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Overall Performance */}
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">
                Overall Score
              </p>
              <p className="text-xl font-bold text-blue-800 dark:text-blue-200 mt-1">
                {summary.overall_performance}%
              </p>
            </div>
            <FiTarget className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        {/* Study Hours */}
        <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">
                Study Hours
              </p>
              <p className="text-xl font-bold text-green-800 dark:text-green-200 mt-1">
                {summary.total_study_hours}h
              </p>
            </div>
            <FiClock className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
        </div>

        {/* Assignments */}
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">
                Assignments
              </p>
              <p className="text-xl font-bold text-purple-800 dark:text-purple-200 mt-1">
                {summary.completed_assignments}/{summary.total_assignments}
              </p>
            </div>
            <FiBookOpen className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>

        {/* Class Rank */}
        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-yellow-600 dark:text-yellow-400 uppercase tracking-wide">
                Class Rank
              </p>
              <p className="text-xl font-bold text-yellow-800 dark:text-yellow-200 mt-1">
                #{summary.class_rank}
              </p>
            </div>
            <FiUsers className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recommendations */}
        {recommendations && recommendations.length > 0 && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3 flex items-center">
              <FiTarget className="w-4 h-4 mr-2" />
              Quick Recommendations
            </h4>
            <div className="space-y-2">
              {recommendations.slice(0, 3).map((rec, index) => (
                <div key={index} className="text-sm text-blue-700 dark:text-blue-300">
                  • {rec}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upcoming Competitions */}
        {upcomingCompetitions && upcomingCompetitions.length > 0 && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
              <FiAward className="w-4 h-4 mr-2" />
              Upcoming Competitions
            </h4>
            <div className="space-y-2">
              {upcomingCompetitions.slice(0, 3).map((comp, index) => (
                <div key={index} className="text-sm text-yellow-700 dark:text-yellow-300">
                  • {comp.name} - {comp.date}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyticsQuickView;
