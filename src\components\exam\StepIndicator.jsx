import React from 'react';
import { <PERSON><PERSON>ileText, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, <PERSON><PERSON><PERSON><PERSON>, FiEye } from 'react-icons/fi';

const StepIndicator = ({ steps, currentStep, examDetailsValid, questionsCount }) => {
  // Map step titles to icons
  const getStepIcon = (title) => {
    switch (title) {
      case 'Exam Details':
        return FiFileText;
      case 'Questions':
        return FiPlus;
      case 'Assignment':
        return FiUsers;
      case 'Review':
        return FiEye;
      default:
        return FiFileText;
    }
  };

  // Map step completion status
  const getStepStatus = (step, index) => {
    const stepNumber = index + 1;
    switch (step.title) {
      case 'Exam Details':
        return {
          isComplete: examDetailsValid,
          isActive: currentStep === stepNumber
        };
      case 'Questions':
        return {
          isComplete: questionsCount > 0,
          isActive: currentStep === stepNumber
        };
      case 'Assignment':
      case 'Review':
        return {
          isComplete: false, // These steps don't have completion validation yet
          isActive: currentStep === stepN<PERSON>ber
        };
      default:
        return {
          isComplete: false,
          isActive: currentStep === stepNumber
        };
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between relative">
        {steps.map((step, index) => {
          const IconComponent = getStepIcon(step.title);
          const status = getStepStatus(step, index);

          return (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center relative z-10">
                {/* Enhanced Step Circle */}
                <div className={`
                  w-12 h-12 rounded-xl flex items-center justify-center border-2 transition-all duration-300 shadow-lg transform hover:scale-105
                  ${status.isComplete
                    ? "bg-gradient-to-br from-green-500 to-green-600 border-green-400 text-white shadow-green-200 dark:shadow-green-900"
                    : status.isActive
                    ? "bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400 text-white shadow-blue-200 dark:shadow-blue-900 ring-2 ring-blue-100 dark:ring-blue-900"
                    : "bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 shadow-gray-100 dark:shadow-gray-800"
                  }
                `}>
                  {status.isComplete ? (
                    <FiCheck className="w-5 h-5 animate-pulse" />
                  ) : (
                    <IconComponent className="w-5 h-5" />
                  )}
                </div>

                {/* Step Number Badge */}
                <div className={`
                  absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold transition-all duration-300
                  ${status.isComplete
                    ? "bg-green-600 text-white"
                    : status.isActive
                    ? "bg-blue-600 text-white"
                    : "bg-gray-400 text-white"
                  }
                `}>
                  {index + 1}
                </div>

                {/* Step Content */}
                <div className="mt-3 text-center max-w-24">
                  <h3 className={`text-xs font-semibold transition-all duration-200 ${
                    status.isActive || status.isComplete
                      ? "text-gray-900 dark:text-gray-100"
                      : "text-gray-500 dark:text-gray-400"
                  }`}>
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Status Indicator */}
                  <div className="mt-1">
                    {status.isComplete && (
                      <div className="flex items-center justify-center gap-1 text-xs text-green-600 dark:text-green-400">
                        <FiCheck className="w-2 h-2" />
                        <span>Complete</span>
                      </div>
                    )}
                    {status.isActive && !status.isComplete && (
                      <div className="flex items-center justify-center gap-1 text-xs text-blue-600 dark:text-blue-400">
                        <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse"></div>
                        <span>In Progress</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Connection Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 relative mx-4">
                  <div className={`h-0.5 rounded-full transition-all duration-500 ${
                    status.isComplete
                      ? "bg-gradient-to-r from-green-500 to-green-400"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}>
                    {status.isComplete && (
                      <div className="h-full bg-gradient-to-r from-green-400 to-green-500 rounded-full animate-pulse"></div>
                    )}
                  </div>

                  {/* Animated Progress Dots */}
                  {status.isComplete && (
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="flex gap-1">
                        <div className="w-0.5 h-0.5 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-0.5 h-0.5 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-0.5 h-0.5 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;
