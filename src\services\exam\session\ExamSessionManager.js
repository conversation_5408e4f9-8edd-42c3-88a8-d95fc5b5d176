/**
 * Exam Session Manager
 * Handles exam session lifecycle management including start, pause, resume, and submission
 */

import EventEmitter from '../../../utils/EventEmitter';
import URL, { BASE_URL } from '../../../utils/api/API_URL';
import examSessionDataService from './ExamSessionDataService';

class ExamSessionManager extends EventEmitter {
  constructor() {
    super();
    this.activeSessions = new Map();
    this.sessionTimers = new Map();
    this.baseUrl = BASE_URL || URL;
  }



  /**
   * Complete exam session setup: Get session ID → WebSocket → Exam data
   */
  async startSessionAndGetExamData(examId, token, options = {}) {
    try {
      // Step 1: Get session ID from backend using the correct WebSocket API endpoint
      const sessionResponse = await fetch(`${this.baseUrl}/exam-session/request/${examId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!sessionResponse.ok) {
        const error = await sessionResponse.json();
        throw new Error(error.detail || error.message || 'Failed to get session ID');
      }

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.session_id;

      if (!sessionId) {
        throw new Error('No session ID received from backend');
      }

      // Store session locally
      this.activeSessions.set(sessionId, {
        session_id: sessionId,
        exam_id: examId,
        status: 'active',
        startTime: Date.now(),
        lastActivity: Date.now(),
        ...options
      });

      // Step 2: Establish WebSocket connection
      const webSocketConnected = await this.establishWebSocketConnection(sessionId, token);

      if (!webSocketConnected) {
        throw new Error('Failed to establish WebSocket connection');
      }

      // Step 3: Get exam details using session ID
      const examData = await examSessionDataService.getSessionWithExamData(sessionId);

      // Combine all data
      const combinedData = {
        session_id: sessionId,
        exam_id: examId,
        examData: examData.exam_data || examData,
        sessionStatus: examData.session_status,
        currentAnswers: examData.answers || {}
      };

      this.emit('sessionWithExamDataReady', combinedData);
      return combinedData;

    } catch (error) {
      this.emit('sessionError', { type: 'startWithData', error: error.message });
      throw error;
    }
  }

  /**
   * Establish WebSocket connection and wait for successful handshake
   */
  async establishWebSocketConnection(sessionId, token) {
    // Import WebSocket service dynamically to avoid circular dependencies
    const { default: ExamWebSocketService } = await import('../websocket/ExamWebSocketService.js');

    return new Promise((resolve, reject) => {
      // Set up connection event listeners
      const onConnected = () => {
        console.log('🎉 WebSocket connected successfully');
        ExamWebSocketService.off('connected', onConnected);
        ExamWebSocketService.off('error', onError);
        clearTimeout(connectionTimeout);
        resolve(true);
      };

      const onError = (error) => {
        console.error('❌ WebSocket connection failed:', error);
        ExamWebSocketService.off('connected', onConnected);
        ExamWebSocketService.off('error', onError);
        clearTimeout(connectionTimeout);
        reject(new Error(`WebSocket connection failed: ${error.message || error}`));
      };

      // Set timeout for connection attempt
      const connectionTimeout = setTimeout(() => {
        ExamWebSocketService.off('connected', onConnected);
        ExamWebSocketService.off('error', onError);
        reject(new Error('WebSocket connection timeout (10s)'));
      }, 10000); // 10 second timeout

      // Listen for connection events
      ExamWebSocketService.on('connected', onConnected);
      ExamWebSocketService.on('error', onError);

      // Attempt to connect
      try {
        ExamWebSocketService.connect(sessionId, token);
      } catch (error) {
        clearTimeout(connectionTimeout);
        reject(error);
      }
    });
  }

  /**
   * Pause an exam session
   */
  async pauseSession(sessionId, reason, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to pause exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'paused';
        session.pauseReason = reason;
        session.pausedAt = Date.now();
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionPaused', { sessionId, reason });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'pause', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Resume a paused exam session
   */
  async resumeSession(sessionId, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to resume exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'active';
        session.resumedAt = Date.now();
        delete session.pauseReason;
        delete session.pausedAt;
        
        // Restart timer with remaining time
        this.startSessionTimer(sessionId, result.remaining_time);
      }

      this.emit('sessionResumed', { sessionId });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'resume', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Submit exam session according to API specification
   */
  async submitSession(sessionId, answers, token, exam = null, questions = null, isAutoSubmit = false) {
    try {


      // Prepare submission payload according to API specification
      const submissionPayload = { session_id: sessionId };

      // Add complete exam data if provided (new API format)
      if (exam && questions) {

        submissionPayload.exam = exam;
        submissionPayload.questions = questions;
        submissionPayload.student_answers = answers || []; // Allow empty answers for auto-submit
      }

      // Choose the correct endpoint based on submission type (new WebSocket API)
      const endpoint = isAutoSubmit
        ? `${this.baseUrl}/exam-session/auto-submit`
        : `${this.baseUrl}/exam-session/submit`;



      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(submissionPayload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'submitted';
        session.submittedAt = Date.now();
        session.answers = answers;
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionSubmitted', { sessionId, result });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'submit', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Terminate exam session (admin action)
   */
  async terminateSession(sessionId, reason, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/terminate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to terminate exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'terminated';
        session.terminationReason = reason;
        session.terminatedAt = Date.now();
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionTerminated', { sessionId, reason });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'terminate', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId) {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Get all active sessions
   */
  getAllActiveSessions() {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Update session activity
   */
  updateSessionActivity(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastActivity = Date.now();
    }
  }

  /**
   * Start session timer
   */
  startSessionTimer(sessionId, durationSeconds) {
    // Clear existing timer
    this.stopSessionTimer(sessionId);
    
    const timer = setTimeout(() => {

      this.handleSessionTimeout(sessionId);
    }, durationSeconds * 1000);
    
    this.sessionTimers.set(sessionId, timer);
    
    // Also set up periodic warnings
    this.setupTimeWarnings(sessionId, durationSeconds);
  }

  /**
   * Stop session timer
   */
  stopSessionTimer(sessionId) {
    const timer = this.sessionTimers.get(sessionId);
    if (timer) {
      clearTimeout(timer);
      this.sessionTimers.delete(sessionId);
    }
  }

  /**
   * Setup time warnings
   */
  setupTimeWarnings(sessionId, durationSeconds) {
    // Warning at 30 minutes remaining
    if (durationSeconds > 1800) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '30min', remaining: 1800 });
      }, (durationSeconds - 1800) * 1000);
    }
    
    // Warning at 10 minutes remaining
    if (durationSeconds > 600) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '10min', remaining: 600 });
      }, (durationSeconds - 600) * 1000);
    }
    
    // Warning at 5 minutes remaining
    if (durationSeconds > 300) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '5min', remaining: 300 });
      }, (durationSeconds - 300) * 1000);
    }
  }

  /**
   * Handle session timeout
   */
  handleSessionTimeout(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session && session.status === 'active') {
      session.status = 'expired';
      session.expiredAt = Date.now();
      
      this.emit('sessionExpired', { sessionId });

    }
  }

  /**
   * Cleanup session
   */
  cleanupSession(sessionId) {
    this.stopSessionTimer(sessionId);
    this.activeSessions.delete(sessionId);

  }

  /**
   * Cleanup all sessions
   */
  cleanup() {

    
    // Stop all timers
    for (const timer of this.sessionTimers.values()) {
      clearTimeout(timer);
    }
    this.sessionTimers.clear();
    
    // Clear sessions
    this.activeSessions.clear();
    
    // Remove all listeners
    this.removeAllListeners();
    

  }
}

// Create singleton instance
const examSessionManager = new ExamSessionManager();

export default examSessionManager;
