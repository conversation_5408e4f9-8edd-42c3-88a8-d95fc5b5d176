import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchClasses } from '../../store/slices/ClassesSlice';
import ModernExamCreator from '../../components/exam/ModernExamCreator';

const TeacherCreateExam = () => {
  const dispatch = useDispatch();
  const { examId } = useParams();
  const isEditing = !!examId;

  useEffect(() => {
    // Load necessary data
    dispatch(fetchAllOwnClasses());
    dispatch(fetchSubjects());
    dispatch(fetchClasses());
  }, [dispatch]);

  return (
    <ModernExamCreator
      examId={examId}
      isEditing={isEditing}
      userType="teacher"
    />
  );
};

export default TeacherCreateExam;
