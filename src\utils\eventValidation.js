/**
 * Event Validation and Data Cleaning Utilities
 * Handles proper formatting and validation for event creation/update
 */

/**
 * Validates if a string is a valid UUID
 * @param {string} uuid - The UUID string to validate
 * @returns {boolean} - True if valid UUID, false otherwise
 */
export const isValidUUID = (uuid) => {
  if (!uuid || typeof uuid !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validates if a string is a valid datetime
 * @param {string} datetime - The datetime string to validate
 * @returns {boolean} - True if valid datetime, false otherwise
 */
export const isValidDateTime = (datetime) => {
  if (!datetime || typeof datetime !== 'string') return false;
  const date = new Date(datetime);
  return !isNaN(date.getTime());
};

/**
 * Converts a datetime string to ISO format or returns undefined
 * @param {string} datetime - The datetime string to convert
 * @returns {string|undefined} - ISO string or undefined if invalid
 */
export const toISOStringOrUndefined = (datetime) => {
  if (!datetime || typeof datetime !== 'string' || datetime.trim() === '') {
    return undefined;
  }
  
  try {
    const date = new Date(datetime);
    if (isNaN(date.getTime())) {
      return undefined;
    }
    return date.toISOString();
  } catch (error) {
    return undefined;
  }
};

/**
 * Cleans ticket data for API submission
 * @param {Object} ticket - The ticket object to clean
 * @returns {Object} - Cleaned ticket object
 */
export const cleanTicketData = (ticket) => {
  const cleanTicket = { ...ticket };
  
  // Remove local ID if it exists
  if ('id' in cleanTicket && typeof cleanTicket.id === 'number') {
    delete cleanTicket.id;
  }

  // Handle datetime fields
  const saleStart = toISOStringOrUndefined(cleanTicket.sale_start);
  const saleEnd = toISOStringOrUndefined(cleanTicket.sale_end);
  
  if (saleStart) {
    cleanTicket.sale_start = saleStart;
  } else {
    delete cleanTicket.sale_start;
  }
  
  if (saleEnd) {
    cleanTicket.sale_end = saleEnd;
  } else {
    delete cleanTicket.sale_end;
  }

  // Remove empty string fields and null values
  Object.keys(cleanTicket).forEach(key => {
    const value = cleanTicket[key];
    if (value === '' || value === null || value === undefined) {
      delete cleanTicket[key];
    }
  });

  // Ensure numeric fields are properly typed
  if (cleanTicket.price !== undefined) {
    cleanTicket.price = parseFloat(cleanTicket.price) || 0;
  }
  if (cleanTicket.total_quantity !== undefined) {
    cleanTicket.total_quantity = parseInt(cleanTicket.total_quantity) || 1;
  }
  if (cleanTicket.min_quantity_per_order !== undefined) {
    cleanTicket.min_quantity_per_order = parseInt(cleanTicket.min_quantity_per_order) || 1;
  }
  if (cleanTicket.max_quantity_per_order !== undefined) {
    cleanTicket.max_quantity_per_order = parseInt(cleanTicket.max_quantity_per_order) || 1;
  }

  return cleanTicket;
};

/**
 * Cleans event data for API submission
 * @param {Object} eventData - The event data to clean
 * @returns {Object} - Cleaned event data
 */
export const cleanEventData = (eventData) => {
  const cleanData = { ...eventData };

  // Handle datetime fields
  const datetimeFields = [
    'start_datetime',
    'end_datetime', 
    'registration_start',
    'registration_end'
  ];

  datetimeFields.forEach(field => {
    const value = toISOStringOrUndefined(cleanData[field]);
    if (value) {
      cleanData[field] = value;
    } else {
      delete cleanData[field];
    }
  });

  // Handle numeric fields
  if (cleanData.max_attendees !== undefined) {
    const maxAttendees = parseInt(cleanData.max_attendees);
    if (!isNaN(maxAttendees) && maxAttendees > 0) {
      cleanData.max_attendees = maxAttendees;
    } else {
      delete cleanData.max_attendees;
    }
  }

  if (cleanData.min_attendees !== undefined) {
    const minAttendees = parseInt(cleanData.min_attendees);
    if (!isNaN(minAttendees) && minAttendees >= 0) {
      cleanData.min_attendees = minAttendees;
    } else {
      cleanData.min_attendees = 1; // Default value
    }
  }

  // Handle competition fields
  if (!cleanData.is_competition) {
    // Remove competition-related fields if not a competition
    delete cleanData.competition_rules;
    delete cleanData.prize_details;
    delete cleanData.competition_exam_id;
  } else {
    // Validate competition_exam_id if provided
    if (cleanData.competition_exam_id) {
      if (!isValidUUID(cleanData.competition_exam_id)) {
        delete cleanData.competition_exam_id; // Remove invalid UUID
      }
    } else {
      delete cleanData.competition_exam_id; // Remove empty field
    }
  }

  // Handle array fields
  const arrayFields = ['agenda', 'tags', 'gallery_images', 'speaker_ids', 'mentor_ids'];
  arrayFields.forEach(field => {
    if (!Array.isArray(cleanData[field])) {
      cleanData[field] = [];
    }
  });

  // Validate and clean speaker_ids and mentor_ids arrays
  if (Array.isArray(cleanData.speaker_ids)) {
    cleanData.speaker_ids = cleanData.speaker_ids.filter(id => isValidUUID(id));
  }

  if (Array.isArray(cleanData.mentor_ids)) {
    cleanData.mentor_ids = cleanData.mentor_ids.filter(id => isValidUUID(id));
  }

  // Remove speaker_ids for competition events and mentor_ids for non-competition events
  if (cleanData.category === 'COMPETITION' || cleanData.is_competition) {
    delete cleanData.speaker_ids;
  } else {
    delete cleanData.mentor_ids;
  }

  // Handle object fields
  const objectFields = ['external_links', 'prize_details'];
  objectFields.forEach(field => {
    if (typeof cleanData[field] !== 'object' || cleanData[field] === null) {
      cleanData[field] = {};
    }
  });

  // Remove empty string fields
  Object.keys(cleanData).forEach(key => {
    const value = cleanData[key];
    if (value === '' || value === null) {
      delete cleanData[key];
    }
  });

  // Clean tickets array if present
  if (Array.isArray(cleanData.tickets)) {
    cleanData.tickets = cleanData.tickets.map(cleanTicketData);
  }

  return cleanData;
};

/**
 * Validates event form data
 * @param {Object} formData - The form data to validate
 * @param {boolean} isPublishing - Whether this is for publishing (stricter validation)
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateEventForm = (formData, isPublishing = false) => {
  const errors = [];

  // Required fields
  if (!formData.title || formData.title.trim() === '') {
    errors.push('Event title is required');
  }

  if (!formData.description || formData.description.trim() === '') {
    errors.push('Event description is required');
  }

  if (!formData.start_datetime) {
    errors.push('Event start date and time is required');
  }

  if (!formData.end_datetime) {
    errors.push('Event end date and time is required');
  }

  // Additional required fields for publishing
  if (isPublishing) {
    if (!formData.location || formData.location.trim() === '') {
      errors.push('Event location is required');
    }

    if (!formData.max_attendees || formData.max_attendees === '') {
      errors.push('Maximum attendees is required');
    }

    if (!formData.registration_start) {
      errors.push('Registration start date is required');
    }

    if (!formData.registration_end) {
      errors.push('Registration end date is required');
    }

    if (!formData.short_description || formData.short_description.trim() === '') {
      errors.push('Short description is required for publishing');
    }

    // Competition-specific validations for publishing
    if (formData.category === 'COMPETITION' || formData.is_competition) {
      if (!formData.exam_id || formData.exam_id.trim() === '') {
        errors.push('Competition exam selection is required');
      }
    }
  }

  // Date validations
  if (formData.start_datetime && formData.end_datetime) {
    const startDate = new Date(formData.start_datetime);
    const endDate = new Date(formData.end_datetime);
    
    if (startDate >= endDate) {
      errors.push('Event end time must be after start time');
    }
  }

  if (formData.registration_start && formData.registration_end) {
    const regStart = new Date(formData.registration_start);
    const regEnd = new Date(formData.registration_end);
    
    if (regStart >= regEnd) {
      errors.push('Registration end time must be after registration start time');
    }
  }

  if (formData.registration_end && formData.start_datetime) {
    const regEnd = new Date(formData.registration_end);
    const eventStart = new Date(formData.start_datetime);
    
    if (regEnd > eventStart) {
      errors.push('Registration must end before the event starts');
    }
  }

  // Competition validations
  if (formData.is_competition) {
    if (formData.competition_exam_id && !isValidUUID(formData.competition_exam_id)) {
      errors.push('Competition exam ID must be a valid UUID');
    }
  }

  // Numeric validations
  if (formData.max_attendees) {
    const maxAttendees = parseInt(formData.max_attendees);
    if (isNaN(maxAttendees) || maxAttendees <= 0) {
      errors.push('Maximum attendees must be a positive number');
    }
  }

  if (formData.min_attendees) {
    const minAttendees = parseInt(formData.min_attendees);
    if (isNaN(minAttendees) || minAttendees < 0) {
      errors.push('Minimum attendees must be a non-negative number');
    }
  }

  if (formData.max_attendees && formData.min_attendees) {
    const maxAttendees = parseInt(formData.max_attendees);
    const minAttendees = parseInt(formData.min_attendees);
    
    if (minAttendees > maxAttendees) {
      errors.push('Minimum attendees cannot be greater than maximum attendees');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validates ticket data
 * @param {Object} ticket - The ticket data to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateTicketData = (ticket) => {
  const errors = [];

  if (!ticket.name || ticket.name.trim() === '') {
    errors.push('Ticket name is required');
  }

  if (ticket.price !== undefined) {
    const price = parseFloat(ticket.price);
    if (isNaN(price) || price < 0) {
      errors.push('Ticket price must be a non-negative number');
    }
  }

  if (ticket.total_quantity !== undefined) {
    const quantity = parseInt(ticket.total_quantity);
    if (isNaN(quantity) || quantity <= 0) {
      errors.push('Ticket quantity must be a positive number');
    }
  }

  if (ticket.sale_start && ticket.sale_end) {
    const saleStart = new Date(ticket.sale_start);
    const saleEnd = new Date(ticket.sale_end);
    
    if (saleStart >= saleEnd) {
      errors.push('Ticket sale end time must be after sale start time');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Checks if the form is ready for publishing (all required fields filled)
 * @param {Object} formData - The form data to check
 * @returns {boolean} - True if ready for publishing
 */
export const isFormReadyForPublishing = (formData) => {
  const validation = validateEventForm(formData, true);
  return validation.isValid;
};

/**
 * Gets missing required fields for publishing
 * @param {Object} formData - The form data to check
 * @returns {Array} - Array of missing field names
 */
export const getMissingRequiredFields = (formData) => {
  const missingFields = [];

  if (!formData.title || formData.title.trim() === '') {
    missingFields.push('title');
  }

  if (!formData.description || formData.description.trim() === '') {
    missingFields.push('description');
  }

  if (!formData.short_description || formData.short_description.trim() === '') {
    missingFields.push('short_description');
  }

  if (!formData.start_datetime) {
    missingFields.push('start_datetime');
  }

  if (!formData.end_datetime) {
    missingFields.push('end_datetime');
  }

  if (!formData.location || formData.location.trim() === '') {
    missingFields.push('location');
  }

  if (!formData.max_attendees || formData.max_attendees === '') {
    missingFields.push('max_attendees');
  }

  if (!formData.registration_start) {
    missingFields.push('registration_start');
  }

  if (!formData.registration_end) {
    missingFields.push('registration_end');
  }

  // Competition-specific fields
  if ((formData.category === 'COMPETITION' || formData.is_competition) &&
      (!formData.exam_id || formData.exam_id.trim() === '')) {
    missingFields.push('exam_id');
  }

  return missingFields;
};

export default {
  isValidUUID,
  isValidDateTime,
  toISOStringOrUndefined,
  cleanTicketData,
  cleanEventData,
  validateEventForm,
  validateTicketData,
  isFormReadyForPublishing,
  getMissingRequiredFields
};
