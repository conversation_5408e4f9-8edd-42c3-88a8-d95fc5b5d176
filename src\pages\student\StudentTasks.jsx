import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTasksByStudent,
  selectTasks,
  selectTasksLoading,
  selectTasksError,
  clearTaskState
} from '../../store/slices/TaskSlice';
import {
  FiSearch,
  FiCalendar,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiEye,
  FiLoader,
  FiBook,
  FiSend,
  FiEdit3,
  FiStar,
  FiTrendingUp
} from 'react-icons/fi';

/**
 * StudentTasks Page
 * Dashboard for students to view assigned tasks, submit work, and track completion
 */
const StudentTasks = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const tasksFromStore = useSelector(selectTasks);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);

  // Ensure tasks is always an array
  const tasks = Array.isArray(tasksFromStore) ? tasksFromStore : [];

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [sortBy, setSortBy] = useState('deadline');

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load student tasks on mount
  useEffect(() => {
    dispatch(fetchTasksByStudent());

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch]);

  // Filter and sort tasks
  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(task => {
        const taskSubject = typeof task.subject === 'string'
          ? task.subject
          : (task.subject && task.subject.name) || '';

        return task.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
               task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
               taskSubject.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.submission_status === statusFilter);
    }

    // Apply subject filter
    if (subjectFilter !== 'all') {
      filtered = filtered.filter(task => {
        const taskSubject = typeof task.subject === 'string'
          ? task.subject
          : (task.subject && task.subject.name) || '';
        return taskSubject === subjectFilter;
      });
    }

    // Sort tasks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'deadline':
          if (!a.deadline && !b.deadline) return 0;
          if (!a.deadline) return 1;
          if (!b.deadline) return -1;
          return new Date(a.deadline) - new Date(b.deadline);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'subject':
          const aSubject = typeof a.subject === 'string'
            ? a.subject
            : (a.subject && a.subject.name) || '';
          const bSubject = typeof b.subject === 'string'
            ? b.subject
            : (b.subject && b.subject.name) || '';
          return aSubject.localeCompare(bSubject);
        case 'created_at':
          return new Date(b.created_at) - new Date(a.created_at);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tasks, searchTerm, statusFilter, subjectFilter, sortBy]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.submission_status === 'submitted' || t.submission_status === 'graded').length;
    const pendingTasks = tasks.filter(t => t.submission_status === 'not_submitted' || t.submission_status === 'in_progress').length;
    const overdueTasks = tasks.filter(t => 
      t.deadline && 
      new Date(t.deadline) < new Date() && 
      (t.submission_status === 'not_submitted' || t.submission_status === 'in_progress')
    ).length;

    const averageGrade = tasks
      .filter(t => t.grade !== null && t.grade !== undefined)
      .reduce((sum, t, _, arr) => sum + t.grade / arr.length, 0);

    return {
      totalTasks,
      completedTasks,
      pendingTasks,
      overdueTasks,
      averageGrade: averageGrade || 0
    };
  }, [tasks]);

  // Get unique subjects for filter
  const subjects = useMemo(() => {
    const uniqueSubjects = [...new Set(tasks.map(t => {
      // Handle both string and object subjects
      if (typeof t.subject === 'string') {
        return t.subject;
      } else if (t.subject && typeof t.subject === 'object' && t.subject.name) {
        return t.subject.name;
      }
      return null;
    }).filter(Boolean))];
    return uniqueSubjects.sort();
  }, [tasks]);

  // Handle task actions
  const handleViewTask = (taskId) => {
    navigate(`/student/task/${taskId}`);
  };



  // Check if task is overdue
  const isOverdue = (task) => {
    return task.deadline && 
           new Date(task.deadline) < new Date() && 
           (task.submission_status === 'not_submitted' || task.submission_status === 'in_progress');
  };

  // Status badge component
  const StatusBadge = ({ status, isOverdue: overdue }) => {
    const statusConfig = {
      not_submitted: {
        color: overdue
          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800'
          : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border border-orange-200 dark:border-orange-800',
        icon: overdue ? FiAlertCircle : FiClock,
        text: overdue ? 'Overdue' : 'Missing'
      },
      in_progress: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800',
        icon: FiEdit3,
        text: 'Draft'
      },
      submitted: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800',
        icon: FiCheckCircle,
        text: 'Turned in'
      },
      graded: {
        color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800',
        icon: FiStar,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    );
  };

  // Stats card component
  const StatsCard = ({ title, value, icon: Icon, color, subtitle }) => (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4 hover:shadow-sm transition-shadow`}>
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${color.replace('text-', 'bg-').replace('-600', '-100')} dark:${color.replace('text-', 'bg-').replace('-600', '-900/20')}`}>
          <Icon className={`w-5 h-5 ${color}`} />
        </div>
        <div>
          <p className={`text-lg font-semibold ${textPrimary}`}>{value}</p>
          <p className={`text-sm ${textSecondary}`}>{title}</p>
          {subtitle && (
            <p className={`text-xs ${textSecondary} mt-1`}>{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-none px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="mb-6">
          <h1 className={`text-3xl font-bold ${textPrimary} mb-2`}>To do</h1>
          <p className={`text-base ${textSecondary}`}>
            Keep track of your assignments and due dates
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <StatsCard
          title="Missing"
          value={stats.pendingTasks}
          icon={FiClock}
          color="text-orange-600"
        />
        <StatsCard
          title="Turned in"
          value={stats.completedTasks}
          icon={FiCheckCircle}
          color="text-green-600"
        />
        <StatsCard
          title="Overdue"
          value={stats.overdueTasks}
          icon={FiAlertCircle}
          color="text-red-600"
        />
        <StatsCard
          title="Graded"
          value={stats.averageGrade > 0 ? `${stats.averageGrade.toFixed(1)}%` : '0'}
          icon={FiStar}
          color="text-purple-600"
        />
      </div>

      {/* Filters and Search */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${textSecondary}`} />
            <input
              type="text"
              placeholder="Search assignments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2.5 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${bgPrimary} ${textPrimary} placeholder-gray-400`}
            />
          </div>

          {/* Filters */}
          <div className="flex gap-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={`px-3 py-2.5 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} text-sm`}
            >
              <option value="all">All</option>
              <option value="not_submitted">Missing</option>
              <option value="in_progress">Draft</option>
              <option value="submitted">Turned in</option>
              <option value="graded">Graded</option>
            </select>

            {subjects.length > 0 && (
              <select
                value={subjectFilter}
                onChange={(e) => setSubjectFilter(e.target.value)}
                className={`px-3 py-2.5 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} text-sm`}
              >
                <option value="all">All classes</option>
                {subjects.map(subject => (
                  <option key={subject} value={subject}>{subject}</option>
                ))}
              </select>
            )}

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={`px-3 py-2.5 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} text-sm`}
            >
              <option value="deadline">Due date</option>
              <option value="name">Title</option>
              <option value="subject">Class</option>
              <option value="created_at">Assigned</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Tasks Grid */}
      {loading ? (
        <div className="text-center py-12">
          <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
          <p className={`text-sm ${textSecondary}`}>Loading your tasks...</p>
        </div>
      ) : filteredTasks.length === 0 ? (
        <div className="text-center py-12">
          <FiBook className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
          <p className={`text-sm ${textSecondary}`}>
            {searchTerm || statusFilter !== 'all' || subjectFilter !== 'all'
              ? 'No tasks match your filters'
              : 'No tasks assigned yet'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTasks.map((task) => (
            <div
              key={task.id}
              className={`${bgPrimary} rounded-lg border ${borderColor} hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer overflow-hidden group`}
              onClick={() => handleViewTask(task.id)}
            >
              {/* Color bar based on status */}
              <div className={`h-1 ${
                task.submission_status === 'graded' ? 'bg-purple-500' :
                task.submission_status === 'submitted' ? 'bg-green-500' :
                task.submission_status === 'in_progress' ? 'bg-blue-500' :
                isOverdue(task) ? 'bg-red-500' : 'bg-orange-500'
              }`} />

              <div className="p-5">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-semibold text-lg ${textPrimary} mb-1 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors`}>
                      {task.name}
                    </h3>
                    {task.subject && (
                      <p className={`text-sm ${textSecondary} font-medium`}>
                        {typeof task.subject === 'string'
                          ? task.subject
                          : (task.subject.name || '')}
                      </p>
                    )}
                  </div>
                  <StatusBadge status={task.submission_status} isOverdue={isOverdue(task)} />
                </div>

                {/* Description */}
                {task.description && (
                  <p className={`text-sm ${textSecondary} mb-4 line-clamp-2 leading-relaxed`}>
                    {task.description}
                  </p>
                )}

                {/* Deadline and Grade Info */}
                <div className="space-y-3 mb-4">
                  {task.deadline && (
                    <div className={`flex items-center gap-2 p-2 rounded-md ${
                      isOverdue(task)
                        ? 'bg-red-50 dark:bg-red-900/20'
                        : 'bg-gray-50 dark:bg-gray-800'
                    }`}>
                      <FiCalendar className={`w-4 h-4 ${isOverdue(task) ? 'text-red-500' : textSecondary}`} />
                      <span className={`text-sm ${isOverdue(task) ? 'text-red-600 dark:text-red-400 font-medium' : textSecondary}`}>
                        Due {new Date(task.deadline).toLocaleDateString()} at{' '}
                        {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  )}

                  {task.grade !== null && task.grade !== undefined && (
                    <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-md">
                      <FiStar className="w-4 h-4 text-green-600" />
                      <span className={`text-sm font-medium text-green-700 dark:text-green-400`}>
                        {task.grade}
                        {task.max_grade && `/${task.max_grade}`}
                        {task.max_grade && ` (${Math.round((task.grade / task.max_grade) * 100)}%)`}
                      </span>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
                  <span className={`text-xs ${textSecondary}`}>
                    {new Date(task.created_at).toLocaleDateString()}
                  </span>

                  <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                    <FiEye className="w-4 h-4" />
                    Open
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default StudentTasks;
