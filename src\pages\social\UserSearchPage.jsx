import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oa<PERSON>, Fi<PERSON><PERSON><PERSON>, FiFilter } from 'react-icons/fi';
import SocialUserListItem from '../../components/social/UserListItem';
import FollowSuggestions from '../../components/social/FollowSuggestions';
import { PageContainer, PageHeader } from '../../components/ui/layout';
import socialFollowService from '../../services/socialFollowService';
import { extractFollowableUsersFromResponse, extractErrorMessage } from '../../utils/helpers/apiResponseHelpers';

/**
 * UserSearchPage Component
 * Search for users across the platform
 */
const UserSearchPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [userTypeFilter, setUserTypeFilter] = useState('all');
  const [hasSearched, setHasSearched] = useState(false);

  // Search users using the real API
  const searchUsers = async (term, userType = 'all') => {
    try {
      setLoading(true);
      setError(null);

      // Use the real API to search users
      const results = await socialFollowService.searchUsers(term, userType, 50, 0);


      // Use helper function to extract followable users from response
      const searchData = extractFollowableUsersFromResponse(results);


      // Temporary: Let's also try direct extraction to see if the helper is the issue
      const directData = results.followable_users || [];


      setSearchResults(searchData);
      setHasSearched(true);
    } catch (err) {
      console.error('❌ Search failed:', err);
      setError(extractErrorMessage(err));
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      searchUsers(searchTerm.trim(), userTypeFilter);
    }
  };

  const handleSearchTermChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleUserTypeChange = (e) => {
    setUserTypeFilter(e.target.value);
    if (hasSearched && searchTerm.trim()) {
      searchUsers(searchTerm.trim(), e.target.value);
    }
  };

  const handleFollowChange = (isFollowing, userId, user) => {
    // Update search results optimistically
    setSearchResults(prev => prev.map(u => 
      u.id === userId ? { ...u, isFollowing } : u
    ));
  };

  return (
    <PageContainer>
      <PageHeader
        title="Find People"
        subtitle="Search for students, teachers, mentors, and institutes to connect with"
      />

      <div className="space-y-8">

        {/* Search Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-4">
              {/* Search Input */}
              <div className="flex-1 relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search by username or email..."
                  value={searchTerm}
                  onChange={handleSearchTermChange}
                  className="w-full pl-10 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* User Type Filter */}
              <div className="relative">
                <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <select
                  value={userTypeFilter}
                  onChange={handleUserTypeChange}
                  className="pl-10 pr-8 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                >
                  <option value="all">All Users</option>
                  <option value="student">Students</option>
                  <option value="teacher">Teachers</option>
                  <option value="mentor">Mentors</option>
                  <option value="institute">Institutes</option>
                </select>
              </div>

              {/* Search Button */}
              <button
                type="submit"
                disabled={loading || !searchTerm.trim()}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiSearch className="w-4 h-4" />
                )}
                Search
              </button>
            </div>
          </form>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Search Results */}
          <div className="lg:col-span-2">
            {/* Loading State */}
            {loading && (
              <div className="flex items-center justify-center py-12">
                <FiLoader className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-3 text-gray-600 dark:text-gray-400">Searching...</span>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6 text-center">
                <p className="text-red-600 dark:text-red-400">{error}</p>
                <button
                  onClick={() => searchUsers(searchTerm, userTypeFilter)}
                  className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}

            {/* No Results */}
            {!loading && !error && hasSearched && searchResults.length === 0 && (
              <div className="text-center py-12">
                <FiUsers className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No users found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search terms or filters
                </p>
              </div>
            )}

            {/* Search Results */}
            {!loading && !error && searchResults.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Search Results ({searchResults.length})
                </h2>
                {searchResults.map((user) => (
                  <SocialUserListItem
                    key={user.id}
                    user={user}
                    showFollowButton={true}
                    onFollowChange={handleFollowChange}
                  />
                ))}
              </div>
            )}
            {/* Initial State */}
            {!loading && !error && !hasSearched && (
              <div className="text-center py-12">
                <FiSearch className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Start searching
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Enter a username or email to find people to connect with
                </p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Follow Suggestions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <FollowSuggestions
                limit={5}
                layout="card"
                showHeader={true}
                showRefresh={true}
              />
            </div>

            {/* Search Tips */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                🔍 Search Tips
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Use partial usernames or email addresses</li>
                <li>• Filter by user type to narrow results</li>
                <li>• Follow people to see their updates</li>
                <li>• Check out suggested users below</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default UserSearchPage;
