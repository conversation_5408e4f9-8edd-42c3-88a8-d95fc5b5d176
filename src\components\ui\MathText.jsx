/**
 * MathText Component
 * Renders text with proper mathematical formatting
 */

import React from 'react';
import { convertLatexToHtml, formatComplexMath, containsMathExpressions } from '../../utils/mathRenderer';

const MathText = ({ 
  children, 
  className = '', 
  inline = true,
  enableLatex = true,
  ...props 
}) => {
  // Handle different input types
  const text = typeof children === 'string' ? children : String(children || '');
  
  if (!text) return null;

  // Process the text based on content type
  let processedText = text;
  
  if (enableLatex) {
    // First handle LaTeX expressions
    processedText = convertLatexToHtml(processedText);
  }
  
  // Then handle general math expressions
  if (containsMathExpressions(processedText)) {
    processedText = formatComplexMath(processedText);
  }

  // Base CSS classes for math rendering
  const baseClasses = `
    math-text
    ${inline ? 'inline' : 'block'}
    ${className}
  `;

  // If no mathematical content detected, render as plain text
  if (processedText === text && !containsMathExpressions(text)) {
    return (
      <span className={baseClasses} {...props}>
        {text}
      </span>
    );
  }

  // Render with mathematical formatting
  return (
    <span 
      className={baseClasses}
      dangerouslySetInnerHTML={{ __html: processedText }}
      {...props}
    />
  );
};

// Block version for display math
export const MathBlock = ({ children, className = '', ...props }) => {
  return (
    <MathText 
      inline={false} 
      className={`block ${className}`} 
      {...props}
    >
      {children}
    </MathText>
  );
};

// Inline version (default)
export const MathInline = ({ children, className = '', ...props }) => {
  return (
    <MathText 
      inline={true} 
      className={`inline ${className}`} 
      {...props}
    >
      {children}
    </MathText>
  );
};

export default MathText;
