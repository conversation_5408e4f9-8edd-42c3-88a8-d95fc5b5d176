import React, { useState } from 'react';
import { FiCheck, FiCheckCircle, FiTrash2, FiMoreVertical, FiUser } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';

/**
 * MessageBubble Component
 * Displays individual chat messages with sender info and actions
 */
const MessageBubble = ({
  message,
  isOwn = false,
  showAvatar = true,
  onDelete,
  className = ''
}) => {
  const { currentTheme } = useThemeProvider();
  const [showActions, setShowActions] = useState(false);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const getReadStatus = () => {
    if (!isOwn) return null;
    
    if (message.read_at) {
      return (
        <FiCheckCircle className="w-3 h-3 text-blue-500" title="Read" />
      );
    } else {
      return (
        <FiCheck className="w-3 h-3 text-gray-400" title="Sent" />
      );
    }
  };

  if (message.is_deleted) {
    return (
      <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-2 ${className}`}>
        <div className={`
          max-w-xs lg:max-w-md px-3 py-2 rounded-lg italic
          ${currentTheme === 'dark' 
            ? 'bg-gray-700 text-gray-400' 
            : 'bg-gray-100 text-gray-500'
          }
        `}>
          <div className="flex items-center space-x-2">
            <FiTrash2 className="w-3 h-3" />
            <span className="text-sm">This message was deleted</span>
          </div>
          <div className="text-xs mt-1 opacity-75">
            {formatTime(message.sent_at)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-3 group ${className}`}>
      <div className={`flex ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}>
        {/* Avatar */}
        {showAvatar && !isOwn && (
          <div className="flex-shrink-0 mb-1">
            {message.sender_profile_picture ? (
              <img
                src={message.sender_profile_picture}
                alt={message.sender_username}
                className="w-6 h-6 rounded-full object-cover"
              />
            ) : (
              <div className={`
                w-6 h-6 rounded-full flex items-center justify-center
                ${currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'}
              `}>
                <FiUser className="w-3 h-3 text-gray-500" />
              </div>
            )}
          </div>
        )}

        {/* Message content */}
        <div className="relative">
          {/* Sender name (for received messages) */}
          {!isOwn && (
            <div className={`text-xs mb-1 px-1 ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {message.sender_username}
            </div>
          )}

          {/* Message bubble */}
          <div
            className={`
              relative px-3 py-2 rounded-lg break-words
              ${isOwn
                ? 'bg-blue-500 text-white rounded-br-sm'
                : currentTheme === 'dark'
                  ? 'bg-gray-700 text-gray-100 rounded-bl-sm'
                  : 'bg-gray-200 text-gray-900 rounded-bl-sm'
              }
            `}
            onMouseEnter={() => setShowActions(true)}
            onMouseLeave={() => setShowActions(false)}
          >
            {/* Message text */}
            <div className="text-sm whitespace-pre-wrap">
              {message.message}
            </div>

            {/* Message info */}
            <div className={`flex items-center justify-between mt-1 space-x-2`}>
              <span className={`text-xs ${
                isOwn 
                  ? 'text-blue-100' 
                  : currentTheme === 'dark' 
                    ? 'text-gray-400' 
                    : 'text-gray-500'
              }`}>
                {formatTime(message.sent_at)}
              </span>

              {/* Read status for own messages */}
              <div className="flex items-center space-x-1">
                {getReadStatus()}
              </div>
            </div>

            {/* Actions menu */}
            {showActions && isOwn && onDelete && (
              <div className="absolute -top-8 right-0 z-10">
                <div className={`
                  flex items-center space-x-1 px-2 py-1 rounded-md shadow-lg border
                  ${currentTheme === 'dark' 
                    ? 'bg-gray-800 border-gray-600' 
                    : 'bg-white border-gray-200'
                  }
                `}>
                  <button
                    onClick={() => onDelete(message.id)}
                    className={`
                      p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/30 
                      text-red-500 hover:text-red-600 transition-colors
                    `}
                    title="Delete message"
                  >
                    <FiTrash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * DateSeparator Component
 * Shows date separators between messages from different days
 */
export const DateSeparator = ({ date }) => {
  const { currentTheme } = useThemeProvider();
  
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString([], { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  return (
    <div className="flex items-center justify-center my-4">
      <div className={`
        px-3 py-1 rounded-full text-xs font-medium
        ${currentTheme === 'dark' 
          ? 'bg-gray-700 text-gray-300' 
          : 'bg-gray-100 text-gray-600'
        }
      `}>
        {formatDate(date)}
      </div>
    </div>
  );
};

/**
 * TypingIndicator Component
 * Shows when someone is typing
 */
export const TypingIndicator = ({ username }) => {
  const { currentTheme } = useThemeProvider();

  return (
    <div className="flex justify-start mb-3">
      <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
        <div className={`
          px-3 py-2 rounded-lg rounded-bl-sm
          ${currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'}
        `}>
          <div className="flex items-center space-x-1">
            <span className={`text-xs ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {username} is typing
            </span>
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
