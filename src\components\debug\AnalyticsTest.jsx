import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>p,
  FiBook<PERSON><PERSON>,
  Fi<PERSON>sers,
  <PERSON>Award,
  FiTarget,
  FiClock,
  FiCheckCircle,
  FiStar
} from 'react-icons/fi';

/**
 * Analytics Test Component
 * Tests the improved analytics sections with sample data
 */
const AnalyticsTest = () => {
  // Sample data for testing
  const sampleData = {
    overallScore: 87.5,
    strongestSubject: 'Mathematics',
    avgClassroomRank: 3,
    totalCompetitions: 12,
    subjectMastery: 85,
    classEngagement: 92,
    competitionPerformance: 78,
    assignmentCompletion: 95,
    onTimeRate: 88
  };

  const improvementOpportunities = [
    "Focus more on Physics concepts to improve understanding",
    "Participate more actively in Chemistry lab sessions",
    "Complete assignments earlier to avoid last-minute rush",
    "Join study groups for better collaborative learning",
    "Practice more competition-style problems",
    "Improve time management during exams"
  ];

  return (
    <div className="p-8 space-y-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          Enhanced Analytics Test
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Testing improved analytics sections without the ugly radar chart
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-600 dark:text-blue-400 mb-1">Overall Performance</p>
              <p className="text-3xl font-bold text-blue-800 dark:text-blue-200">{sampleData.overallScore}%</p>
              <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2 mt-3">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${sampleData.overallScore}%` }}
                ></div>
              </div>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <FiTarget className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-600 dark:text-green-400 mb-1">Strongest Area</p>
              <p className="text-xl font-bold text-green-800 dark:text-green-200">{sampleData.strongestSubject}</p>
              <div className="flex items-center mt-2">
                <FiTrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" />
                <span className="text-xs text-green-600 dark:text-green-400 font-medium">Top Subject</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <FiStar className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-1">Avg Class Rank</p>
              <p className="text-3xl font-bold text-purple-800 dark:text-purple-200">#{sampleData.avgClassroomRank}</p>
              <div className="flex items-center mt-2">
                <FiUsers className="w-4 h-4 text-purple-600 dark:text-purple-400 mr-1" />
                <span className="text-xs text-purple-600 dark:text-purple-400 font-medium">Class Position</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
              <FiUsers className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-600 dark:text-yellow-400 mb-1">Competitions</p>
              <p className="text-3xl font-bold text-yellow-800 dark:text-yellow-200">{sampleData.totalCompetitions}</p>
              <div className="flex items-center mt-2">
                <FiAward className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-1" />
                <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">Participated</span>
              </div>
            </div>
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
              <FiAward className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Overall Grade Performance */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
        <h3 className="text-xl font-bold text-blue-800 dark:text-blue-200 mb-6 flex items-center">
          <FiTrendingUp className="w-6 h-6 mr-3 text-blue-600 dark:text-blue-400" />
          Overall Grade Performance
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {[
            { icon: FiBookOpen, label: 'Subject Mastery', value: sampleData.subjectMastery, color: 'blue' },
            { icon: FiUsers, label: 'Class Engagement', value: sampleData.classEngagement, color: 'green' },
            { icon: FiAward, label: 'Competitions', value: sampleData.competitionPerformance, color: 'yellow' },
            { icon: FiCheckCircle, label: 'Assignments', value: sampleData.assignmentCompletion, color: 'purple' },
            { icon: FiClock, label: 'On-Time Rate', value: sampleData.onTimeRate, color: 'indigo' }
          ].map((item, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <item.icon className={`w-5 h-5 text-${item.color}-600 dark:text-${item.color}-400`} />
                <span className={`text-2xl font-bold text-${item.color}-600 dark:text-${item.color}-400`}>
                  {item.value}%
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div 
                  className={`bg-${item.color}-600 h-2 rounded-full transition-all duration-500`}
                  style={{ width: `${item.value}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Improvement Opportunities */}
      <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-700 rounded-xl p-6">
        <div className="flex items-center mb-6">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg mr-3">
            <FiTarget className="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 className="text-xl font-bold text-orange-800 dark:text-orange-200">Improvement Opportunities</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {improvementOpportunities.map((opportunity, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200">
              <div className="flex items-start">
                <div className="p-1 bg-orange-100 dark:bg-orange-900/30 rounded-full mr-3 mt-0.5">
                  <div className="w-3 h-3 bg-orange-600 dark:bg-orange-400 rounded-full"></div>
                </div>
                <div className="flex-1">
                  <p className="text-gray-800 dark:text-gray-200 text-sm font-medium leading-relaxed">
                    {opportunity}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsTest;
