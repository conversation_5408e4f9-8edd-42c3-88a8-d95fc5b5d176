import { useEffect, useState } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useNotification } from "../../contexts/NotificationContext";
import competitionExamService from "../../services/competitionExamService";
import {
  FiAward,
  FiClock,
  FiUsers,
  FiAlertTriangle,
  FiPlay,
  FiLoader
} from "react-icons/fi";

/**
 * Student Take Competition Exam Component
 *
 * This component handles competition exam taking for students.
 * It shows competition rules and then redirects to the regular exam interface.
 */
function StudentTakeCompetitionExam() {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification, showError, showSuccess } = useNotification();

  // Debug params extraction
  console.log('🔍 URL Params:', params);
  console.log('🔍 Location:', location);
  console.log('🔍 Pathname:', location.pathname);

  // Extract eventId from URL path as fallback
  const pathParts = location.pathname.split('/');
  const eventIdFromPath = pathParts[pathParts.length - 1];

  const eventId = params.eventId || params.examId || eventIdFromPath; // Try multiple sources

  console.log('🔍 Final eventId:', eventId);

  // Get event data from location state
  const event = location.state?.event;
  const examId = location.state?.examId; // Get exam ID from state

  // Local state
  const [showRulesModal, setShowRulesModal] = useState(true);
  const [loading, setLoading] = useState(false);
  const [competitionInfo, setCompetitionInfo] = useState(null);

  /**
   * Load competition exam info
   */
  useEffect(() => {
    const loadCompetitionData = async () => {
      console.log('🔍 Debug - Component mounted with:', {
        eventId,
        examId,
        event,
        locationState: location.state,
        params: { eventId }
      });

      if (!eventId) {
        console.error('❌ No eventId provided!');
        console.error('❌ Debug info:', {
          params,
          pathParts,
          eventIdFromPath,
          locationState: location.state
        });
        showError(`Invalid event ID. Debug: params=${JSON.stringify(params)}, path=${location.pathname}`);
        return;
      }

      setLoading(true);
      try {
        console.log('🔍 Loading competition exam info for event:', eventId);

        // Just load basic competition info - no status needed
        const info = await competitionExamService.getCompetitionExamInfo(eventId);
        console.log('📊 Competition info:', info);

        setCompetitionInfo(info);

      } catch (error) {
        console.error('❌ Failed to load competition data:', error);
        showError(error.message || 'Failed to load competition information');
      } finally {
        setLoading(false);
      }
    };

    loadCompetitionData();
  }, [eventId, examId, event, location.state, showError]);

  /**
   * Start the competition exam - use same flow as teacher exams
   */
  const startCompetitionExam = () => {
    console.log('🏆 Starting competition exam with:', {
      eventId,
      examId: competitionInfo?.exam_id || examId,
      competitionInfo,
      event
    });

    const finalExamId = competitionInfo?.exam_id || examId;

    if (!finalExamId) {
      showError('No exam ID found for this competition');
      return;
    }

    setShowRulesModal(false);

    // Navigate to regular exam interface with competition context
    // Use the SAME flow as teacher-student exams
    navigate(`/student/take-exam/${finalExamId}`, {
      state: {
        competitionMode: true,
        event: event || competitionInfo,
        eventId: eventId,
        returnPath: `/student/competition-exam-submitted/${eventId}`
      }
    });
  };

  // Show error if no event data
  if (!event) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Competition Data Missing</h2>
          <p className="text-gray-600 mb-4">Unable to load competition information.</p>
          <button
            onClick={() => navigate('/student/events/my')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Return to My Events
          </button>
        </div>
      </div>
    );
  }
  // Competition Rules Modal
  const CompetitionRulesModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <FiAward className="h-6 w-6 text-purple-600 mr-3" />
            <h2 className="text-xl font-bold text-gray-900">Competition Rules & Guidelines</h2>
          </div>
        </div>

        <div className="p-6 space-y-4">
          {/* Competition Info */}
          {event && (
            <div className="bg-purple-50 rounded-lg p-4 mb-4">
              <h3 className="font-semibold text-purple-900 mb-2">{event.title}</h3>
              <p className="text-purple-700 text-sm">{event.description}</p>
            </div>
          )}

          {/* General Rules */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center">
              <FiAlertTriangle className="h-4 w-4 text-orange-500 mr-2" />
              Important Rules
            </h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• This is a timed competition exam - manage your time wisely</li>
              <li>• No external help or resources are allowed</li>
              <li>• Switching tabs or applications may result in disqualification</li>
              <li>• Your screen activity is monitored for fair play</li>
              <li>• Once started, you cannot pause the exam</li>
              <li>• Submit before time runs out to ensure your answers are recorded</li>
            </ul>
          </div>

          {/* Prize Information */}
          <div className="bg-yellow-50 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-900 mb-2 flex items-center">
              <FiAward className="h-4 w-4 mr-2" />
              Competition Prizes
            </h4>
            <div className="text-sm text-yellow-800">
              <p>🥇 First Place: Certificate + Recognition</p>
              <p>🥈 Second Place: Certificate</p>
              <p>🥉 Third Place: Certificate</p>
            </div>
          </div>
        </div>

        {/* Competition Info */}
        {competitionInfo && (
          <div className="p-4 bg-purple-50 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <div>
                <span className="text-purple-700 font-medium">
                  🏆 {competitionInfo.exam_title}
                </span>
              </div>
              <div className="text-purple-600">
                <span>{competitionInfo.total_questions} Questions • {competitionInfo.total_duration} Minutes • {competitionInfo.total_marks} Marks</span>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 border-t border-gray-200 flex justify-between">
          <button
            onClick={() => navigate('/student/events/my')}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>

          <button
            onClick={startCompetitionExam}
            disabled={loading || !competitionInfo?.exam_id}
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium flex items-center space-x-2 disabled:opacity-50"
          >
            {loading ? <FiLoader className="h-4 w-4 animate-spin" /> : <FiPlay className="h-4 w-4" />}
            <span>{loading ? 'Loading...' : 'I Understand - Start Competition'}</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Competition Rules Modal */}
      {showRulesModal && <CompetitionRulesModal />}
    </div>
  );
}

export default StudentTakeCompetitionExam;
