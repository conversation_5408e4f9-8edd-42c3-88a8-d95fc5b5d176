import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Mentor Evaluation Service
 * Handles all mentor evaluation-related API operations based on OpenAPI specification
 */

const getAuthToken = () => localStorage.getItem("token");

const getAuthHeaders = () => ({
  Authorization: `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Mentor Dashboard APIs
 */

// Get Mentor Dashboard Statistics
export const getMentorDashboard = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/mentor/evaluation/dashboard`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching mentor dashboard:', error);
    throw error;
  }
};

// Get Assigned Competitions
export const getAssignedCompetitions = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/mentor/evaluation/competitions`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching assigned competitions:', error);
    throw error;
  }
};

/**
 * Submission Management APIs
 */

// Get Competition Submissions
export const getCompetitionSubmissions = async (competitionId, params = {}) => {
  try {
    const queryParams = new URLSearchParams();
    
    // Add query parameters
    if (params.skip !== undefined) queryParams.append('skip', params.skip);
    if (params.limit !== undefined) queryParams.append('limit', params.limit);
    if (params.status) queryParams.append('status', params.status);

    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/submissions?${queryParams}`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competition submissions:', error);
    throw error;
  }
};

// Get Submission Details
export const getSubmissionDetails = async (attemptId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/${attemptId}`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching submission details:', error);
    throw error;
  }
};

// Mark Submission
export const markSubmission = async (markingData) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/mark`,
      markingData,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error marking submission:', error);
    throw error;
  }
};

// Get Submission Evaluation Status
export const getSubmissionEvaluationStatus = async (attemptId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/${attemptId}/status`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching submission status:', error);
    throw error;
  }
};

/**
 * Competition Progress and Rankings APIs
 */

// Get Competition Evaluation Progress
export const getCompetitionEvaluationProgress = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/progress`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching evaluation progress:', error);
    throw error;
  }
};

// Get Competition Rankings
export const getCompetitionRankings = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/rankings`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competition rankings:', error);
    throw error;
  }
};

/**
 * Certificate Management APIs
 */

// Generate Winner Certificate
export const generateWinnerCertificate = async (competitionId, certificateData) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/certificates/generate`,
      certificateData,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error generating certificate:', error);
    throw error;
  }
};

// Get Competition Certificates List
export const getCompetitionCertificates = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/certificates`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching certificates:', error);
    throw error;
  }
};

// Get Student Certificates
export const getStudentCertificates = async () => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/students/certificates`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching student certificates:', error);
    throw error;
  }
};

// Get Certificate Details
export const getCertificateDetails = async (certificateId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/students/certificates/${certificateId}`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching certificate details:', error);
    throw error;
  }
};

// Verify Certificate (Public endpoint - no auth required)
export const verifyCertificate = async (verificationCode) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/certificates/verify/${verificationCode}`
      // Note: No auth headers for public verification
    );
    return response.data;
  } catch (error) {
    console.error('Error verifying certificate:', error);
    throw error;
  }
};

/**
 * Utility Functions
 */

// Calculate percentage from score
export const calculatePercentage = (score, maxScore) => {
  if (!maxScore || maxScore === 0) return 0;
  return Math.round((score / maxScore) * 100);
};

// Format evaluation status
export const formatEvaluationStatus = (status) => {
  const statusMap = {
    'pending': 'Pending Review',
    'checked': 'Evaluated',
    'all': 'All Submissions'
  };
  return statusMap[status] || status;
};

// Get position suffix (1st, 2nd, 3rd, etc.)
export const getPositionSuffix = (position) => {
  if (position === 1) return '1st';
  if (position === 2) return '2nd';
  if (position === 3) return '3rd';
  return `${position}th`;
};

export default {
  // Dashboard
  getMentorDashboard,
  getAssignedCompetitions,

  // Submissions
  getCompetitionSubmissions,
  getSubmissionDetails,
  markSubmission,
  getSubmissionEvaluationStatus,

  // Progress & Rankings
  getCompetitionEvaluationProgress,
  getCompetitionRankings,

  // Certificates
  generateWinnerCertificate,
  getCompetitionCertificates,
  getStudentCertificates,
  getCertificateDetails,
  verifyCertificate,

  // Utilities
  calculatePercentage,
  formatEvaluationStatus,
  getPositionSuffix
};
