import React, { useState, useEffect } from 'react';
import {
  FiAward,
  FiDownload,
  FiEye,
  FiCalendar,
  FiTrendingUp,
  FiUsers,
  FiTarget,
  FiRefreshCw
} from 'react-icons/fi';
import { getStudentCertificates } from '../../services/mentorEvaluationService';
import { LoadingSpinner, ErrorMessage } from '../ui';
import CertificateViewer from './CertificateViewer';

const StudentCertificates = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [certificates, setCertificates] = useState([]);
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const [viewerOpen, setViewerOpen] = useState(false);

  useEffect(() => {
    loadCertificates();
  }, []);

  const loadCertificates = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getStudentCertificates();
      
      // Handle different response structures
      if (response && response.certificates) {
        setCertificates(response.certificates);
      } else if (Array.isArray(response)) {
        setCertificates(response);
      } else {
        setCertificates([]);
      }
    } catch (err) {
      console.error('Error loading certificates:', err);
      setError('Failed to load certificates. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewCertificate = (certificate) => {
    setSelectedCertificate(certificate);
    setViewerOpen(true);
  };

  const handleDownloadCertificate = (certificate) => {
    if (certificate.certificate_url) {
      const baseUrl = 'http://127.0.0.1:8000';
      const downloadUrl = `${baseUrl}${certificate.certificate_url}`;
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `certificate-${certificate.participant_name || 'certificate'}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPositionText = (position) => {
    switch (position) {
      case 1: return '1st';
      case 2: return '2nd';
      case 3: return '3rd';
      default: return `${position}th`;
    }
  };

  const getPositionEmoji = (position) => {
    switch (position) {
      case 1: return '🏆';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return '🏅';
    }
  };

  const getPositionColor = (position) => {
    switch (position) {
      case 1: return 'text-yellow-600 bg-yellow-100';
      case 2: return 'text-gray-600 bg-gray-100';
      case 3: return 'text-amber-600 bg-amber-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  const calculateStats = () => {
    if (certificates.length === 0) return { total: 0, firstPlace: 0, avgScore: 0 };
    
    const total = certificates.length;
    const firstPlace = certificates.filter(cert => (cert.rank_position || cert.position) === 1).length;
    const avgScore = certificates.reduce((sum, cert) => sum + (cert.score_percentage || 0), 0) / total;
    
    return { total, firstPlace, avgScore: Math.round(avgScore) };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadCertificates} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Certificates</h1>
          <p className="text-gray-600">View and download your earned certificates</p>
        </div>
        <button
          onClick={loadCertificates}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <FiRefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Statistics */}
      {certificates.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiAward className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Certificates</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiTrendingUp className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">First Place Wins</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.firstPlace}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiTarget className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Score</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.avgScore}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Certificates Grid */}
      {certificates.length === 0 ? (
        <div className="text-center py-12">
          <FiAward className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No certificates yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Participate in competitions to earn certificates!
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {certificates.map((certificate, index) => (
            <div key={certificate.certificate_id || index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {/* Certificate Header */}
              <div className="p-6 pb-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-3xl">
                    {getPositionEmoji(certificate.rank_position || certificate.position)}
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPositionColor(certificate.rank_position || certificate.position)}`}>
                    {getPositionText(certificate.rank_position || certificate.position)} Place
                  </span>
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {certificate.competition_title || certificate.competition_name || 'Competition Certificate'}
                </h3>
                
                <p className="text-sm text-gray-600 mb-3">
                  {certificate.certification_type || certificate.certificate_type || 'Achievement Certificate'}
                </p>

                {/* Score Information */}
                <div className="flex items-center justify-between text-sm">
                  <div>
                    <span className="text-gray-500">Score:</span>
                    <span className="ml-1 font-medium text-gray-900">
                      {certificate.final_score || 'N/A'}
                    </span>
                    {certificate.score_percentage && (
                      <span className="ml-1 text-gray-500">
                        ({certificate.score_percentage}%)
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-gray-500">
                    <FiCalendar className="h-4 w-4 mr-1" />
                    {formatDate(certificate.issued_date || certificate.issued_at)}
                  </div>
                </div>
              </div>

              {/* Certificate Actions */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    ID: {certificate.certificate_id?.slice(0, 8)}...
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleViewCertificate(certificate)}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-3 w-3 mr-1" />
                      View
                    </button>
                    {certificate.certificate_url && (
                      <button
                        onClick={() => handleDownloadCertificate(certificate)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        <FiDownload className="h-3 w-3 mr-1" />
                        PDF
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Certificate Viewer Modal */}
      <CertificateViewer
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        certificate={selectedCertificate}
      />
    </div>
  );
};

export default StudentCertificates;
