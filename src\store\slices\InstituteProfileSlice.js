import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

const BASE_URL = `${API_BASE_URL}/api/institutes`;
const getAuthToken = () => localStorage.getItem("token");

// Async Thunks

// Fetch document binary data
export const fetchInstituteDocument = createAsyncThunk(
  "instituteProfile/fetchDocument",
  async (documentPath, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/document/${documentPath}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch institute profile (includes documents)
export const fetchInstituteProfile = createAsyncThunk(
  "instituteProfile/fetchProfile",
  async (_, thunkAPI) => {
    try {
      // Use the with-documents endpoint to get profile including documents
      const res = await axios.get(`${BASE_URL}/profile/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Check if the response contains an error (API returns 200 with error details)
      if (res.data?.detail?.error === 'PROFILE_NOT_CREATED' || res.data?.detail?.error === 'PROFILE_NOT_FOUND') {
        return thunkAPI.rejectWithValue({
          ...res.data,
          isProfileNotFound: true
        });
      }

      return res.data;
    } catch (err) {
      const errorData = err.response?.data || err.message;

      // Handle specific PROFILE_NOT_FOUND error (for backward compatibility)
      if (errorData?.detail?.error === 'PROFILE_NOT_FOUND' || errorData?.detail?.error === 'PROFILE_NOT_CREATED') {
        return thunkAPI.rejectWithValue({
          ...errorData,
          isProfileNotFound: true
        });
      }

      return thunkAPI.rejectWithValue(errorData);
    }
  }
);

// Create/Update institute profile
export const saveInstituteProfile = createAsyncThunk(
  "instituteProfile/saveProfile",
  async (profileData, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/profile`, profileData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json"
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Helper function to convert file to base64
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result.split(',')[1]); // Remove data:type;base64, prefix
    reader.onerror = error => reject(error);
  });
};

// Create/Update institute profile with documents
export const saveInstituteProfileWithDocuments = createAsyncThunk(
  "instituteProfile/saveProfileWithDocuments",
  async ({ profileData, documents }, thunkAPI) => {
    try {
      // Filter for documents that have actual files to upload
      const validDocuments = documents.filter(doc => doc.file && doc.file instanceof File);

      if (validDocuments.length === 0) {
        throw new Error('No documents provided for document upload endpoint.');
      }

      // Convert files to base64 and prepare documents array
      const documentsWithBase64 = await Promise.all(
        validDocuments.map(async (doc) => {
          const base64Data = await fileToBase64(doc.file);
          return {
            filename: doc.file.name,
            content_type: doc.file.type,
            data: base64Data,
            document_type: doc.type || 'other',
            description: doc.description || ''
          };
        })
      );

      // Prepare the JSON payload as per the API specification you provided
      const payload = {
        ...profileData,
        documents: documentsWithBase64
      };

      console.log('Sending JSON payload:', payload);

      const res = await axios.put(`${BASE_URL}/profile/with-documents`, payload, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch profile completion status
export const fetchProfileStatus = createAsyncThunk(
  "instituteProfile/fetchProfileStatus",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/profile/status`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Submit profile for admin approval
export const submitForApproval = createAsyncThunk(
  "instituteProfile/submitForApproval",
  async (_, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/profile/submit-for-verification`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Approve institute profile
export const approveInstituteProfile = createAsyncThunk(
  "instituteProfile/approveProfile",
  async ({ instituteId, approvalData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/approve`, approvalData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Reject institute profile
export const rejectInstituteProfile = createAsyncThunk(
  "instituteProfile/rejectProfile",
  async ({ instituteId, rejectionData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/reject`, rejectionData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch pending verification institutes (Admin only) - Updated to match new API
export const fetchPendingVerificationInstitutes = createAsyncThunk(
  "instituteProfile/fetchPendingVerificationInstitutes",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${BASE_URL}/admin/pending-verification?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      console.log('fetchPendingVerificationInstitutes API response:', res.data);

      // Handle the API response format based on the documentation
      const data = res.data;

      // Handle different possible response formats
      let institutes = [];

      if (Array.isArray(data)) {
        // If the response is directly an array
        institutes = data;
      } else if (data.institutes && Array.isArray(data.institutes)) {
        // If the response has an institutes array
        institutes = data.institutes;
      } else if (data.user || data.profile) {
        // If the response is a single institute object
        institutes = [data];
      } else {
        // Default to empty array
        institutes = [];
      }

      return {
        institutes: institutes,
        total: data.total || institutes.length,
        page: data.page || 0,
        size: data.size || limit,
        has_next: data.has_next || false,
        has_prev: data.has_prev || false
      };
    } catch (err) {
      console.error('fetchPendingVerificationInstitutes error:', err);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Legacy function for backward compatibility - now calls the new function
export const fetchPendingInstitutes = createAsyncThunk(
  "instituteProfile/fetchPendingInstitutes",
  async (params, thunkAPI) => {
    // Delegate to the new function
    return thunkAPI.dispatch(fetchPendingVerificationInstitutes(params)).unwrap();
  }
);

// Fetch detailed institute information (Admin only) - Updated to match new API
export const fetchInstituteDetailsAdmin = createAsyncThunk(
  "instituteProfile/fetchInstituteDetailsAdmin",
  async (instituteId, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/admin/institute/${instituteId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      console.log('fetchInstituteDetailsAdmin API response:', res.data);
      return res.data;
    } catch (err) {
      console.error('fetchInstituteDetailsAdmin error:', err);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Legacy function for backward compatibility
export const fetchInstituteDetails = createAsyncThunk(
  "instituteProfile/fetchInstituteDetails",
  async (instituteId, thunkAPI) => {
    // Delegate to the new function
    return thunkAPI.dispatch(fetchInstituteDetailsAdmin(instituteId)).unwrap();
  }
);

// Fetch all institutes list (Admin only) - Lightweight data for lists
export const fetchAllInstitutesList = createAsyncThunk(
  "instituteProfile/fetchAllInstitutesList",
  async ({ skip = 0, limit = 20, status = "", search = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) params.append('status', status);
      if (search) params.append('search', search);

      const res = await axios.get(`${API_BASE_URL}/api/admin/institutes/list?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// NEW APIs - Admin Institute Approval/Rejection and Institute Verification Status

// Approve Institute (Admin only) - Updated to use correct API endpoint
export const approveInstituteAdmin = createAsyncThunk(
  "instituteProfile/approveInstituteAdmin",
  async ({ institute_id, verification_notes = "" }, thunkAPI) => {
    try {
      const res = await axios.put(
        `${API_BASE_URL}/api/institutes/admin/approve`,
        {
          institute_id,
          verification_notes
        },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json'
          },
        }
      );
      return { ...res.data, institute_id }; // Include institute_id in response for state updates
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Reject Institute (Admin only) - Updated to use correct API endpoint
export const rejectInstituteAdmin = createAsyncThunk(
  "instituteProfile/rejectInstituteAdmin",
  async ({ institute_id, verification_notes = "" }, thunkAPI) => {
    try {
      const res = await axios.put(
        `${API_BASE_URL}/api/institutes/admin/reject`,
        {
          institute_id,
          verification_notes
        },
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json'
          },
        }
      );
      return { ...res.data, institute_id }; // Include institute_id in response for state updates
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Institute Verification Status (Self institute only)
export const getInstituteVerificationStatus = createAsyncThunk(
  "instituteProfile/getVerificationStatus",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/institutes/verification-status`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch individual institute profile (Admin only) - Includes documents
export const fetchInstituteProfileById = createAsyncThunk(
  "instituteProfile/fetchInstituteProfileById",
  async (instituteId, thunkAPI) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/institutes/${instituteId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  profile: null,
  profileLoading: false,
  profileError: null,
  profileNotFound: false, // Flag to track when profile doesn't exist

  saveLoading: false,
  saveError: null,
  saveSuccess: false,

  submitLoading: false,
  submitError: null,
  submitSuccess: false,

  // Admin states - Pending verification institutes
  pendingInstitutes: [],
  pendingLoading: false,
  pendingError: null,
  pendingTotal: 0,
  pendingPage: 0,
  pendingSize: 20,
  pendingHasNext: false,
  pendingHasPrev: false,

  // Admin institute list (lightweight)
  institutesList: [],
  institutesListLoading: false,
  institutesListError: null,
  institutesListTotal: 0,

  // Admin individual institute profile (with documents)
  selectedInstituteProfile: null,
  selectedInstituteLoading: false,
  selectedInstituteError: null,

  approveLoading: false,
  approveError: null,
  approveSuccess: false,

  rejectLoading: false,
  rejectError: null,
  rejectSuccess: false,

  // Profile completion status
  isProfileComplete: false,
  approvalStatus: 'draft', // 'draft', 'pending', 'approved', 'rejected'
  rejectionReason: null,
  approvalDate: null,
  rejectionDate: null,

  // Document fetch state
  currentDocument: null,
  documentLoading: false,
  documentError: null,

  // NEW API states
  // Admin approval/rejection states
  adminApprovalLoading: false,
  adminApprovalError: null,
  adminApprovalSuccess: false,

  adminRejectionLoading: false,
  adminRejectionError: null,
  adminRejectionSuccess: false,

  // Institute verification status states
  verificationStatus: null,
  verificationStatusLoading: false,
  verificationStatusError: null
};

// Slice
const instituteProfileSlice = createSlice({
  name: "instituteProfile",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.profileError = null;
      state.saveError = null;
      state.submitError = null;
      state.pendingError = null;
      state.approveError = null;
      state.rejectError = null;
      state.profileNotFound = false;
    },
    clearSuccessStates: (state) => {
      state.saveSuccess = false;
      state.submitSuccess = false;
      state.approveSuccess = false;
      state.rejectSuccess = false;
    },
    updateProfileField: (state, action) => {
      const { field, value } = action.payload;
      if (state.profile) {
        state.profile[field] = value;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Profile
      .addCase(fetchInstituteProfile.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
        state.profileNotFound = false;
      })
      .addCase(fetchInstituteProfile.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.profileNotFound = false;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.rejectionReason = responseData.profile?.verification_notes;
        state.approvalDate = responseData.profile?.verified_at;
        state.isProfileComplete = !!responseData.profile?.institute_name; // Basic check for profile completion
      })
      .addCase(fetchInstituteProfile.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;

        // Check if this is a PROFILE_NOT_FOUND error
        if (action.payload?.isProfileNotFound) {
          state.profileNotFound = true;
          state.approvalStatus = 'not_created';
          state.isProfileComplete = false;
        }
      })

      // Fetch Profile Status
      .addCase(fetchProfileStatus.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
      })
      .addCase(fetchProfileStatus.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.approvalStatus = action.payload || 'draft';
      })
      .addCase(fetchProfileStatus.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;
      })

      // Save Profile
      .addCase(saveInstituteProfile.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfile.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
        // Reset profileNotFound since we now have a profile
        state.profileNotFound = false;
        state.profileError = null;
      })
      .addCase(saveInstituteProfile.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Save Profile with Documents
      .addCase(saveInstituteProfileWithDocuments.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfileWithDocuments.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
        // Reset profileNotFound since we now have a profile
        state.profileNotFound = false;
        state.profileError = null;
      })
      .addCase(saveInstituteProfileWithDocuments.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Submit for Approval
      .addCase(submitForApproval.pending, (state) => {
        state.submitLoading = true;
        state.submitError = null;
        state.submitSuccess = false;
      })
      .addCase(submitForApproval.fulfilled, (state, action) => {
        state.submitLoading = false;
        state.submitSuccess = true;
        state.approvalStatus = 'pending';
      })
      .addCase(submitForApproval.rejected, (state, action) => {
        state.submitLoading = false;
        state.submitError = action.payload;
      })

      // Fetch Pending Verification Institutes (Admin) - New API
      .addCase(fetchPendingVerificationInstitutes.pending, (state) => {
        state.pendingLoading = true;
        state.pendingError = null;
      })
      .addCase(fetchPendingVerificationInstitutes.fulfilled, (state, action) => {
        state.pendingLoading = false;
        const payload = action.payload;
        state.pendingInstitutes = payload.institutes || [];
        state.pendingTotal = payload.total || 0;
        state.pendingPage = payload.page || 0;
        state.pendingSize = payload.size || 20;
        state.pendingHasNext = payload.has_next || false;
        state.pendingHasPrev = payload.has_prev || false;
      })
      .addCase(fetchPendingVerificationInstitutes.rejected, (state, action) => {
        state.pendingLoading = false;
        state.pendingError = action.payload;
      })

      // Legacy Fetch Pending Institutes (Admin) - For backward compatibility
      .addCase(fetchPendingInstitutes.pending, (state) => {
        state.pendingLoading = true;
        state.pendingError = null;
      })
      .addCase(fetchPendingInstitutes.fulfilled, (state, action) => {
        state.pendingLoading = false;
        // Handle both old and new response formats
        if (action.payload && typeof action.payload === 'object' && action.payload.institutes) {
          state.pendingInstitutes = action.payload.institutes || [];
          state.pendingTotal = action.payload.total || 0;
          state.pendingPage = action.payload.page || 0;
          state.pendingSize = action.payload.size || 20;
          state.pendingHasNext = action.payload.has_next || false;
          state.pendingHasPrev = action.payload.has_prev || false;
        } else {
          // Legacy format - just an array
          state.pendingInstitutes = Array.isArray(action.payload) ? action.payload : [];
        }
      })
      .addCase(fetchPendingInstitutes.rejected, (state, action) => {
        state.pendingLoading = false;
        state.pendingError = action.payload;
      })

      // Fetch Institute Details Admin (Admin) - New API
      .addCase(fetchInstituteDetailsAdmin.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteDetailsAdmin.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteDetailsAdmin.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      })

      // Legacy Fetch Institute Details (Admin) - For backward compatibility
      .addCase(fetchInstituteDetails.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteDetails.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteDetails.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      })

      // Approve Institute (Admin)
      .addCase(approveInstituteProfile.pending, (state) => {
        state.approveLoading = true;
        state.approveError = null;
        state.approveSuccess = false;
      })
      .addCase(approveInstituteProfile.fulfilled, (state, action) => {
        state.approveLoading = false;
        state.approveSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(approveInstituteProfile.rejected, (state, action) => {
        state.approveLoading = false;
        state.approveError = action.payload;
      })

      // Reject Institute (Admin)
      .addCase(rejectInstituteProfile.pending, (state) => {
        state.rejectLoading = true;
        state.rejectError = null;
        state.rejectSuccess = false;
      })
      .addCase(rejectInstituteProfile.fulfilled, (state, action) => {
        state.rejectLoading = false;
        state.rejectSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(rejectInstituteProfile.rejected, (state, action) => {
        state.rejectLoading = false;
        state.rejectError = action.payload;
      })

      // Fetch All Institutes List (Admin)
      .addCase(fetchAllInstitutesList.pending, (state) => {
        state.institutesListLoading = true;
        state.institutesListError = null;
      })
      .addCase(fetchAllInstitutesList.fulfilled, (state, action) => {
        state.institutesListLoading = false;
        state.institutesList = action.payload.data || action.payload.institutes || [];
        state.institutesListTotal = action.payload.total || 0;
      })
      .addCase(fetchAllInstitutesList.rejected, (state, action) => {
        state.institutesListLoading = false;
        state.institutesListError = action.payload;
      })

      // Fetch Institute Profile By ID (Admin)
      .addCase(fetchInstituteProfileById.pending, (state) => {
        state.selectedInstituteLoading = true;
        state.selectedInstituteError = null;
      })
      .addCase(fetchInstituteProfileById.fulfilled, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteProfile = action.payload;
      })
      .addCase(fetchInstituteProfileById.rejected, (state, action) => {
        state.selectedInstituteLoading = false;
        state.selectedInstituteError = action.payload;
      })

      // Fetch Institute Document
      .addCase(fetchInstituteDocument.pending, (state) => {
        state.documentLoading = true;
        state.documentError = null;
      })
      .addCase(fetchInstituteDocument.fulfilled, (state, action) => {
        state.documentLoading = false;
        state.currentDocument = action.payload;
      })
      .addCase(fetchInstituteDocument.rejected, (state, action) => {
        state.documentLoading = false;
        state.documentError = action.payload;
      })

      // NEW API reducers - Admin Approval
      .addCase(approveInstituteAdmin.pending, (state) => {
        state.adminApprovalLoading = true;
        state.adminApprovalError = null;
        state.adminApprovalSuccess = false;
      })
      .addCase(approveInstituteAdmin.fulfilled, (state, action) => {
        state.adminApprovalLoading = false;
        state.adminApprovalSuccess = true;

        const instituteId = action.payload.institute_id;

        // Update the selected institute if it's the one being approved
        if (state.selectedInstituteProfile && state.selectedInstituteProfile.id === instituteId) {
          state.selectedInstituteProfile.verification_status = 'approved';
          state.selectedInstituteProfile.verification_notes = action.payload.verification_notes;
          state.selectedInstituteProfile.verified_at = new Date().toISOString();
        }

        // Remove from pending institutes list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== instituteId
        );

        // Update in all institutes list
        const instituteIndex = state.institutesList.findIndex(
          institute => institute.id === instituteId
        );
        if (instituteIndex !== -1) {
          state.institutesList[instituteIndex] = {
            ...state.institutesList[instituteIndex],
            verification_status: 'approved',
            verification_notes: action.payload.verification_notes,
            verified_at: new Date().toISOString()
          };
        }
      })
      .addCase(approveInstituteAdmin.rejected, (state, action) => {
        state.adminApprovalLoading = false;
        state.adminApprovalError = action.payload;
      })

      // NEW API reducers - Admin Rejection
      .addCase(rejectInstituteAdmin.pending, (state) => {
        state.adminRejectionLoading = true;
        state.adminRejectionError = null;
        state.adminRejectionSuccess = false;
      })
      .addCase(rejectInstituteAdmin.fulfilled, (state, action) => {
        state.adminRejectionLoading = false;
        state.adminRejectionSuccess = true;

        const instituteId = action.payload.institute_id;

        // Update the selected institute if it's the one being rejected
        if (state.selectedInstituteProfile && state.selectedInstituteProfile.id === instituteId) {
          state.selectedInstituteProfile.verification_status = 'rejected';
          state.selectedInstituteProfile.verification_notes = action.payload.verification_notes;
          state.selectedInstituteProfile.verified_at = new Date().toISOString();
        }

        // Remove from pending institutes list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== instituteId
        );

        // Update in all institutes list
        const instituteIndex = state.institutesList.findIndex(
          institute => institute.id === instituteId
        );
        if (instituteIndex !== -1) {
          state.institutesList[instituteIndex] = {
            ...state.institutesList[instituteIndex],
            verification_status: 'rejected',
            verification_notes: action.payload.verification_notes,
            verified_at: new Date().toISOString()
          };
        }
      })
      .addCase(rejectInstituteAdmin.rejected, (state, action) => {
        state.adminRejectionLoading = false;
        state.adminRejectionError = action.payload;
      })

      // NEW API reducers - Institute Verification Status
      .addCase(getInstituteVerificationStatus.pending, (state) => {
        state.verificationStatusLoading = true;
        state.verificationStatusError = null;
      })
      .addCase(getInstituteVerificationStatus.fulfilled, (state, action) => {
        state.verificationStatusLoading = false;
        state.verificationStatus = action.payload;
        // Also update the profile verification status if available
        if (state.profile) {
          state.profile.verification_status = action.payload.verification_status;
          state.profile.verification_notes = action.payload.verification_notes;
          state.profile.verified_at = action.payload.verified_at;
        }
      })
      .addCase(getInstituteVerificationStatus.rejected, (state, action) => {
        state.verificationStatusLoading = false;
        state.verificationStatusError = action.payload;
      });
  },
});

export const { clearErrors, clearSuccessStates, updateProfileField } = instituteProfileSlice.actions;

// Selectors
export const selectProfile = (state) => state.instituteProfile.profile;
export const selectProfileLoading = (state) => state.instituteProfile.profileLoading;
export const selectProfileError = (state) => state.instituteProfile.profileError;
export const selectProfileNotFound = (state) => state.instituteProfile.profileNotFound;
export const selectSaveLoading = (state) => state.instituteProfile.saveLoading;
export const selectSaveError = (state) => state.instituteProfile.saveError;
export const selectSaveSuccess = (state) => state.instituteProfile.saveSuccess;
export const selectSubmitLoading = (state) => state.instituteProfile.submitLoading;
export const selectSubmitError = (state) => state.instituteProfile.submitError;
export const selectSubmitSuccess = (state) => state.instituteProfile.submitSuccess;
export const selectApprovalStatus = (state) => state.instituteProfile.approvalStatus;
export const selectIsProfileComplete = (state) => state.instituteProfile.isProfileComplete;
export const selectRejectionReason = (state) => state.instituteProfile.rejectionReason;

// Admin selectors - Pending verification institutes
export const selectPendingInstitutes = (state) => state.instituteProfile.pendingInstitutes;
export const selectPendingLoading = (state) => state.instituteProfile.pendingLoading;
export const selectPendingError = (state) => state.instituteProfile.pendingError;
export const selectPendingTotal = (state) => state.instituteProfile.pendingTotal;
export const selectPendingPage = (state) => state.instituteProfile.pendingPage;
export const selectPendingSize = (state) => state.instituteProfile.pendingSize;
export const selectPendingHasNext = (state) => state.instituteProfile.pendingHasNext;
export const selectPendingHasPrev = (state) => state.instituteProfile.pendingHasPrev;

// Admin institutes list selectors
export const selectInstitutesList = (state) => state.instituteProfile.institutesList;
export const selectInstitutesListLoading = (state) => state.instituteProfile.institutesListLoading;
export const selectInstitutesListError = (state) => state.instituteProfile.institutesListError;

// Document selectors
export const selectCurrentDocument = (state) => state.instituteProfile.currentDocument;
export const selectDocumentLoading = (state) => state.instituteProfile.documentLoading;
export const selectDocumentError = (state) => state.instituteProfile.documentError;
export const selectInstitutesListTotal = (state) => state.instituteProfile.institutesListTotal;

// Admin selected institute selectors
export const selectSelectedInstituteProfile = (state) => state.instituteProfile.selectedInstituteProfile;
export const selectSelectedInstituteLoading = (state) => state.instituteProfile.selectedInstituteLoading;
export const selectSelectedInstituteError = (state) => state.instituteProfile.selectedInstituteError;

// Admin action selectors
export const selectApproveLoading = (state) => state.instituteProfile.approveLoading;
export const selectApproveSuccess = (state) => state.instituteProfile.approveSuccess;
export const selectRejectLoading = (state) => state.instituteProfile.rejectLoading;
export const selectRejectSuccess = (state) => state.instituteProfile.rejectSuccess;

// NEW API selectors
// Admin approval/rejection selectors
export const selectAdminApprovalLoading = (state) => state.instituteProfile.adminApprovalLoading;
export const selectAdminApprovalError = (state) => state.instituteProfile.adminApprovalError;
export const selectAdminApprovalSuccess = (state) => state.instituteProfile.adminApprovalSuccess;

export const selectAdminRejectionLoading = (state) => state.instituteProfile.adminRejectionLoading;
export const selectAdminRejectionError = (state) => state.instituteProfile.adminRejectionError;
export const selectAdminRejectionSuccess = (state) => state.instituteProfile.adminRejectionSuccess;

// Institute verification status selectors
export const selectVerificationStatus = (state) => state.instituteProfile.verificationStatus;
export const selectVerificationStatusLoading = (state) => state.instituteProfile.verificationStatusLoading;
export const selectVerificationStatusError = (state) => state.instituteProfile.verificationStatusError;

export default instituteProfileSlice.reducer;
