import React, { useState, useEffect } from 'react';
import { useThemeProvider } from "../../providers/ThemeContext";
import URL from "../../utils/api/API_URL";

const WebSocketTest = () => {
  const { currentTheme } = useThemeProvider();
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";

  const connectWebSocket = () => {
    const examId = '8d81b7bf-6f2b-46ce-8410-de631a527cdf'; // Test exam ID
    const studentId = 'test-student-123';
    const token = localStorage.getItem('token') || 'test-token'; // Get token for authentication

    // Convert HTTP URL to WebSocket URL
    const baseUrl = URL.replace('https://', 'wss://').replace('http://', 'ws://');
    const wsUrl = `${baseUrl}/ws/exam/${examId}/${studentId}?token=${token}`;

    console.log('Connecting to WebSocket:', wsUrl);
    setConnectionStatus('connecting');
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket connected');
      setConnectionStatus('connected');
      setSocket(ws);
      
      // Send join exam message
      ws.send(JSON.stringify({
        type: 'join_exam',
        exam_id: examId,
        student_id: studentId,
        timestamp: new Date().toISOString()
      }));
      
      addMessage('Connected to WebSocket', 'system');
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log('WebSocket message received:', data);
      addMessage(`Received: ${JSON.stringify(data, null, 2)}`, 'received');
    };
    
    ws.onclose = () => {
      console.log('WebSocket disconnected');
      setConnectionStatus('disconnected');
      setSocket(null);
      addMessage('Disconnected from WebSocket', 'system');
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setConnectionStatus('error');
      addMessage(`Error: ${error.message || 'Connection failed'}`, 'error');
    };
  };

  const disconnectWebSocket = () => {
    if (socket) {
      socket.close();
    }
  };

  const sendMessage = () => {
    if (socket && socket.readyState === WebSocket.OPEN && messageInput.trim()) {
      try {
        const message = JSON.parse(messageInput);
        socket.send(JSON.stringify(message));
        addMessage(`Sent: ${JSON.stringify(message, null, 2)}`, 'sent');
        setMessageInput('');
      } catch (error) {
        addMessage(`Invalid JSON: ${error.message}`, 'error');
      }
    }
  };

  const addMessage = (text, type) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      type,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const sendHeartbeat = () => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      const heartbeat = {
        type: 'heartbeat',
        student_id: 'test-student-123',
        timestamp: new Date().toISOString()
      };
      socket.send(JSON.stringify(heartbeat));
      addMessage(`Sent heartbeat: ${JSON.stringify(heartbeat, null, 2)}`, 'sent');
    }
  };

  const sendTestAnswer = () => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      const answer = {
        type: 'save_answer',
        question_id: 'test-question-123',
        answer: 'Test Answer',
        timestamp: new Date().toISOString()
      };
      socket.send(JSON.stringify(answer));
      addMessage(`Sent answer: ${JSON.stringify(answer, null, 2)}`, 'sent');
    }
  };

  useEffect(() => {
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, [socket]);

  return (
    <div className={`min-h-screen ${themeBg} ${themeText} p-6`}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">WebSocket Test Page</h1>
        
        {/* Connection Status */}
        <div className={`${cardBg} rounded-lg p-4 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' : 
                connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 
                connectionStatus === 'error' ? 'bg-red-500' : 
                'bg-gray-500'
              }`}></div>
              <span className="font-medium">Status: {connectionStatus}</span>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={connectWebSocket}
                disabled={connectionStatus === 'connected' || connectionStatus === 'connecting'}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Connect
              </button>
              <button
                onClick={disconnectWebSocket}
                disabled={connectionStatus !== 'connected'}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                Disconnect
              </button>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`${cardBg} rounded-lg p-4 mb-6`}>
          <h3 className="font-semibold mb-3">Quick Actions</h3>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={sendHeartbeat}
              disabled={connectionStatus !== 'connected'}
              className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              Send Heartbeat
            </button>
            <button
              onClick={sendTestAnswer}
              disabled={connectionStatus !== 'connected'}
              className="px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              Send Test Answer
            </button>
          </div>
        </div>

        {/* Custom Message */}
        <div className={`${cardBg} rounded-lg p-4 mb-6`}>
          <h3 className="font-semibold mb-3">Send Custom Message</h3>
          <div className="flex gap-2">
            <textarea
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder='{"type": "heartbeat", "student_id": "test-123", "timestamp": "2024-01-01T00:00:00Z"}'
              className="flex-1 p-2 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
              rows={3}
            />
            <button
              onClick={sendMessage}
              disabled={connectionStatus !== 'connected' || !messageInput.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              Send
            </button>
          </div>
        </div>

        {/* Messages Log */}
        <div className={`${cardBg} rounded-lg p-4`}>
          <h3 className="font-semibold mb-3">Messages Log</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-2 rounded text-sm ${
                  message.type === 'sent' ? 'bg-blue-100 dark:bg-blue-900' :
                  message.type === 'received' ? 'bg-green-100 dark:bg-green-900' :
                  message.type === 'error' ? 'bg-red-100 dark:bg-red-900' :
                  'bg-gray-100 dark:bg-gray-700'
                }`}
              >
                <div className="flex justify-between items-start">
                  <pre className="whitespace-pre-wrap font-mono text-xs">{message.text}</pre>
                  <span className="text-xs opacity-70 ml-2">{message.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
          {messages.length > 0 && (
            <button
              onClick={() => setMessages([])}
              className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              Clear Log
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default WebSocketTest;
