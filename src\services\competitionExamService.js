/**
 * Competition Exam Service
 * 
 * Handles all competition exam-related API calls using the new dedicated endpoints
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class CompetitionExamService {
  constructor() {
    this.baseUrl = BASE_API;
    this.apiPrefix = '/api/competitions';
  }

  /**
   * Get auth headers for API requests with security headers
   */
  getAuthHeaders() {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication token not found');
    }

    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest', // CSRF protection
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache'
    };
  }

  /**
   * Validate UUID format
   */
  validateUUID(id) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  /**
   * Validate if event is a valid competition with exam
   * GET /api/competitions/{event_id}/exam/validate
   */
  async validateCompetitionExam(eventId) {
    try {
      // SECURITY: Validate event ID format
      if (!eventId || !this.validateUUID(eventId)) {
        throw new Error('Invalid event ID format');
      }

      logger.info('Validating competition exam for event:', eventId);

      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/validate`,
        { headers: this.getAuthHeaders() }
      );

      logger.info('Competition validation response:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to validate competition exam:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get competition exam info
   * GET /api/competitions/{event_id}/exam/info
   */
  async getCompetitionExamInfo(eventId) {
    try {
      // SECURITY: Validate event ID format
      if (!eventId || !this.validateUUID(eventId)) {
        throw new Error('Invalid event ID format');
      }

      logger.info('Fetching competition exam info for event:', eventId);

      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/info`,
        { headers: this.getAuthHeaders() }
      );

      logger.info('Competition exam info:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get competition exam info:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get competition exam status for current student
   * GET /api/competitions/{event_id}/exam/status
   */
  async getCompetitionExamStatus(eventId) {
    try {
      logger.info('Fetching competition exam status for event:', eventId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/status`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Competition exam status:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get competition exam status:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Register for competition exam
   * POST /api/competitions/{event_id}/exam/register
   */
  async registerForCompetitionExam(eventId) {
    try {
      logger.info('Registering for competition exam:', eventId);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/register`,
        {},
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Competition exam registration response:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to register for competition exam:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Start competition exam attempt
   * POST /api/competitions/{event_id}/exam/start
   */
  async startCompetitionExam(eventId) {
    try {
      logger.info('Starting competition exam for event:', eventId);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/start`,
        {},
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Competition exam start response:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to start competition exam:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get competition exam questions
   * GET /api/competitions/{event_id}/exam/questions
   */
  async getCompetitionExamQuestions(eventId) {
    try {
      logger.info('Fetching competition exam questions for event:', eventId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/exam/questions`,
        { headers: this.getAuthHeaders() }
      );
      
      logger.info('Competition exam questions:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get competition exam questions:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get competition results
   * GET /api/competitions/{event_id}/results
   */
  async getCompetitionResults(eventId, limit = 10) {
    try {
      logger.info('Fetching competition results for event:', eventId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/${eventId}/results`,
        { 
          params: { limit },
          headers: this.getAuthHeaders() 
        }
      );
      
      logger.info('Competition results:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get competition results:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Handle API errors consistently
   */
  handleApiError(error) {
    if (error.response) {
      const { status, data } = error.response;
      logger.error(`API Error ${status}:`, data);
      
      if (status === 422) {
        return new Error(data.detail?.[0]?.msg || 'Validation error');
      }
      
      return new Error(data.message || `API Error: ${status}`);
    }
    
    return new Error(error.message || 'Network error');
  }
}

// Export singleton instance
const competitionExamService = new CompetitionExamService();
export default competitionExamService;
