import React, { useRef, useEffect, useCallback } from 'react';
import * as d3 from 'd3';
import { useThemeProvider } from '../../../providers/ThemeContext';

/**
 * Base D3 Chart Component
 * Provides common functionality for all D3 charts including:
 * - Theme-aware styling
 * - Responsive behavior
 * - Common chart setup
 * - Error handling
 */
const BaseD3Chart = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  className = '',
  onRender,
  children,
  ...props
}) => {
  const svgRef = useRef(null);
  const containerRef = useRef(null);
  const { currentTheme } = useThemeProvider();
  const isDark = currentTheme === 'dark';

  // Theme-aware colors
  const colors = {
    primary: isDark ? '#3B82F6' : '#2563EB',
    secondary: isDark ? '#10B981' : '#059669',
    accent: isDark ? '#F59E0B' : '#D97706',
    danger: isDark ? '#EF4444' : '#DC2626',
    warning: isDark ? '#F59E0B' : '#D97706',
    success: isDark ? '#10B981' : '#059669',
    text: isDark ? '#F3F4F6' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    background: isDark ? '#1F2937' : '#FFFFFF',
    border: isDark ? '#374151' : '#E5E7EB',
    grid: isDark ? '#374151' : '#F3F4F6',
  };

  // Chart dimensions
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  // Clear and setup SVG
  const setupSVG = useCallback(() => {
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Set SVG attributes
    svg
      .attr('width', width)
      .attr('height', height)
      .attr('viewBox', `0 0 ${width} ${height}`)
      .style('background', colors.background);

    // Create main group with margins
    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    return { svg, g };
  }, [width, height, margin, colors.background]);

  // Common scales
  const createScales = useCallback((data, config = {}) => {
    const {
      xDomain,
      yDomain,
      xType = 'linear',
      yType = 'linear',
      xRange = [0, innerWidth],
      yRange = [innerHeight, 0]
    } = config;

    let xScale, yScale;

    // X Scale
    switch (xType) {
      case 'band':
        xScale = d3.scaleBand()
          .domain(xDomain || data.map(d => d.x || d.label || d.name))
          .range(xRange)
          .padding(0.1);
        break;
      case 'time':
        xScale = d3.scaleTime()
          .domain(xDomain || d3.extent(data, d => new Date(d.x || d.date)))
          .range(xRange);
        break;
      case 'ordinal':
        xScale = d3.scaleOrdinal()
          .domain(xDomain || data.map(d => d.x || d.label || d.name))
          .range(xRange);
        break;
      default: // linear
        xScale = d3.scaleLinear()
          .domain(xDomain || d3.extent(data, d => d.x || 0))
          .range(xRange);
    }

    // Y Scale
    switch (yType) {
      case 'band':
        yScale = d3.scaleBand()
          .domain(yDomain || data.map(d => d.y || d.value))
          .range(yRange)
          .padding(0.1);
        break;
      case 'time':
        yScale = d3.scaleTime()
          .domain(yDomain || d3.extent(data, d => new Date(d.y || d.date)))
          .range(yRange);
        break;
      default: // linear
        yScale = d3.scaleLinear()
          .domain(yDomain || [0, d3.max(data, d => d.y || d.value || 0)])
          .range(yRange);
    }

    return { xScale, yScale };
  }, [innerWidth, innerHeight]);

  // Common axes
  const createAxes = useCallback((g, xScale, yScale, config = {}) => {
    const {
      showXAxis = true,
      showYAxis = true,
      xAxisLabel = '',
      yAxisLabel = '',
      xTickFormat,
      yTickFormat,
      xTicks,
      yTicks
    } = config;

    if (showXAxis) {
      const xAxis = d3.axisBottom(xScale);
      if (xTickFormat) xAxis.tickFormat(xTickFormat);
      if (xTicks) xAxis.ticks(xTicks);

      const xAxisGroup = g.append('g')
        .attr('class', 'x-axis')
        .attr('transform', `translate(0,${innerHeight})`)
        .call(xAxis);

      xAxisGroup.selectAll('text')
        .style('fill', colors.text)
        .style('font-size', '12px');

      xAxisGroup.selectAll('line, path')
        .style('stroke', colors.border);

      if (xAxisLabel) {
        xAxisGroup.append('text')
          .attr('x', innerWidth / 2)
          .attr('y', 35)
          .style('text-anchor', 'middle')
          .style('fill', colors.text)
          .style('font-size', '14px')
          .text(xAxisLabel);
      }
    }

    if (showYAxis) {
      const yAxis = d3.axisLeft(yScale);
      if (yTickFormat) yAxis.tickFormat(yTickFormat);
      if (yTicks) yAxis.ticks(yTicks);

      const yAxisGroup = g.append('g')
        .attr('class', 'y-axis')
        .call(yAxis);

      yAxisGroup.selectAll('text')
        .style('fill', colors.text)
        .style('font-size', '12px');

      yAxisGroup.selectAll('line, path')
        .style('stroke', colors.border);

      if (yAxisLabel) {
        yAxisGroup.append('text')
          .attr('transform', 'rotate(-90)')
          .attr('y', -35)
          .attr('x', -innerHeight / 2)
          .style('text-anchor', 'middle')
          .style('fill', colors.text)
          .style('font-size', '14px')
          .text(yAxisLabel);
      }
    }
  }, [innerHeight, innerWidth, colors]);

  // Grid lines
  const createGrid = useCallback((g, xScale, yScale, config = {}) => {
    const { showXGrid = true, showYGrid = true } = config;

    if (showYGrid) {
      g.append('g')
        .attr('class', 'grid')
        .selectAll('line')
        .data(yScale.ticks())
        .enter()
        .append('line')
        .attr('x1', 0)
        .attr('x2', innerWidth)
        .attr('y1', d => yScale(d))
        .attr('y2', d => yScale(d))
        .style('stroke', colors.grid)
        .style('stroke-width', 1)
        .style('opacity', 0.3);
    }

    if (showXGrid && xScale.ticks) {
      g.append('g')
        .attr('class', 'grid')
        .selectAll('line')
        .data(xScale.ticks())
        .enter()
        .append('line')
        .attr('x1', d => xScale(d))
        .attr('x2', d => xScale(d))
        .attr('y1', 0)
        .attr('y2', innerHeight)
        .style('stroke', colors.grid)
        .style('stroke-width', 1)
        .style('opacity', 0.3);
    }
  }, [innerWidth, innerHeight, colors.grid]);

  // Tooltip
  const createTooltip = useCallback(() => {
    // Remove existing tooltip
    d3.select('body').selectAll('.d3-tooltip').remove();

    const tooltip = d3.select('body')
      .append('div')
      .attr('class', 'd3-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', colors.background)
      .style('border', `1px solid ${colors.border}`)
      .style('border-radius', '6px')
      .style('padding', '8px 12px')
      .style('font-size', '12px')
      .style('color', colors.text)
      .style('box-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.1)')
      .style('z-index', '1000')
      .style('pointer-events', 'none');

    return tooltip;
  }, [colors]);

  // Render chart
  useEffect(() => {
    if (!data || data.length === 0) return;

    try {
      const { svg, g } = setupSVG();
      
      if (onRender) {
        onRender({
          svg,
          g,
          data,
          colors,
          createScales,
          createAxes,
          createGrid,
          createTooltip,
          innerWidth,
          innerHeight,
          margin
        });
      }
    } catch (error) {
      console.error('Error rendering D3 chart:', error);
    }
  }, [data, setupSVG, onRender, colors, createScales, createAxes, createGrid, createTooltip, innerWidth, innerHeight, margin]);

  return (
    <div 
      ref={containerRef}
      className={`d3-chart-container ${className}`}
      {...props}
    >
      <svg ref={svgRef} />
      {children}
    </div>
  );
};

export default BaseD3Chart;
