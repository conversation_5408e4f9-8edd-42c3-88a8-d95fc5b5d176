/**
 * Exam Attempt Manager
 * Manages the complete exam attempt flow from start screen to completion
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { FiAlertTriangle, FiCheckCircle } from 'react-icons/fi';

// Import components
import ExamStartScreen from './ExamStartScreen';
import ExamInterface from './ExamInterface/ExamInterface';
import ExamLoadingState from './ExamLoadingState';

// Import services
import ExamSessionManager from '../../../services/exam/session/ExamSessionManager';
import AntiCheatService from '../../../services/exam/security/AntiCheatService';

// Import Redux actions
import {
  setSessionStatus,
  setSessionId,
  setRemainingTime,
  setConnectionStatus
} from '../../../store/slices/exam/examSessionSlice';

const ExamAttemptManager = ({
  examId,
  examData = null,
  competitionMode = false,
  competitionEvent = null,
  onBackToExams = null
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [currentPhase, setCurrentPhase] = useState('start'); // 'start', 'loading', 'active', 'error'
  const [error, setError] = useState(null);
  const [sessionData, setSessionData] = useState(null);
  const [isStarting, setIsStarting] = useState(false);

  // Redux state
  const examSession = useSelector(state => state.examSession);

  /**
   * Handle exam start - transition from start screen to exam room
   */
  const handleStartExam = useCallback(async () => {
    try {
      setIsStarting(true);
      setCurrentPhase('loading');
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('🎯 Starting exam session for exam:', examId);

      // Step 1: Request session ID
      console.log('📡 Step 1: Requesting session ID...');
      console.log('📡 URL:', `http://127.0.0.1:8000/exam-session/request/${examId}`);
      console.log('📡 Token:', token ? 'Present' : 'Missing');

      const sessionResponse = await fetch(`http://127.0.0.1:8000/exam-session/request/${examId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📡 Session Response Status:', sessionResponse.status);
      console.log('📡 Session Response OK:', sessionResponse.ok);

      if (!sessionResponse.ok) {
        const errorText = await sessionResponse.text();
        console.error('📡 Session Error Response:', errorText);
        let error;
        try {
          error = JSON.parse(errorText);
        } catch (e) {
          error = { message: errorText };
        }
        throw new Error(error.detail || error.message || `HTTP ${sessionResponse.status}: ${errorText}`);
      }

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.session_id;

      if (!sessionId) {
        throw new Error('No session ID received from backend');
      }

      console.log('✅ Step 1 Complete: Got session ID:', sessionId);

      // Step 2: Get exam data using session ID
      console.log('📚 Step 2: Getting exam data...');
      console.log('📚 URL:', `http://127.0.0.1:8000/attempt/${sessionId}`);

      const examResponse = await fetch(`http://127.0.0.1:8000/attempt/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📚 Exam Response Status:', examResponse.status);
      console.log('📚 Exam Response OK:', examResponse.ok);

      if (!examResponse.ok) {
        const errorText = await examResponse.text();
        console.error('📚 Exam Error Response:', errorText);
        let error;
        try {
          error = JSON.parse(errorText);
        } catch (e) {
          error = { message: errorText };
        }
        throw new Error(error.detail || error.message || `HTTP ${examResponse.status}: ${errorText}`);
      }

      const examData = await examResponse.json();
      console.log('✅ Step 2 Complete: Got exam data:', examData);

      // Update Redux state
      dispatch(setSessionId(sessionId));
      dispatch(setSessionStatus('active'));
      dispatch(setConnectionStatus('connected'));

      // Set remaining time if available
      if (examData.remaining_time_seconds) {
        dispatch(setRemainingTime(examData.remaining_time_seconds));
      }

      // Store session data locally
      const sessionKey = `exam_session_${examId}`;
      localStorage.setItem(sessionKey, JSON.stringify({
        session_id: sessionId,
        exam_id: examId,
        created_at: new Date().toISOString(),
        status: 'active'
      }));

      // Store session data for the interface
      const combinedData = {
        session_id: sessionId,
        exam_id: examId,
        examData: examData,
        sessionStatus: 'active',
        currentAnswers: {}
      };
      setSessionData(combinedData);

      console.log('🎉 Exam setup complete, transitioning to active phase');
      // Transition to active exam phase
      setCurrentPhase('active');

    } catch (error) {
      console.error('❌ Failed to start exam:', error);
      setError(error.message);
      setCurrentPhase('error');
      dispatch(setConnectionStatus('error'));
    } finally {
      setIsStarting(false);
    }
  }, [examId, competitionMode, competitionEvent, dispatch]);

  /**
   * Handle exam submission
   */
  const handleExamSubmit = useCallback(async (isAutoSubmit = false) => {
    try {
      console.log('📤 Submitting exam...', { isAutoSubmit });
      
      // The ExamInterface component will handle the actual submission
      // This is just a callback for any additional logic needed
      
      return true;
    } catch (error) {
      console.error('❌ Error during exam submission:', error);
      throw error;
    }
  }, []);

  /**
   * Handle answer changes
   */
  const handleAnswerChange = useCallback((questionId, answer) => {
    // The ExamInterface component will handle answer management
    // This is just a pass-through for any additional logic needed
    console.log('📝 Answer changed:', { questionId, answer });
  }, []);

  /**
   * Handle back to exams
   */
  const handleBackToExams = useCallback(() => {
    // Clean up any active sessions
    if (examSession.sessionId) {
      AntiCheatService.deactivate();
    }
    
    if (onBackToExams) {
      onBackToExams();
    } else {
      navigate('/student/exams');
    }
  }, [examSession.sessionId, onBackToExams, navigate]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      // Cleanup anti-cheat service if component unmounts
      if (AntiCheatService.isServiceActive && AntiCheatService.isServiceActive()) {
        AntiCheatService.deactivate();
      }
    };
  }, []);

  // Render based on current phase
  switch (currentPhase) {
    case 'start':
      return (
        <ExamStartScreen
          exam={examData} // This may be null initially - ExamStartScreen should handle this
          onStartExam={handleStartExam}
          onBackToExams={handleBackToExams}
          isStarting={isStarting}
        />
      );

    case 'loading':
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Preparing Exam Environment</h2>
            <p className="text-gray-600 mb-4">Setting up your exam session...</p>
            <div className="text-sm text-gray-500">
              <p>• Requesting session ID</p>
              <p>• Loading exam data</p>
              <p>• Preparing interface</p>
            </div>
            <button
              onClick={handleBackToExams}
              className="mt-4 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      );

    case 'active':
      if (!sessionData) {
        return (
          <ExamLoadingState
            isLoading={true}
            error={null}
            examId={examId}
            onRetry={handleStartExam}
            onBackToExams={handleBackToExams}
          />
        );
      }

      return (
        <ExamInterface
          exam={sessionData.examData}
          onSubmit={handleExamSubmit}
          onAnswerChange={handleAnswerChange}
          competitionMode={competitionMode}
          competitionEvent={competitionEvent}
        />
      );

    case 'error':
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center max-w-md">
            <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Start Exam</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex space-x-3 justify-center">
              <button
                onClick={handleStartExam}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Try Again
              </button>
              <button
                onClick={handleBackToExams}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Back to Exams
              </button>
            </div>
          </div>
        </div>
      );

    default:
      return (
        <ExamLoadingState
          isLoading={true}
          error={null}
          examId={examId}
          onRetry={handleStartExam}
          onBackToExams={handleBackToExams}
        />
      );
  }
};

export default ExamAttemptManager;
