/* Mobile UX Optimizations */

/* Touch-friendly button sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Improved touch manipulation */
.touch-manipulation {
  touch-action: manipulation;
}

/* Better tap highlights */
.tap-highlight-none {
  -webkit-tap-highlight-color: transparent;
}

/* Mobile-optimized form inputs */
.form-input-mobile {
  min-height: 44px;
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Mobile-optimized select elements */
.form-select-mobile {
  min-height: 44px;
  font-size: 16px;
}

/* Responsive text scaling */
@media (max-width: 640px) {
  .text-responsive-sm {
    font-size: 0.875rem;
  }
  
  .text-responsive-base {
    font-size: 1rem;
  }
  
  .text-responsive-lg {
    font-size: 1.125rem;
  }
  
  .text-responsive-xl {
    font-size: 1.25rem;
  }
  
  .text-responsive-2xl {
    font-size: 1.5rem;
  }
}

/* Mobile-optimized spacing */
@media (max-width: 640px) {
  .mobile-p-3 {
    padding: 0.75rem;
  }
  
  .mobile-p-4 {
    padding: 1rem;
  }
  
  .mobile-gap-2 {
    gap: 0.5rem;
  }
  
  .mobile-gap-3 {
    gap: 0.75rem;
  }
}

/* Improved mobile scrolling */
.mobile-scroll-smooth {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Mobile-optimized cards */
.mobile-card {
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@media (max-width: 640px) {
  .mobile-card {
    border-radius: 0.5rem;
    margin: 0.5rem;
  }
}

/* Mobile-optimized buttons */
.btn-mobile {
  min-height: 44px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Sidebar mobile optimizations */
.sidebar-mobile {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.sidebar-item-mobile {
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Improved mobile sidebar backdrop */
.sidebar-backdrop-mobile {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Mobile sidebar animations */
@media (max-width: 1023px) {
  .sidebar-mobile-enter {
    transform: translateX(-100%);
  }

  .sidebar-mobile-enter-active {
    transform: translateX(0);
    transition: transform 300ms ease-out;
  }

  .sidebar-mobile-exit {
    transform: translateX(0);
  }

  .sidebar-mobile-exit-active {
    transform: translateX(-100%);
    transition: transform 300ms ease-in;
  }
}

/* Responsive sidebar utilities - only apply to mobile, not desktop */
@media (max-width: 640px) {
  .sidebar-mobile {
    width: 280px !important; /* Slightly wider on small screens */
  }
}

@media (max-width: 480px) {
  .sidebar-mobile {
    width: 100vw !important; /* Full width on very small screens */
    max-width: 320px !important;
  }
}

/* Improved touch targets for mobile only */
@media (max-width: 1023px) {
  .sidebar-item-mobile {
    min-height: 48px; /* Larger touch targets on mobile */
    padding: 0.875rem 1rem;
  }
}

/* Landscape mobile optimizations - only for mobile */
@media (max-width: 1023px) and (orientation: landscape) {
  .sidebar-mobile {
    width: 240px !important; /* Narrower in landscape to save space */
  }
}

/* Mobile-optimized modal */
@media (max-width: 640px) {
  .modal-mobile {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Mobile-optimized table alternatives */
.mobile-table-card {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  padding: 1rem;
}

@media (min-width: 640px) {
  .mobile-table-card {
    display: none;
  }
}

/* Hide desktop table on mobile */
@media (max-width: 639px) {
  .desktop-table {
    display: none;
  }
}

/* Mobile-optimized navigation */
.mobile-nav-item {
  min-height: 48px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  touch-action: manipulation;
}

/* Improved focus states for mobile */
.mobile-focus:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile-optimized dropdown */
.mobile-dropdown {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 1rem 1rem 0 0;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  max-height: 70vh;
  overflow-y: auto;
}

/* Safe area handling for mobile devices */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* Mobile-optimized loading states */
.mobile-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile-optimized error states */
.mobile-error-card {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

/* Mobile-optimized success states */
.mobile-success-card {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

/* Utility classes for mobile-first responsive design */
.mobile-hidden {
  display: none;
}

@media (min-width: 640px) {
  .mobile-hidden {
    display: block;
  }
}

.desktop-hidden {
  display: block;
}

@media (min-width: 640px) {
  .desktop-hidden {
    display: none;
  }
}

/* Mobile-optimized animations */
@media (prefers-reduced-motion: reduce) {
  .mobile-animation {
    animation: none;
    transition: none;
  }
}

/* Mobile-optimized text selection */
.mobile-text-select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.mobile-text-no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
