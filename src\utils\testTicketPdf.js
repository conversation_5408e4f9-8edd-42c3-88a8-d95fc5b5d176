import { downloadTicketPDF } from './ticketPdfGenerator';

/**
 * Test function to generate a sample ticket PDF
 * This can be used for testing the PDF generation functionality
 */
export const testTicketPDFGeneration = () => {
  // Sample ticket data that matches the actual backend response format
  const sampleTicketData = {
    registration: {
      id: "a40ff346-9090-4963-865f-f3789de1028c",
      registration_number: "REG-8233C7AE-VNWNOQ",
      status: "CONFIRMED",
      quantity: 1,
      total_amount: 10.0,
      currency: "PKR",
      registered_at: "2025-09-10T17:39:15.437182+00:00",
      confirmed_at: "2025-09-10T17:39:15.435214+00:00",
      check_in_code: null
    },
    event: {
      id: "8233c7ae-4e5d-49a0-b769-d5feff169000",
      title: "ExamTest",
      description: "ExamTest",
      start_datetime: "2025-09-10T20:09:00+00:00",
      end_datetime: "2025-09-10T21:09:00+00:00",
      location: "OnWebsite",
      category: "COMPETITION"
    },
    ticket: {
      id: "7e14df4f-e0e5-4fb4-acb1-793aad54b40c",
      name: "General Admission",
      description: null,
      price: 10.0,
      currency: "ZAR"
    },
    user: {
      id: "bcc8b96a-5b05-4187-914e-4a7820cc831d",
      username: "Student User",
      email: "<EMAIL>",
      display_name: "Student User"
    },
    check_in_code: null,
    generated_at: "2025-09-11T13:04:39.004039+00:00"
  };

  console.log('Testing ticket PDF generation with sample data...');
  
  try {
    const result = downloadTicketPDF(sampleTicketData, 'Test_Ticket_Sample.pdf');
    
    if (result.success) {
      console.log('✅ Test PDF generated successfully:', result.filename);
      return { success: true, message: 'Test PDF generated successfully' };
    } else {
      console.error('❌ Test PDF generation failed:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('❌ Test PDF generation error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test function for the Welcome Event ticket
 */
export const testWelcomeEventTicket = () => {
  const welcomeEventData = {
    registration: {
      id: "1120aa69-f58x-jq12-3456-789012345678",
      registration_number: "REG-1120AA69-F58XJQ",
      status: "CONFIRMED",
      quantity: 1,
      total_amount: 100.0,
      currency: "PKR",
      registered_at: "2025-09-06T21:01:00+00:00",
      confirmed_at: "2025-09-06T21:01:00+00:00",
      check_in_code: "WELCOME2025"
    },
    event: {
      id: "welcome-event-456",
      title: "Welcome Event",
      description: "Welcome Event for new students",
      start_datetime: "2025-09-06T21:01:00+00:00",
      end_datetime: "2025-09-06T23:01:00+00:00",
      location: "H12",
      category: "WELCOME"
    },
    ticket: {
      id: "welcome-ticket-789",
      name: "General Admission",
      description: "Basic Entry, Food",
      price: 100.0,
      currency: "PKR"
    },
    user: {
      id: "student-user-123",
      username: "Student User",
      email: "<EMAIL>",
      display_name: "Student User"
    },
    check_in_code: "WELCOME2025",
    generated_at: "2025-09-06T21:01:00+00:00"
  };

  console.log('Testing Welcome Event ticket PDF generation...');
  
  try {
    const result = downloadTicketPDF(welcomeEventData, 'Welcome_Event_Test_Ticket.pdf');
    
    if (result.success) {
      console.log('✅ Welcome Event test PDF generated successfully:', result.filename);
      return { success: true, message: 'Welcome Event test PDF generated successfully' };
    } else {
      console.error('❌ Welcome Event test PDF generation failed:', result.error);
      return { success: false, error: result.error };
    }
  } catch (error) {
    console.error('❌ Welcome Event test PDF generation error:', error);
    return { success: false, error: error.message };
  }
};

// Make test functions available globally for browser console testing
if (typeof window !== 'undefined') {
  window.testTicketPDF = testTicketPDFGeneration;
  window.testWelcomeEventTicket = testWelcomeEventTicket;
  console.log('🎫 Ticket PDF test functions available:');
  console.log('- window.testTicketPDF() - Test ExamTest ticket');
  console.log('- window.testWelcomeEventTicket() - Test Welcome Event ticket');
}
