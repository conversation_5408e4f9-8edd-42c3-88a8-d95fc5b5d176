import React from 'react';
import { FiClock, FiCalendar, FiBookOpen, FiTarget, FiHome, FiGlobe } from 'react-icons/fi';
import { formatDateTimeSync } from '../../utils/timezone';
import useTimezone from '../../hooks/useTimezone';

/**
 * Institute-specific enhanced exam details component
 * Displays comprehensive exam information without assignment details
 */
const InstituteEnhancedExamDetails = ({ exam, className = "" }) => {
  const { timezoneData, formatDateTime: formatDateTimeWithLocation, loading: timezoneLoading } = useTimezone();

  if (!exam) {
    return (
      <div className={`p-6 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No exam data available</p>
      </div>
    );
  }

  const formatDateTime = (dateString, showLocation = false) => {
    if (!dateString) return 'Not set';
    try {
      // Use the timezone hook's formatDateTime for location-aware formatting
      if (timezoneData && !timezoneLoading) {
        return formatDateTimeWithLocation(dateString, showLocation);
      }
      // Fallback to sync formatting
      return formatDateTimeSync(dateString);
    } catch (error) {
      console.warn('Error formatting datetime:', error);
      return new Date(dateString).toLocaleString();
    }
  };

  const formatDuration = (minutes) => {
    if (!minutes) return 'Not set';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {exam.title}
            </h2>
            {exam.description && (
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                {exam.description}
              </p>
            )}
          </div>
          {exam.class_number && (
            <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
              <FiHome className="w-4 h-4" />
              Class {exam.class_number}
            </div>
          )}
        </div>
      </div>

      {/* Stats Grid - Institute focused (no student counts) */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Total Questions */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <FiBookOpen className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Questions</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {exam.total_questions || exam.questions?.length || 0}
              </p>
            </div>
          </div>

          {/* Total Marks */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <FiTarget className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Marks</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {exam.total_marks || 0}
              </p>
            </div>
          </div>

          {/* Duration */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <FiClock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Duration</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {formatDuration(exam.total_duration)}
              </p>
            </div>
          </div>
        </div>

        {/* Schedule Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <FiCalendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Start Time</span>
            </div>
            <p className="text-gray-900 dark:text-gray-100 font-medium">
              {formatDateTime(exam.start_time, true)}
            </p>
            {timezoneData && timezoneData.detected && (
              <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 flex items-center gap-1">
                <FiGlobe className="w-3 h-3" />
                <span>Your local time</span>
              </div>
            )}
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <FiCalendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">End Time</span>
            </div>
            <p className="text-gray-900 dark:text-gray-100 font-medium">
              {formatDateTime(exam.end_time, true)}
            </p>
            {timezoneData && timezoneData.detected && (
              <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 flex items-center gap-1">
                <FiGlobe className="w-3 h-3" />
                <span>Your local time</span>
              </div>
            )}
          </div>
        </div>

        {/* Timezone Information */}
        {timezoneData && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <FiGlobe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                Timezone Information
              </h3>
            </div>
            <div className="space-y-2 text-sm">
              {timezoneLoading ? (
                <p className="text-blue-700 dark:text-blue-300">🌍 Detecting your location...</p>
              ) : timezoneData.detected ? (
                <>
                  <p className="text-blue-700 dark:text-blue-300">
                    <strong>Your Location:</strong> {timezoneData.city}, {timezoneData.country}
                  </p>
                  <p className="text-blue-700 dark:text-blue-300">
                    <strong>Timezone:</strong> {timezoneData.timezone}
                  </p>
                  <p className="text-blue-600 dark:text-blue-400 text-xs">
                    💡 Times shown above are converted to your local timezone.
                  </p>
                </>
              ) : (
                <>
                  <p className="text-blue-700 dark:text-blue-300">
                    <strong>Timezone:</strong> {timezoneData.timezone} (Browser detected)
                  </p>
                  <p className="text-blue-600 dark:text-blue-400 text-xs">
                    💡 Using browser timezone. Times are converted for display.
                  </p>
                </>
              )}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <div className="text-sm">
            <span className="text-gray-600 dark:text-gray-400">Created: </span>
            <span className="text-gray-900 dark:text-gray-100">
              {formatDateTime(exam.created_at, true)}
            </span>
          </div>
          <div className="text-sm">
            <span className="text-gray-600 dark:text-gray-400">Last Updated: </span>
            <span className="text-gray-900 dark:text-gray-100">
              {formatDateTime(exam.updated_at, true)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstituteEnhancedExamDetails;
