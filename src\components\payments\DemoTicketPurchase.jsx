import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import demoPaymentService from '../../services/mvpPaymentService';
import { LoadingSpinner } from '../ui';
import {
  FiShoppingCart,
  FiCheckCircle,
  FiXCircle,
  FiArrowLeft,
  FiInfo,
  FiGift
} from 'react-icons/fi';

const DemoTicketPurchase = ({ 
  eventId, 
  ticketId, 
  ticketData, 
  onSuccess, 
  onCancel, 
  onError 
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [purchaseStatus, setPurchaseStatus] = useState(null);
  const [error, setError] = useState(null);
  const [purchaseResult, setPurchaseResult] = useState(null);

  const handleDemoPurchase = async () => {
    setLoading(true);
    setError(null);
    
    try {
      demoPaymentService.logDemoEvent(ticketId, 'purchase_attempt', {
        event_id: eventId,
        ticket_data: ticketData
      });

      const result = await demoPaymentService.purchaseTicket(ticketId);
      
      setPurchaseResult(result);
      setPurchaseStatus('success');
      
      demoPaymentService.logDemoEvent(ticketId, 'purchase_success', result);
      
      if (onSuccess) onSuccess(result);
      
    } catch (err) {
      setError(err.message);
      setPurchaseStatus('failed');
      
      demoPaymentService.logDemoEvent(ticketId, 'purchase_error', { error: err.message });
      
      if (onError) onError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    demoPaymentService.logDemoEvent(ticketId, 'purchase_cancel');
    if (onCancel) onCancel();
    navigate(-1);
  };

  // Success state
  if (purchaseStatus === 'success') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center">
        <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
          <FiCheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Ticket Purchased!</h2>
        <p className="text-gray-600 mb-4">
          Your demo ticket has been confirmed successfully.
        </p>
        
        {purchaseResult && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6 text-left">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Ticket:</span>
                <span className="font-semibold">{purchaseResult.ticket_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-semibold">R{purchaseResult.amount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Confirmation:</span>
                <span className="font-mono text-xs bg-green-100 px-2 py-1 rounded">
                  {purchaseResult.confirmation_code}
                </span>
              </div>
            </div>
          </div>
        )}
        
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
          <div className="flex items-start">
            <FiInfo className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
            <div className="text-sm text-blue-800">
              <p className="font-semibold mb-1">Demo Mode</p>
              <p>This is a demo purchase. No real payment was processed.</p>
            </div>
          </div>
        </div>

        <button
          onClick={() => navigate('/student/events')}
          className="w-full px-6 py-3 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200"
        >
          Back to Events
        </button>
      </div>
    );
  }

  // Failed state
  if (purchaseStatus === 'failed') {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center">
        <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
          <FiXCircle className="h-8 w-8 text-red-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Purchase Failed</h2>
        <p className="text-gray-600 mb-6">
          {error || 'Your demo purchase could not be processed. Please try again.'}
        </p>
        <div className="space-y-3">
          <button
            onClick={handleDemoPurchase}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
          >
            Try Again
          </button>
          <button
            onClick={handleCancel}
            className="w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  // Purchase form
  return (
    <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Demo Purchase</h2>
          <FiGift className="h-6 w-6" />
        </div>
        <p className="text-green-100">
          Complete your demo ticket purchase instantly
        </p>
      </div>

      {/* Purchase Details */}
      <div className="p-6">
        <div className="space-y-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Ticket Type:</span>
            <span className="font-semibold">{ticketData.name}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Quantity:</span>
            <span className="font-semibold">{ticketData.quantity || 1}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Price per ticket:</span>
            <span className="font-semibold">R{ticketData.price}</span>
          </div>
          <hr className="border-gray-200" />
          <div className="flex justify-between items-center text-lg">
            <span className="font-semibold">Total Amount:</span>
            <span className="font-bold text-green-600">R{ticketData.total_amount}</span>
          </div>
        </div>

        {/* Demo Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
          <div className="flex items-start">
            <FiInfo className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
            <div className="text-sm text-yellow-800">
              <p className="font-semibold mb-1">Demo Mode</p>
              <p>This is a demonstration. No real payment will be processed and the ticket will be confirmed instantly.</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleDemoPurchase}
            disabled={loading}
            className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <FiShoppingCart className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Processing...' : 'Get Demo Ticket'}
          </button>
          
          <button
            onClick={handleCancel}
            disabled={loading}
            className="w-full flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50"
          >
            <FiArrowLeft className="h-5 w-5 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default DemoTicketPurchase;
