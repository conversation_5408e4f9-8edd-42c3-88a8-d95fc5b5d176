import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiLoader } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import socialFollowService from '../../services/socialFollowService';
import FollowButton from './FollowButton';
import { MessageButton } from '../chat';

/**
 * UserSocialStats Component
 * Displays social statistics and follow information for a user profile
 */
const UserSocialStats = ({
  userId,
  showFollowButton = true,
  showMessageButton = true,
  showMutualFollowers = true,
  layout = 'horizontal', // 'horizontal' or 'vertical'
  className = ''
}) => {
  const [stats, setStats] = useState(null);
  const [mutualFollowers, setMutualFollowers] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const currentUserId = localStorage.getItem('userId');
  const isOwnProfile = userId === currentUserId;

  useEffect(() => {
    if (userId) {
      loadSocialData();
    }
  }, [userId]);

  const loadSocialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load stats for all users
      const statsData = await socialFollowService.getFollowStats(userId);
      setStats(statsData);

      // Load mutual followers only for other users
      if (!isOwnProfile && showMutualFollowers) {
        try {
          const mutualData = await socialFollowService.getMutualFollowers(userId, 1, 5);

          // Handle different response structures for mutual followers
          let mutualFollowersData = null;
          if (mutualData && Array.isArray(mutualData.users)) {
            mutualFollowersData = mutualData;
          } else if (Array.isArray(mutualData)) {
            mutualFollowersData = { users: mutualData, total_count: mutualData.length };
          } else if (mutualData && Array.isArray(mutualData.data)) {
            mutualFollowersData = { users: mutualData.data, total_count: mutualData.data.length };
          }

          setMutualFollowers(mutualFollowersData);
        } catch (mutualError) {
          console.warn('Failed to load mutual followers:', mutualError);
          // Don't fail the whole component for mutual followers
        }
      }
    } catch (err) {
      console.error('Failed to load social data:', err);
      setError(err.message || 'Failed to load social information');
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing, targetUserId) => {
    // Update stats optimistically
    if (stats) {
      setStats(prev => ({
        ...prev,
        followers_count: isFollowing 
          ? prev.followers_count + 1 
          : prev.followers_count - 1
      }));
    }
  };

  const navigateToFollowers = () => {
    navigate(`/social/users/${userId}/followers`);
  };

  const navigateToFollowing = () => {
    navigate(`/social/users/${userId}/following`);
  };

  const navigateToMutual = () => {
    navigate(`/social/users/${userId}/mutual`);
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-6 ${className}`}>
        <FiLoader className="w-6 h-6 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-500">Loading social information...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 dark:bg-red-900/20 rounded-lg ${className}`}>
        <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const layoutClasses = layout === 'vertical' 
    ? 'flex flex-col space-y-4' 
    : 'flex flex-wrap items-center gap-6';

  return (
    <div className={`${layoutClasses} ${className}`}>
      {/* Social Stats */}
      <div className="flex items-center gap-6">
        {/* Followers */}
        <button
          onClick={navigateToFollowers}
          className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        >
          <FiUsers className="w-5 h-5" />
          <div className="text-left">
            <div className="font-semibold text-lg">{stats.followers_count || 0}</div>
            <div className="text-sm text-gray-500">
              {stats.followers_count === 1 ? 'Follower' : 'Followers'}
            </div>
          </div>
        </button>

        {/* Following */}
        <button
          onClick={navigateToFollowing}
          className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
        >
          <FiUserCheck className="w-5 h-5" />
          <div className="text-left">
            <div className="font-semibold text-lg">{stats.following_count || 0}</div>
            <div className="text-sm text-gray-500">Following</div>
          </div>
        </button>

        {/* Mutual Followers (only for other users) */}
        {!isOwnProfile && showMutualFollowers && stats.mutual_follows_count > 0 && (
          <button
            onClick={navigateToMutual}
            className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
          >
            <FiHeart className="w-5 h-5" />
            <div className="text-left">
              <div className="font-semibold text-lg">{stats.mutual_follows_count}</div>
              <div className="text-sm text-gray-500">Mutual</div>
            </div>
          </button>
        )}
      </div>

      {/* Mutual Followers Preview (only for other users) */}
      {!isOwnProfile && showMutualFollowers && mutualFollowers && mutualFollowers.users && mutualFollowers.users.length > 0 && (
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-600 dark:text-gray-400">Followed by:</span>
          <div className="flex items-center gap-2">
            {mutualFollowers.users.slice(0, 3).map((user, index) => (
              <div
                key={user.id}
                className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-xs font-semibold"
                title={user.username}
              >
                {user.username?.charAt(0)?.toUpperCase() || '?'}
              </div>
            ))}
            {mutualFollowers.total_count > 3 && (
              <button
                onClick={navigateToMutual}
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                +{mutualFollowers.total_count - 3} more
              </button>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons (only for other users) */}
      {!isOwnProfile && (showFollowButton || showMessageButton) && (
        <div className="flex-shrink-0 flex items-center gap-2">
          {showMessageButton && (
            <MessageButton
              userId={userId}
              size="medium"
              variant="secondary"
            />
          )}
          {showFollowButton && (
            <FollowButton
              userId={userId}
              onFollowChange={handleFollowChange}
              size="medium"
              variant="primary"
            />
          )}
        </div>
      )}
    </div>
  );
};

export default UserSocialStats;
