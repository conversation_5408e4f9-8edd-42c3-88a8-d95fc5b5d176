/**
 * Analytics Summary Component
 * 
 * Displays comprehensive analytics and personalized recommendations including:
 * - Academic standing and performance score
 * - Strength and improvement areas
 * - Personalized recommendations
 * - Upcoming competitions
 */

import React from 'react';
import {
  FiTrendingUp,
  FiTarget,
  FiAward,
  FiInfo,
  FiArrowRight
} from 'react-icons/fi';
import { Card } from '../ui/layout';

const AnalyticsSummary = ({ 
  analyticsSummary, 
  recommendations = [], 
  upcomingCompetitions = [],
  loading = false 
}) => {
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (!analyticsSummary && !recommendations.length && !upcomingCompetitions.length) {
    return null;
  }

  const getStandingColor = (standing) => {
    switch (standing) {
      case 'excellent':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'good':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'average':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'below_average':
        return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30';
      case 'poor':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'improving':
        return 'text-green-600 dark:text-green-400';
      case 'declining':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
        Analytics & Insights
      </h3>

      {/* Analytics Summary */}
      {analyticsSummary && (
        <div className="mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Performance Score */}
            {analyticsSummary.overall_performance_score !== undefined && (
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                  {analyticsSummary.overall_performance_score.toFixed(1)}
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  Performance Score
                </div>
              </div>
            )}

            {/* Academic Standing */}
            {analyticsSummary.academic_standing && (
              <div className={`text-center p-4 rounded-lg ${getStandingColor(analyticsSummary.academic_standing)}`}>
                <div className="text-lg font-semibold mb-1 capitalize">
                  {analyticsSummary.academic_standing.replace('_', ' ')}
                </div>
                <div className="text-sm opacity-80">
                  Academic Standing
                </div>
              </div>
            )}

            {/* Recent Trend */}
            {analyticsSummary.recent_trend && (
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className={`text-lg font-semibold mb-1 capitalize ${getTrendColor(analyticsSummary.recent_trend)}`}>
                  {analyticsSummary.recent_trend}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Recent Trend
                </div>
              </div>
            )}
          </div>

          {/* Strength and Improvement Areas */}
          {(analyticsSummary.top_strength_area || analyticsSummary.primary_improvement_area) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {analyticsSummary.top_strength_area && (
                <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <FiTarget className="w-6 h-6 text-green-600 dark:text-green-400" />
                  <div>
                    <div className="font-medium text-green-800 dark:text-green-200">
                      Top Strength
                    </div>
                    <div className="text-sm text-green-600 dark:text-green-400">
                      {analyticsSummary.top_strength_area}
                    </div>
                  </div>
                </div>
              )}

              {analyticsSummary.primary_improvement_area && (
                <div className="flex items-center gap-3 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <FiTrendingUp className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                  <div>
                    <div className="font-medium text-orange-800 dark:text-orange-200">
                      Focus Area
                    </div>
                    <div className="text-sm text-orange-600 dark:text-orange-400">
                      {analyticsSummary.primary_improvement_area}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Personalized Recommendations */}
      {recommendations.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <FiInfo className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              Personalized Recommendations
            </h4>
          </div>
          <div className="space-y-3">
            {recommendations.slice(0, 3).map((recommendation, index) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  {recommendation}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upcoming Competitions */}
      {upcomingCompetitions.length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-4">
            <FiAward className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              Upcoming Competitions
            </h4>
          </div>
          <div className="space-y-3">
            {upcomingCompetitions.slice(0, 2).map((competition, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-purple-200 dark:border-purple-700 rounded-lg hover:border-purple-300 dark:hover:border-purple-600 transition-colors">
                <div className="flex items-center gap-3">
                  <FiAward className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {competition.name || competition.title || 'Competition'}
                    </div>
                    {competition.description && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {competition.description}
                      </div>
                    )}
                  </div>
                </div>
                <FiArrowRight className="w-4 h-4 text-gray-400" />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Last Updated */}
      {analyticsSummary?.analytics_last_updated && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Analytics updated: {new Date(analyticsSummary.analytics_last_updated).toLocaleDateString()}
          </p>
        </div>
      )}
    </Card>
  );
};

export default AnalyticsSummary;
