import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getStudentUpcomingExams, getStudentExam } from "../../store/slices/ExamSlice";
import { fetchCurrentUser } from "../../store/slices/userSlice";
import { useThemeProvider } from "../../providers/ThemeContext";
import useTimezone from "../../hooks/useTimezone";
import { formatExamDateTime, backendTimeToUTC } from "../../utils/timezone";
import {
  FiCalendar,
  FiClock,
  FiBookOpen,
  FiPlay,
  FiEye,
  FiCheckCircle,
  FiAlertCircle,
  FiFilter,
  FiSearch,
  FiRefreshCw
} from "react-icons/fi";

function StudentExams() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const { timezoneData, formatDateTime, loading: timezoneLoading } = useTimezone();
  
  const { exams, loading, error } = useSelector((state) => state.exams);
  const { currentUser } = useSelector((state) => state.users);
  
  const [filter, setFilter] = useState("all"); // all, upcoming, active, completed, missed, disqualified
  const [searchTerm, setSearchTerm] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [loadingExamId, setLoadingExamId] = useState(null); // Track which exam is being loaded

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const inputBg = currentTheme === "dark" ? "bg-gray-700 border-gray-600" : "bg-white border-gray-300";

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      dispatch(getStudentUpcomingExams());
    }
  }, [dispatch, currentUser]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(getStudentUpcomingExams()).unwrap();
    } catch (error) {
      console.error('Failed to refresh exams:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getExamStatus = (exam) => {
    // Always calculate client-side status for time-sensitive decisions
    const now = new Date();
    let clientStatus = "upcoming";

    if (exam.start_time) {
      // Backend sends UTC time, so convert it properly
      const startTime = backendTimeToUTC(exam.start_time);
      const endTime = new Date(startTime.getTime() + exam.total_duration * 60000);



      if (exam.completed) {
        clientStatus = "completed";
      } else if (now > endTime) {
        clientStatus = "missed";
      } else if (now >= startTime && now <= endTime) {
        clientStatus = "active";
      } else {
        clientStatus = "upcoming";
      }
    }

    // For certain backend statuses, override client calculation
    if (exam.status) {
      const finalStatusMap = {
        'submitted': 'completed',
        'disqualified': 'disqualified',
        'started': 'active', // Student has already begun the exam
      };

      // Use backend status for these final states, otherwise use client calculation
      if (finalStatusMap[exam.status]) {
        return finalStatusMap[exam.status];
      }
    }

    return clientStatus;


  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "upcoming": return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case "completed": return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      case "missed": return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "disqualified": return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "active": return <FiPlay className="w-4 h-4" />;
      case "upcoming": return <FiClock className="w-4 h-4" />;
      case "completed": return <FiCheckCircle className="w-4 h-4" />;
      case "missed": return <FiAlertCircle className="w-4 h-4" />;
      case "disqualified": return <FiAlertCircle className="w-4 h-4" />;
      default: return <FiClock className="w-4 h-4" />;
    }
  };

  const filteredExams = Array.isArray(exams) ? exams.filter(exam => {
    const status = getExamStatus(exam);
    const matchesFilter = filter === "all" || status === filter;
    const matchesSearch = exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         exam.subject?.name?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  }) : [];

  const handleTakeExam = async (examId) => {
    try {
      // Set loading state for this specific exam
      setLoadingExamId(examId);

      console.log('🎯 Starting exam with session-based approach:', examId);

      // Navigate directly to exam page - session creation and data fetching will happen there
      navigate(`/student/take-exam/${examId}`);

    } catch (error) {
      console.error('❌ Error starting exam:', error);
      alert('An error occurred while starting the exam. Please try again.');
    } finally {
      // Clear loading state
      setLoadingExamId(null);
    }
  };

  const handleViewResults = (examId) => {
    navigate(`/student/exam-results/${examId}`);
  };

  if (loading && !refreshing) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} p-4 sm:p-8`}>
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map(i => (
                <div key={i} className={`${cardBg} rounded-xl p-6 shadow-sm`}>
                  <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeBg} ${themeText} p-4 sm:p-6 lg:p-8 transition-colors duration-300`}>
      <div className="w-full max-w-none">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold mb-2">My Exams</h1>
            <p className="text-gray-600 dark:text-gray-400">View and manage your upcoming and completed exams</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="mt-4 sm:mt-0 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
          >
            <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex items-center gap-2">
            <FiFilter className="w-4 h-4 text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className={`px-3 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${inputBg}`}
            >
              <option value="all">All Exams</option>
              <option value="active">Active Now</option>
              <option value="upcoming">Upcoming</option>
              <option value="completed">Completed</option>
              <option value="missed">Missed</option>
              <option value="disqualified">Disqualified</option>
            </select>
          </div>
          
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search exams..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${inputBg}`}
            />
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8">
            <div className="flex items-center gap-3">
              <FiAlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-700 dark:text-red-400">
                {typeof error === 'string' ? error : 'Failed to load exams'}
              </span>
            </div>
          </div>
        )}

        {/* Exams Grid */}
        {filteredExams.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredExams.map((exam) => {
              const status = getExamStatus(exam);
              const formattedStartTime = exam.start_time ? formatExamDateTime(exam.start_time) : 'Not scheduled';

              return (
                <div key={exam.id} className={`${cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200`}>
                  {/* Status Badge */}
                  <div className="flex items-center justify-between mb-4">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                      {getStatusIcon(status)}
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </span>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {exam.total_marks} marks
                    </div>
                  </div>

                  {/* Exam Info */}
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                    {exam.title}
                  </h3>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <FiCalendar className="w-4 h-4" />
                      <span>Start: {formattedStartTime}</span>
                    </div>
                    {exam.end_time && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <FiCalendar className="w-4 h-4" />
                        <span>End: {exam.end_time ? formatExamDateTime(exam.end_time) : 'Not scheduled'}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <FiClock className="w-4 h-4" />
                      <span>{exam.total_duration} minutes</span>
                    </div>
                    {exam.total_questions && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <FiBookOpen className="w-4 h-4" />
                        <span>{exam.total_questions} questions</span>
                      </div>
                    )}
                  </div>

                  {exam.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                      {exam.description}
                    </p>
                  )}

                  {/* Action Button */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    {status === "active" && (
                      <button
                        onClick={() => handleTakeExam(exam.id)}
                        disabled={loadingExamId === exam.id}
                        className={`w-full px-4 py-2 text-white rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 font-medium ${
                          loadingExamId === exam.id
                            ? 'bg-green-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700'
                        }`}
                      >
                        {loadingExamId === exam.id ? (
                          <>
                            <FiRefreshCw className="w-4 h-4 animate-spin" />
                            Loading Exam...
                          </>
                        ) : (
                          <>
                            <FiPlay className="w-4 h-4" />
                            {exam.status === "started" ? "Continue Exam" : "Take Exam Now"}
                          </>
                        )}
                      </button>
                    )}
                    {status === "upcoming" && (
                      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                        Starts {formattedStartTime}
                      </div>
                    )}
                    {status === "completed" && (
                      <button
                        onClick={() => handleViewResults(exam.id)}
                        className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center gap-2 font-medium"
                      >
                        <FiEye className="w-4 h-4" />
                        View Results
                      </button>
                    )}
                    {status === "missed" && (
                      <div className="text-center text-sm text-red-500 dark:text-red-400 font-medium">
                        Exam Missed
                      </div>
                    )}
                    {status === "disqualified" && (
                      <div className="text-center text-sm text-red-500 dark:text-red-400 font-medium">
                        Disqualified
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className={`${cardBg} rounded-xl p-12 text-center shadow-sm border border-gray-200 dark:border-gray-700`}>
            <FiBookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {searchTerm || filter !== "all" ? "No exams found" : "No exams available"}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm || filter !== "all" 
                ? "Try adjusting your search or filter criteria"
                : "Your teachers haven't scheduled any exams yet"
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default StudentExams;
