/**
 * Professional Mentor Multi-Select Component
 * Beautiful UI with complete mentor information
 */

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiUser, FiMail, FiPhone, FiGlobe, FiLinkedin, <PERSON>Twitter, FiDollarSign, <PERSON>Clock, <PERSON>Check } from 'react-icons/fi';
import { fetchMentorsList } from '../../store/slices/MentorsSlice';

const SimpleMentorSelector = ({ 
  selectedMentors = [], 
  onMentorsChange 
}) => {
  const dispatch = useDispatch();
  const {
    publicMentors: mentors,
    publicMentorsLoading: loading,
    publicMentorsError: error
  } = useSelector((state) => state.mentors);

  useEffect(() => {
    console.log('🚀 SimpleMentorSelector: Fetching mentors...');
    dispatch(fetchMentorsList());
  }, [dispatch]);

  console.log('🔍 SimpleMentorSelector state:', {
    mentors,
    mentorsLength: mentors?.length,
    loading,
    error
  });

  const handleMentorChange = (mentorId, isChecked) => {
    if (isChecked) {
      onMentorsChange([...selectedMentors, mentorId]);
    } else {
      onMentorsChange(selectedMentors.filter(id => id !== mentorId));
    }
  };

  if (loading) return <div>Loading mentors...</div>;
  if (error) return <div>Error loading mentors: {error}</div>;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Competition Mentors</h3>
          <p className="text-sm text-gray-600 mt-1">
            Select experienced mentors to guide participants ({mentors?.length || 0} available)
          </p>
        </div>
        {selectedMentors.length > 0 && (
          <div className="bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium">
            {selectedMentors.length} selected
          </div>
        )}
      </div>

      {/* Empty State */}
      {mentors?.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <FiUser className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No mentors available</h3>
          <p className="text-gray-500">Add mentors to your system to select them for competitions.</p>
        </div>
      )}

      {/* Mentor Grid */}
      {mentors?.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mentors.map((mentor) => {
            const isSelected = selectedMentors.includes(mentor.id);

            return (
              <div
                key={mentor.id}
                className={`relative bg-white rounded-xl border-2 transition-all duration-200 hover:shadow-lg cursor-pointer ${
                  isSelected
                    ? 'border-purple-500 bg-purple-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleMentorChange(mentor.id, !isSelected)}
              >
                {/* Selection Indicator */}
                <div className={`absolute top-4 right-4 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                  isSelected
                    ? 'bg-purple-500 border-purple-500'
                    : 'border-gray-300 bg-white'
                }`}>
                  {isSelected && <FiCheck className="w-4 h-4 text-white" />}
                </div>

                <div className="p-6">
                  {/* Profile Section */}
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="flex-shrink-0">
                      {mentor.profile_image_url ? (
                        <img
                          src={mentor.profile_image_url}
                          alt={mentor.full_name || mentor.username}
                          className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                        />
                      ) : (
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-bold text-xl">
                            {(mentor.full_name || mentor.username || 'M').split(' ').map(n => n[0]).join('').toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-lg font-semibold text-gray-900 truncate">
                        {mentor.full_name || mentor.username}
                      </h4>
                      <div className="flex items-center space-x-4 mt-2">
                        {mentor.experience_years && (
                          <div className="flex items-center text-gray-600 text-sm">
                            <FiClock className="w-4 h-4 mr-1" />
                            {mentor.experience_years} yrs
                          </div>
                        )}
                        {mentor.hourly_rate && (
                          <div className="flex items-center text-green-600 text-sm font-medium">
                            <FiDollarSign className="w-4 h-4 mr-1" />
                            R{mentor.hourly_rate}/h
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Bio Section */}
                  {mentor.bio && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {mentor.bio.length > 120 ? `${mentor.bio.substring(0, 120)}...` : mentor.bio}
                      </p>
                    </div>
                  )}

                  {/* Current Position */}
                  {mentor.current_position && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-purple-700">
                        {mentor.current_position}
                      </p>
                    </div>
                  )}

                  {/* Expertise Areas */}
                  {mentor.expertise_areas && mentor.expertise_areas.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {mentor.expertise_areas.slice(0, 3).map((area, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {typeof area === 'object' ? area.name : area}
                          </span>
                        ))}
                        {mentor.expertise_areas.length > 3 && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            +{mentor.expertise_areas.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Contact Information */}
                  <div className="flex items-center space-x-3 text-gray-400">
                    {mentor.email && (
                      <a
                        href={`mailto:${mentor.email}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-purple-600 transition-colors"
                        title={mentor.email}
                      >
                        <FiMail className="w-4 h-4" />
                      </a>
                    )}
                    {mentor.mobile && (
                      <a
                        href={`tel:${mentor.mobile}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-purple-600 transition-colors"
                        title={mentor.mobile}
                      >
                        <FiPhone className="w-4 h-4" />
                      </a>
                    )}
                    {mentor.portfolio_url && (
                      <a
                        href={mentor.portfolio_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-purple-600 transition-colors"
                        title="Portfolio"
                      >
                        <FiGlobe className="w-4 h-4" />
                      </a>
                    )}
                    {mentor.linkedin_url && (
                      <a
                        href={mentor.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-purple-600 transition-colors"
                        title="LinkedIn"
                      >
                        <FiLinkedin className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Selection Summary */}
      {selectedMentors.length > 0 && (
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold text-purple-900">
                {selectedMentors.length} Mentor{selectedMentors.length !== 1 ? 's' : ''} Selected
              </h4>
              <p className="text-purple-700 text-sm mt-1">
                These mentors will guide participants in your competition
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <FiCheck className="w-5 h-5 text-purple-600" />
              <span className="text-purple-600 font-medium">Ready to proceed</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleMentorSelector;
