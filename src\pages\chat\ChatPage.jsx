import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { FiMessageCircle, FiRefreshCw, FiUsers, FiX } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';
import { ConversationList, ChatWindow, FollowersFollowingList } from '../../components/chat';
import { getUserData } from '../../utils/helpers/authHelpers';
import {
  getConversations,
  getConversationMessages,
  sendMessage,
  markConversationRead,
  deleteMessage,
  getChatStats,
  setSelectedConversation,
  clearErrors,
  addVirtualConversation,
  selectConversations,
  selectConversationsLoading,
  selectCurrentConversation,
  selectMessages,
  selectMessagesLoading,
  selectSendingMessage,
  selectChatStats,
  selectSelectedConversationId,

} from '../../store/slices/chatSlice';

/**
 * ChatPage Component
 * Main chat interface for both students and teachers
 */
const ChatPage = () => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get URL parameters
  const urlUserId = searchParams.get('user');
  
  // Redux state
  const conversations = useSelector(selectConversations);
  const conversationsLoading = useSelector(selectConversationsLoading);
  const currentConversation = useSelector(selectCurrentConversation);
  const selectedConversationId = useSelector(selectSelectedConversationId);
  const messagesLoading = useSelector(selectMessagesLoading);
  const sendingMessage = useSelector(selectSendingMessage);
  const chatStats = useSelector(selectChatStats);

  
  // Get messages for selected conversation
  const messages = useSelector(state => 
    selectedConversationId ? selectMessages(state, selectedConversationId) : []
  );
  
  // Local state
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [showFollowersFollowing, setShowFollowersFollowing] = useState(false);
  const [activeView, setActiveView] = useState('conversations'); // 'conversations' or 'followers-following'

  // Get current user ID from localStorage
  const getCurrentUserId = () => {
    const userData = getUserData();
    return userData?.id || null;
  };
  const currentUserId = getCurrentUserId();

  // Load initial data
  useEffect(() => {
    dispatch(getConversations());

    // Load chat stats with error handling
    dispatch(getChatStats()).catch(error => {
      console.warn('Failed to load chat stats:', error);
      // Stats failure shouldn't prevent chat functionality
    });

    // Clear any previous errors
    dispatch(clearErrors());
  }, [dispatch]);

  // Handle URL user parameter
  useEffect(() => {
    if (urlUserId && conversations.length > 0) {
      const conversation = conversations.find(conv => conv.other_user.id === urlUserId);
      if (conversation) {
        handleConversationSelect(urlUserId);
      }
    }
  }, [urlUserId, conversations]);

  // Load messages when conversation is selected
  useEffect(() => {
    if (selectedConversationId) {
      // Check if this is a virtual conversation (new chat)
      const conversation = conversations.find(conv => conv.other_user.id === selectedConversationId);

      if (conversation && conversation.is_virtual) {
        // Don't try to load messages for virtual conversations
        // They will be empty until the first message is sent
        console.log('Virtual conversation selected, skipping message load');
      } else {
        // Load messages for existing conversations
        dispatch(getConversationMessages({ userId: selectedConversationId }));
      }
    }
  }, [selectedConversationId, dispatch, conversations]);

  // Note: WebSocket functionality temporarily removed
  // Chat works with REST API only for now

  const handleConversationSelect = (userId) => {
    dispatch(setSelectedConversation(userId));
    setShowMobileChat(true);
    setActiveView('conversations');

    // Update URL
    setSearchParams({ user: userId });
  };

  const handleStartConversation = (userId, user) => {
    // Check if conversation already exists
    const existingConversation = conversations.find(conv => conv.other_user.id === userId);

    if (existingConversation) {
      // Select existing conversation
      handleConversationSelect(userId);
    } else {
      // Create virtual conversation for new chat
      const virtualConversation = {
        other_user: {
          id: userId,
          username: user.username || 'Unknown User',
          email: user.email || '',
          profile_picture: user.profile_picture || null,
          user_type: user.user_type || 'user'
        },
        last_message: null,
        last_activity: new Date().toISOString(),
        unread_count: 0,
        is_virtual: true // Flag to indicate this is a new conversation
      };

      // Add virtual conversation to Redux state
      dispatch(addVirtualConversation(virtualConversation));

      // Select the new conversation
      dispatch(setSelectedConversation(userId));
      setShowMobileChat(true);
      setActiveView('conversations');

      // Update URL
      setSearchParams({ user: userId });
    }
  };

  const handleToggleFollowersFollowing = () => {
    setActiveView(activeView === 'followers-following' ? 'conversations' : 'followers-following');
    setShowFollowersFollowing(!showFollowersFollowing);
  };

  const handleSendMessage = async (receiverId, message) => {
    try {
      // Send message via REST API
      await dispatch(sendMessage({ receiverId, message })).unwrap();
      console.log('✅ Message sent via REST API');
    } catch (error) {
      console.error('Failed to send message:', error);
      // Error handling is managed by Redux slice
    }
  };

  const handleDeleteMessage = async (messageId) => {
    try {
      await dispatch(deleteMessage(messageId)).unwrap();
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const handleMarkAsRead = async (userId) => {
    try {
      await dispatch(markConversationRead(userId)).unwrap();
    } catch (error) {
      console.error('Failed to mark conversation as read:', error);
    }
  };

  const handleRefresh = () => {
    dispatch(getConversations());

    // Refresh stats with error handling
    dispatch(getChatStats()).catch(error => {
      console.warn('Failed to refresh chat stats:', error);
    });

    if (selectedConversationId) {
      dispatch(getConversationMessages({ userId: selectedConversationId }));
    }
  };

  const handleBackToList = () => {
    setShowMobileChat(false);
    dispatch(setSelectedConversation(null));
    setActiveView('conversations');

    // Clear URL params
    setSearchParams({});
  };

  const handleTyping = (isTyping) => {
    // Typing indicators temporarily disabled (WebSocket not available)
    console.log(`Typing ${isTyping ? 'started' : 'stopped'} (WebSocket disabled)`);
  };

  return (
    <div className={`h-full flex ${currentTheme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Conversations sidebar */}
      <div className={`
        ${showMobileChat ? 'hidden lg:flex' : 'flex'} 
        flex-col w-full lg:w-80 xl:w-96 border-r
        ${currentTheme === 'dark' ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'}
      `}>
        {/* Header */}
        <div className={`
          p-4 border-b flex items-center justify-between
          ${currentTheme === 'dark' ? 'border-gray-700' : 'border-gray-200'}
        `}>
          <div className="flex items-center space-x-3">
            <FiMessageCircle className={`w-6 h-6 ${
              currentTheme === 'dark' ? 'text-blue-400' : 'text-blue-500'
            }`} />
            <div>
              <h1 className={`text-lg font-semibold ${
                currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900'
              }`}>
                {activeView === 'followers-following' ? 'Start Conversation' : 'Messages'}
              </h1>
              {chatStats.unreadMessagesCount > 0 && (
                <p className={`text-sm ${
                  currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {chatStats.unreadMessagesCount} unread
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleToggleFollowersFollowing}
              className={`
                p-2 rounded-lg header-button
                ${activeView === 'followers-following' ? 'active' : ''}
                ${activeView === 'followers-following'
                  ? currentTheme === 'dark'
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-500 text-white'
                  : currentTheme === 'dark'
                    ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-300'
                    : 'hover:bg-gray-100 text-gray-500 hover:text-gray-600'
                }
              `}
              title={activeView === 'followers-following' ? 'Back to conversations' : 'Start new conversation'}
            >
              {activeView === 'followers-following' ? (
                <FiX className="w-5 h-5" />
              ) : (
                <FiUsers className="w-5 h-5" />
              )}
            </button>

            {activeView === 'conversations' && (
              <button
                onClick={handleRefresh}
                disabled={conversationsLoading}
                className={`
                  p-2 rounded-lg header-button
                  ${conversationsLoading ? 'animate-spin' : ''}
                  ${currentTheme === 'dark'
                    ? 'hover:bg-gray-700 text-gray-400'
                    : 'hover:bg-gray-100 text-gray-600'
                  }
                `}
                title="Refresh conversations"
              >
                <FiRefreshCw className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto">
          {activeView === 'conversations' ? (
            <ConversationList
              conversations={conversations}
              selectedConversationId={selectedConversationId}
              onConversationSelect={handleConversationSelect}
              loading={conversationsLoading}
              className="h-full"
            />
          ) : (
            <FollowersFollowingList
              onStartConversation={handleStartConversation}
              className="h-full"
            />
          )}
        </div>
      </div>

      {/* Chat window */}
      <div className={`
        ${showMobileChat ? 'flex' : 'hidden lg:flex'} 
        flex-1 flex-col
      `}>
        <ChatWindow
          conversation={currentConversation}
          messages={messages}
          currentUserId={currentUserId}
          onSendMessage={handleSendMessage}
          onDeleteMessage={handleDeleteMessage}
          onMarkAsRead={handleMarkAsRead}
          onBack={handleBackToList}
          loading={messagesLoading}
          sendingMessage={sendingMessage}
          typingUsers={[]}
          onTyping={handleTyping}
          className="h-full"
        />
      </div>
    </div>
  );
};

export default ChatPage;
