import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiEdit, FiUsers, FiSettings } from 'react-icons/fi';

const AdminEventSidebar = ({ event, onEdit, onViewAnalytics }) => {
  const navigate = useNavigate();

  return (
    <div className="lg:col-span-1">
      {/* Admin Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h3>
        <div className="space-y-3">
          <button
            onClick={onEdit}
            className="w-full flex items-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          >
            <FiEdit className="h-5 w-5 mr-3" />
            Edit Event
          </button>
          <button
            onClick={() => navigate(`/admin/events/${event.id}/registrations`)}
            className="w-full flex items-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors duration-200"
          >
            <FiUsers className="h-5 w-5 mr-3" />
            Manage Registrations ({event.total_registrations || 0})
          </button>
          <button
            onClick={onViewAnalytics}
            className="w-full flex items-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors duration-200"
          >
            <FiSettings className="h-5 w-5 mr-3" />
            View Analytics
          </button>
        </div>
      </div>

      {/* Speakers */}
      {event.speakers && event.speakers.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Speakers</h3>
          <div className="space-y-3">
            {event.speakers.map((speaker, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900">{speaker.name || 'Speaker'}</h4>
                {speaker.title && (
                  <p className="text-sm text-gray-600">{speaker.title}</p>
                )}
                {speaker.bio && (
                  <p className="text-sm text-gray-500 mt-1">{speaker.bio}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tickets */}
      {event.tickets && event.tickets.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Tickets</h3>
          <div className="space-y-3">
            {event.tickets.map((ticket, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{ticket.name}</h4>
                  <span className="text-lg font-bold text-blue-600">
                    {ticket.price === 0 ? 'Free' : `${ticket.currency || 'R'}${ticket.price}`}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{ticket.description}</p>
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-500">
                  <span>Available: {ticket.total_quantity}</span>
                  <span>Status: {ticket.status}</span>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {ticket.requires_approval && (
                    <span className="inline-flex px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                      Requires Approval
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminEventSidebar;
