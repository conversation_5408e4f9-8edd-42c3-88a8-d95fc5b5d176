/**
 * Admin Session Service
 * Handles admin session monitoring, control, and management operations
 */

import { getAuthToken } from '../../../utils/helpers/authHelpers';
import { BASE_URL } from '../../../utils/api/API_URL';
class AdminSessionService {
  constructor() {
    this.baseUrl = BASE_URL;
    this.refreshIntervals = new Map();
    this.sessionCache = new Map();
    this.cacheTimeout = 30000; // 30 seconds
  }

  /**
   * Get all active exam sessions
   * @returns {Promise<Array>} List of active sessions
   */
  async getActiveExamSessions() {
    try {
      console.log('📊 Fetching active exam sessions');

      const token = getAuthToken();
      console.log('🔑 Auth token available:', !!token);

      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      const response = await fetch(`${this.baseUrl}/api/admin/exam-sessions/active`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please login again as admin.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Admin privileges required.');
        }

        const error = await response.json();
        throw new Error(error.detail || `Failed to fetch active sessions (${response.status})`);
      }

      const data = await response.json();
      console.log('📊 Active sessions fetched:', data);

      // Cache the results
      this.sessionCache.set('activeSessions', {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to fetch active sessions:', error);
      throw error;
    }
  }

  /**
   * View specific exam session details
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Session details
   */
  async viewExamSession(sessionId) {
    try {
      console.log('🔍 Viewing exam session details:', sessionId);

      const response = await fetch(`${this.baseUrl}/api/admin/exam-session/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to view session details');
      }

      const data = await response.json();
      console.log('🔍 Session details:', data);

      // Cache session details
      this.sessionCache.set(`session_${sessionId}`, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to view session:', error);
      throw error;
    }
  }

  /**
   * Force submit exam session
   * @param {string} sessionId - The exam session ID
   * @param {string} reason - Reason for force submission
   * @returns {Promise<Object>} Submission result
   */
  async forceSubmitExamSession(sessionId, reason) {
    try {
      console.log('🚨 Force submitting exam session:', { sessionId, reason });

      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      const response = await fetch(`${this.baseUrl}/api/admin/exam-session/${sessionId}/submit?reason=${encodeURIComponent(reason)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please login again as admin.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Admin privileges required.');
        }

        const error = await response.json();
        throw new Error(error.detail || `Failed to force submit session (${response.status})`);
      }

      const data = await response.json();
      console.log('✅ Session force submitted:', data);

      // Clear cached data since session state changed
      this.clearSessionCache(sessionId);

      return data;
    } catch (error) {
      console.error('❌ Force submit failed:', error);
      throw error;
    }
  }

  /**
   * Terminate exam session
   * @param {string} sessionId - The exam session ID
   * @param {string} reason - Reason for termination
   * @returns {Promise<Object>} Termination result
   */
  async terminateExamSession(sessionId, reason) {
    try {
      console.log('🛑 Terminating exam session:', { sessionId, reason });

      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please login again.');
      }

      const response = await fetch(`${this.baseUrl}/api/exams/session/admin/exam-session/${sessionId}/terminate?reason=${encodeURIComponent(reason)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please login again as admin.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Admin privileges required.');
        }

        const error = await response.json();
        throw new Error(error.detail || `Failed to terminate session (${response.status})`);
      }

      const data = await response.json();
      console.log('✅ Session terminated:', data);

      // Clear cached data since session state changed
      this.clearSessionCache(sessionId);

      return data;
    } catch (error) {
      console.error('❌ Termination failed:', error);
      throw error;
    }
  }

  /**
   * Delete exam session
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteExamSession(sessionId) {
    try {
      console.log('🗑️ Deleting exam session:', sessionId);

      const response = await fetch(`${this.baseUrl}/api/admin/exam-session/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to delete session');
      }

      const data = await response.json();
      console.log('✅ Session deleted:', data);

      // Clear cached data since session is deleted
      this.clearSessionCache(sessionId);

      return data;
    } catch (error) {
      console.error('❌ Deletion failed:', error);
      throw error;
    }
  }

  /**
   * Get pending reconnection requests
   * @returns {Promise<Array>} List of pending requests
   */
  async getPendingReconnectionRequests() {
    try {
      console.log('📋 Fetching pending reconnection requests');

      const response = await fetch(`${this.baseUrl}/api/exams/session/admin/reconnection-requests`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch reconnection requests');
      }

      const data = await response.json();
      console.log('📋 Pending requests fetched:', data);

      // Cache the results
      this.sessionCache.set('pendingRequests', {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to fetch pending requests:', error);
      throw error;
    }
  }

  /**
   * Approve or deny reconnection request
   * @param {string} requestId - The reconnection request ID
   * @param {boolean} approved - Whether to approve the request
   * @param {string} reason - Reason for approval/denial
   * @returns {Promise<Object>} Approval result
   */
  async approveReconnectionRequest(requestId, approved, reason) {
    try {
      console.log('⚖️ Processing reconnection request:', { requestId, approved, reason });

      const response = await fetch(`${this.baseUrl}/api/exams/session/admin/reconnection-request/${requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          request_id: requestId,
          approved: approved,
          reason: reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to process reconnection request');
      }

      const data = await response.json();
      console.log('✅ Reconnection request processed:', data);

      // Clear pending requests cache since it changed
      this.sessionCache.delete('pendingRequests');

      return data;
    } catch (error) {
      console.error('❌ Request processing failed:', error);
      throw error;
    }
  }

  /**
   * Start auto-refresh for active sessions
   * @param {Function} onUpdate - Callback for session updates
   * @param {number} interval - Refresh interval in milliseconds
   */
  startAutoRefresh(onUpdate, interval = 30000) {
    // Clear existing refresh
    this.stopAutoRefresh();

    console.log('🔄 Starting auto-refresh for active sessions');

    const refreshInterval = setInterval(async () => {
      try {
        const sessions = await this.getActiveExamSessions();
        if (onUpdate) {
          onUpdate(sessions);
        }
      } catch (error) {
        console.error('❌ Auto-refresh failed:', error);
      }
    }, interval);

    this.refreshIntervals.set('activeSessions', refreshInterval);
  }

  /**
   * Start auto-refresh for pending requests
   * @param {Function} onUpdate - Callback for request updates
   * @param {number} interval - Refresh interval in milliseconds
   */
  startRequestsAutoRefresh(onUpdate, interval = 15000) {
    // Clear existing refresh
    this.stopRequestsAutoRefresh();

    console.log('🔄 Starting auto-refresh for pending requests');

    const refreshInterval = setInterval(async () => {
      try {
        const requests = await this.getPendingReconnectionRequests();
        if (onUpdate) {
          onUpdate(requests);
        }
      } catch (error) {
        console.error('❌ Requests auto-refresh failed:', error);
      }
    }, interval);

    this.refreshIntervals.set('pendingRequests', refreshInterval);
  }

  /**
   * Stop auto-refresh for active sessions
   */
  stopAutoRefresh() {
    const interval = this.refreshIntervals.get('activeSessions');
    if (interval) {
      clearInterval(interval);
      this.refreshIntervals.delete('activeSessions');
      console.log('⏹️ Auto-refresh stopped for active sessions');
    }
  }

  /**
   * Stop auto-refresh for pending requests
   */
  stopRequestsAutoRefresh() {
    const interval = this.refreshIntervals.get('pendingRequests');
    if (interval) {
      clearInterval(interval);
      this.refreshIntervals.delete('pendingRequests');
      console.log('⏹️ Auto-refresh stopped for pending requests');
    }
  }

  /**
   * Stop all auto-refresh intervals
   */
  stopAllAutoRefresh() {
    console.log('⏹️ Stopping all auto-refresh intervals');
    for (const [key, interval] of this.refreshIntervals) {
      clearInterval(interval);
    }
    this.refreshIntervals.clear();
  }

  /**
   * Get cached data if available and not expired
   * @param {string} key - Cache key
   * @returns {*} Cached data or null
   */
  getCachedData(key) {
    const cached = this.sessionCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Clear session cache for specific session
   * @param {string} sessionId - The session ID
   */
  clearSessionCache(sessionId) {
    this.sessionCache.delete(`session_${sessionId}`);
    this.sessionCache.delete('activeSessions'); // Also clear active sessions since it contains this session
  }

  /**
   * Clear all cached data
   */
  clearAllCache() {
    console.log('🧹 Clearing all cached data');
    this.sessionCache.clear();
  }

  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getServiceStats() {
    return {
      activeRefreshIntervals: this.refreshIntervals.size,
      cachedItems: this.sessionCache.size,
      refreshIntervals: Array.from(this.refreshIntervals.keys()),
      cacheKeys: Array.from(this.sessionCache.keys())
    };
  }

  /**
   * Cleanup service resources
   */
  cleanup() {
    console.log('🧹 Cleaning up AdminSessionService');
    this.stopAllAutoRefresh();
    this.clearAllCache();
  }
}

// Create singleton instance
const adminSessionService = new AdminSessionService();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    adminSessionService.cleanup();
  });
}

export default adminSessionService;
