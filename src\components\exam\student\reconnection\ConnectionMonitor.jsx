/**
 * Connection Monitor
 * Monitors exam session connection and handles disconnection scenarios
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNotification } from '../../../../contexts/NotificationContext';
import examReconnectionService from '../../../../services/exam/reconnection/ExamReconnectionService';
import {
  selectExamSession
} from '../../../../store/slices/exam/examSessionSlice';
import {
  requestReconnection,
  selectReconnectionState
} from '../../../../store/slices/exam/examReconnectionSlice';
import ReconnectionRequestModal from './ReconnectionRequestModal';
import ReconnectionStatusIndicator from './ReconnectionStatusIndicator';
import {
  FiWifi,
  FiWifiOff,
  FiAlertTriangle
} from 'react-icons/fi';

const ConnectionMonitor = ({ 
  sessionId, 
  examTitle,
  onReconnected,
  children 
}) => {
  const dispatch = useDispatch();
  const { showWarning, showError, showSuccess } = useNotification();
  
  const examSession = useSelector(selectExamSession);
  const reconnectionState = useSelector(selectReconnectionState);
  
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionLost, setConnectionLost] = useState(false);
  const [showReconnectionModal, setShowReconnectionModal] = useState(false);
  const [lastHeartbeat, setLastHeartbeat] = useState(Date.now());
  const [heartbeatInterval, setHeartbeatInterval] = useState(null);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      console.log('🌐 Connection restored');
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('🌐 Connection lost');
      handleConnectionLoss('Internet connection lost');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Monitor exam session connection status
  useEffect(() => {
    if (examSession.connectionStatus === 'disconnected' && !connectionLost) {
      handleConnectionLoss('Exam session disconnected');
    } else if (examSession.connectionStatus === 'connected' && connectionLost) {
      handleConnectionRestored();
    }
  }, [examSession.connectionStatus, connectionLost]);

  // Heartbeat monitoring
  useEffect(() => {
    if (sessionId && examSession.status === 'active') {
      const interval = setInterval(() => {
        const now = Date.now();
        const timeSinceLastHeartbeat = now - lastHeartbeat;
        
        // If no heartbeat for 30 seconds, consider connection lost
        if (timeSinceLastHeartbeat > 30000) {
          console.log('💓 Heartbeat timeout detected');
          handleConnectionLoss('Heartbeat timeout');
        }
      }, 10000); // Check every 10 seconds

      setHeartbeatInterval(interval);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }
  }, [sessionId, examSession.status, lastHeartbeat]);

  // Update heartbeat when session heartbeat updates
  useEffect(() => {
    if (examSession.lastHeartbeat) {
      setLastHeartbeat(examSession.lastHeartbeat);
    }
  }, [examSession.lastHeartbeat]);

  // Handle connection loss
  const handleConnectionLoss = useCallback(async (reason) => {
    if (connectionLost) return; // Already handling disconnection

    console.log('🚨 Connection loss detected:', reason);
    setConnectionLost(true);

    // Show warning notification
    showWarning('Connection lost. Attempting to reconnect...');

    // Check if there's already an active reconnection request
    if (examReconnectionService.hasActiveReconnection(sessionId)) {
      console.log('🔄 Reconnection already in progress');
      return;
    }

    // Auto-request reconnection for certain scenarios
    if (reason.includes('Internet') || reason.includes('Heartbeat')) {
      try {
        await examReconnectionService.handleConnectionLoss(
          sessionId, 
          reason,
          handleReconnectionStatusChange
        );
        showSuccess('Reconnection request submitted automatically');
      } catch (error) {
        console.error('Auto-reconnection failed:', error);
        setShowReconnectionModal(true);
      }
    } else {
      // Show manual reconnection modal for other scenarios
      setShowReconnectionModal(true);
    }
  }, [sessionId, connectionLost, showWarning, showError, showSuccess]);

  // Handle connection restored
  const handleConnectionRestored = useCallback(() => {
    console.log('✅ Connection restored');
    setConnectionLost(false);
    setShowReconnectionModal(false);
    showSuccess('Connection restored successfully');
    
    if (onReconnected) {
      onReconnected();
    }
  }, [onReconnected, showSuccess]);

  // Handle reconnection status changes
  const handleReconnectionStatusChange = useCallback((status) => {
    console.log('📊 Reconnection status changed:', status);
    
    if (status.status === 'approved') {
      handleConnectionRestored();
    } else if (status.status === 'denied') {
      showError('Reconnection request was denied');
      setShowReconnectionModal(true); // Allow manual retry
    }
  }, [handleConnectionRestored, showError]);

  // Handle manual reconnection request
  const handleManualReconnection = () => {
    setShowReconnectionModal(true);
  };

  // Handle reconnection modal close
  const handleModalClose = () => {
    setShowReconnectionModal(false);
  };

  // Handle successful reconnection from modal
  const handleModalReconnected = () => {
    handleConnectionRestored();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      examReconnectionService.stopAllPolling();
    };
  }, [heartbeatInterval]);

  return (
    <div className="relative">
      {/* Connection Status Indicator */}
      {(connectionLost || !isOnline) && (
        <div className="fixed top-4 right-4 z-40">
          <div className="bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
            <FiWifiOff className="w-4 h-4" />
            <span className="text-sm font-medium">
              {!isOnline ? 'No Internet' : 'Session Disconnected'}
            </span>
            {connectionLost && (
              <button
                onClick={handleManualReconnection}
                className="ml-2 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs"
              >
                Reconnect
              </button>
            )}
          </div>
        </div>
      )}

      {/* Reconnection Status Indicator */}
      {reconnectionState.currentRequest && (
        <div className="fixed top-16 right-4 z-40 max-w-sm">
          <ReconnectionStatusIndicator
            sessionId={sessionId}
            onResumeSuccess={handleModalReconnected}
          />
        </div>
      )}

      {/* Main Content */}
      <div className={connectionLost ? 'pointer-events-none opacity-75' : ''}>
        {children}
      </div>

      {/* Connection Lost Overlay */}
      {connectionLost && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-30">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 shadow-xl">
            <div className="text-center">
              <FiAlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Connection Lost
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Your exam session has been disconnected. Please wait while we attempt to reconnect you.
              </p>
              <button
                onClick={handleManualReconnection}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Request Reconnection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reconnection Request Modal */}
      <ReconnectionRequestModal
        isOpen={showReconnectionModal}
        onClose={handleModalClose}
        sessionId={sessionId}
        examTitle={examTitle}
        onReconnected={handleModalReconnected}
      />
    </div>
  );
};

export default ConnectionMonitor;
