import React from 'react';
import { 
  FiFileText, 
  FiClock, 
  FiCalendar, 
  FiUsers, 
  FiCheckCircle,
  FiAlertCircle,
  FiPlay,
  FiPause,
  FiUser
} from 'react-icons/fi';
import DataTable from '../DataTable';
import { ViewButton, EditButton, DeleteButton, QuickActionBar } from '../buttons';

/**
 * Specialized ExamTable component for displaying exam data
 * Provides consistent exam data presentation with built-in actions
 */
const ExamTable = ({
  exams = [],
  onView,
  onEdit,
  onDelete,
  onStart,
  onBulkAction,
  showActions = true,
  showClassroom = false,
  showResults = false,
  userRole = 'teacher', // 'teacher', 'student', 'admin'
  selectable = false,
  ...props
}) => {
  // Ensure all exams have an 'id' field for DataTable compatibility
  const normalizedExams = exams.map(exam => ({
    ...exam,
    id: exam.id || exam._id || exam.exam_id
  }));
  // Define exam-specific columns
  const columns = [
    {
      key: 'icon',
      label: '',
      sortable: false,
      hideOnMobile: false,
      width: '60px',
      render: (value, exam) => (
        <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg flex-shrink-0">
          <FiFileText className="text-green-600 dark:text-green-400" size={20} />
        </div>
      )
    },
    {
      key: 'title',
      label: 'Exam',
      sortable: true,
      render: (value, exam) => (
        <div className="min-w-0">
          <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {exam.title || exam.name || 'Untitled Exam'}
          </div>
          {exam.description && (
            <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {exam.description}
            </div>
          )}
          <div className="flex items-center space-x-3 mt-1">
            {exam.duration && (
              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                <FiClock className="w-3 h-3" />
                <span>{exam.duration} min</span>
              </div>
            )}
            {exam.total_questions && (
              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                <FiFileText className="w-3 h-3" />
                <span>{exam.total_questions} questions</span>
              </div>
            )}
            {exam.total_marks && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {exam.total_marks} marks
              </div>
            )}
          </div>
        </div>
      )
    }
  ];

  // Add classroom column if enabled (for admin/student view)
  if (showClassroom) {
    columns.push({
      key: 'classroom',
      label: 'Classroom',
      sortable: true,
      hideOnMobile: true,
      render: (value, exam) => {
        const classroom = exam.classroom || exam.classroom_info;
        if (!classroom) return 'N/A';
        
        return (
          <div className="text-sm text-gray-900 dark:text-gray-100">
            {classroom.name || 'Unknown Classroom'}
          </div>
        );
      }
    });
  }

  // Add schedule column
  columns.push({
    key: 'schedule',
    label: 'Schedule',
    sortable: true,
    hideOnMobile: true,
    render: (value, exam) => (
      <div className="space-y-1">
        {exam.start_time && (
          <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
            <FiCalendar className="w-3 h-3" />
            <span>{new Date(exam.start_time).toLocaleDateString()}</span>
          </div>
        )}
        {exam.start_time && (
          <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
            <FiClock className="w-3 h-3" />
            <span>{new Date(exam.start_time).toLocaleTimeString()}</span>
          </div>
        )}
      </div>
    )
  });

  // Add participants column
  columns.push({
    key: 'participants',
    label: 'Participants',
    sortable: true,
    hideOnMobile: true,
    render: (value, exam) => (
      <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
        <FiUsers className="w-4 h-4" />
        <span>
          {exam.submitted_count || 0} / {exam.assigned_count || exam.total_participants || 0}
        </span>
      </div>
    )
  });

  // Add status column
  columns.push({
    key: 'status',
    label: 'Status',
    sortable: true,
    render: (value, exam) => {
      const getStatusConfig = (exam) => {
        const now = new Date();
        const startTime = exam.start_time ? new Date(exam.start_time) : null;
        const endTime = exam.end_time ? new Date(exam.end_time) : null;
        
        if (exam.status === 'draft') {
          return {
            icon: FiPause,
            color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
            label: 'Draft'
          };
        }
        
        if (exam.status === 'published' || exam.status === 'active') {
          if (startTime && now < startTime) {
            return {
              icon: FiClock,
              color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
              label: 'Scheduled'
            };
          }
          
          if (endTime && now > endTime) {
            return {
              icon: FiCheckCircle,
              color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
              label: 'Completed'
            };
          }
          
          return {
            icon: FiPlay,
            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            label: 'Active'
          };
        }
        
        return {
          icon: FiAlertCircle,
          color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          label: exam.status || 'Unknown'
        };
      };

      const config = getStatusConfig(exam);
      const IconComponent = config.icon;

      return (
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${config.color}
        `}>
          <IconComponent className="w-3 h-3 mr-1" />
          {config.label}
        </span>
      );
    }
  });

  // Add results column if enabled (for teacher view)
  if (showResults && userRole === 'teacher') {
    columns.push({
      key: 'results',
      label: 'Results',
      sortable: false,
      hideOnMobile: true,
      render: (value, exam) => {
        if (!exam.average_score && !exam.highest_score) return 'N/A';
        
        return (
          <div className="space-y-1">
            {exam.average_score && (
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Avg: {exam.average_score}%
              </div>
            )}
            {exam.highest_score && (
              <div className="text-xs text-gray-600 dark:text-gray-400">
                High: {exam.highest_score}%
              </div>
            )}
          </div>
        );
      }
    });
  }

  // Add actions column if enabled
  if (showActions) {
    columns.push({
      key: 'actions',
      label: 'Actions',
      sortable: false,
      width: '120px',
      render: (value, exam) => {
        const canEdit = userRole === 'teacher' && exam.status === 'draft';
        const canStart = userRole === 'student' && exam.status === 'active';
        
        return (
          <QuickActionBar
            onView={onView ? () => onView(exam) : undefined}
            onEdit={onEdit && canEdit ? () => onEdit(exam) : undefined}
            onDelete={onDelete && userRole === 'teacher' ? () => onDelete(exam) : undefined}
            showView={!!onView}
            showEdit={!!onEdit && canEdit}
            showDelete={!!onDelete && userRole === 'teacher'}
          >
            {canStart && onStart && (
              <button
                onClick={() => onStart(exam)}
                className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                title="Start Exam"
              >
                <FiPlay className="w-4 h-4" />
              </button>
            )}
          </QuickActionBar>
        );
      }
    });
  }

  // Bulk actions for exam management
  const bulkActions = [];
  if (onBulkAction && userRole === 'teacher') {
    bulkActions.push(
      {
        label: 'Delete Selected',
        action: 'delete',
        onClick: (selectedExams) => onBulkAction('delete', selectedExams),
        variant: 'danger'
      }
    );
  }

  // Custom empty state for exams
  const emptyState = (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <FiFileText className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No exams found
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        {userRole === 'teacher' 
          ? "You haven't created any exams yet. Create your first exam to get started."
          : "No exams are available at the moment. Check back later."
        }
      </p>
    </div>
  );

  return (
    <DataTable
      data={normalizedExams}
      columns={columns}
      selectable={selectable}
      bulkActions={bulkActions}
      emptyState={emptyState}
      {...props}
    />
  );
};

export default ExamTable;
