/**
 * FollowersFollowingList Component
 * Instagram-like followers/following list for starting conversations
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FiUsers, FiUserCheck, FiSearch, FiMessageCircle, FiLoader, FiRefreshCw, FiWifi, FiWifiOff } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';
import { useThemeProvider } from '../../providers/ThemeContext';
import { getUserData } from '../../utils/helpers/authHelpers';

const FollowersFollowingList = ({
  onStartConversation,
  className = ''
}) => {
  const { currentTheme } = useThemeProvider();
  const [activeTab, setActiveTab] = useState('followers');
  const [followers, setFollowers] = useState([]);
  const [following, setFollowing] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState({});
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);

  // Cache configuration
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  const CACHE_KEY_PREFIX = 'followers_following_cache_';
  const AUTO_REFRESH_INTERVAL = 30 * 1000; // 30 seconds

  // Get current user ID from localStorage
  const getCurrentUserId = useCallback(() => {
    const userData = getUserData();
    return userData?.id || null;
  }, []);

  // Cache management
  const getCacheKey = useCallback((type, userId) => {
    return `${CACHE_KEY_PREFIX}${type}_${userId}`;
  }, []);

  const getCachedData = useCallback((type, userId) => {
    try {
      const cacheKey = getCacheKey(type, userId);
      const cached = localStorage.getItem(cacheKey);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const now = Date.now();
        if (now - timestamp < CACHE_DURATION) {
          return data;
        } else {
          // Cache expired, remove it
          localStorage.removeItem(cacheKey);
        }
      }
    } catch (error) {
      console.warn('Failed to get cached data:', error);
    }
    return null;
  }, [getCacheKey, CACHE_DURATION]);

  const setCachedData = useCallback((type, userId, data) => {
    try {
      const cacheKey = getCacheKey(type, userId);
      const cacheData = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache data:', error);
    }
  }, [getCacheKey]);

  const loadData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      const currentUserId = getCurrentUserId();
      if (!currentUserId) {
        throw new Error('User not authenticated');
      }

      // Check cache first (unless force refresh)
      if (!forceRefresh) {
        const cachedData = getCachedData(activeTab, currentUserId);
        if (cachedData) {
          console.log(`Using cached ${activeTab} data`);
          if (activeTab === 'followers') {
            setFollowers(cachedData);
          } else {
            setFollowing(cachedData);
          }
          setLoading(false);
          return;
        }
      }

      console.log(`Fetching fresh ${activeTab} data`);

      if (activeTab === 'followers') {
        const response = await socialFollowService.getUserFollowers(currentUserId, 1, 50);
        const users = response.users || [];
        setFollowers(users);
        setCachedData('followers', currentUserId, users);
      } else {
        const response = await socialFollowService.getUserFollowing(currentUserId, 1, 50);
        const users = response.users || [];
        setFollowing(users);
        setCachedData('following', currentUserId, users);
      }

      setLastFetch(prev => ({
        ...prev,
        [activeTab]: Date.now()
      }));

    } catch (err) {
      console.error('Failed to load data:', err);
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  }, [activeTab, getCurrentUserId, getCachedData, setCachedData]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (autoRefreshEnabled) {
        console.log('Back online, refreshing data...');
        loadData(true);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('Gone offline, using cached data');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [autoRefreshEnabled, loadData]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefreshEnabled || !isOnline) return;

    const interval = setInterval(() => {
      const lastFetchTime = lastFetch[activeTab];
      const now = Date.now();

      // Only auto-refresh if data is older than cache duration
      if (!lastFetchTime || (now - lastFetchTime) > CACHE_DURATION) {
        console.log(`Auto-refreshing ${activeTab} data...`);
        loadData(true);
      }
    }, AUTO_REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, [autoRefreshEnabled, isOnline, activeTab, lastFetch, loadData, CACHE_DURATION, AUTO_REFRESH_INTERVAL]);

  const handleRefresh = useCallback(() => {
    loadData(true); // Force refresh
  }, [loadData]);

  const handleStartConversation = useCallback((user) => {
    onStartConversation(user.id, user);
  }, [onStartConversation]);

  // Memoized filtered list for performance
  const filteredList = useMemo(() => {
    const currentList = activeTab === 'followers' ? followers : following;
    if (!searchQuery.trim()) {
      return currentList;
    }

    const query = searchQuery.toLowerCase();
    return currentList.filter(user =>
      user.username?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query)
    );
  }, [activeTab, followers, following, searchQuery]);

  const getUserInitials = (user) => {
    if (user.username) {
      return user.username.substring(0, 2).toUpperCase();
    }
    if (user.email) {
      return user.email.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  const getUserTypeColor = (userType) => {
    const colors = {
      student: 'bg-blue-100 text-blue-800',
      teacher: 'bg-green-100 text-green-800',
      institute: 'bg-purple-100 text-purple-800',
      mentor: 'bg-orange-100 text-orange-800',
      admin: 'bg-red-100 text-red-800'
    };
    return colors[userType?.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`flex flex-col h-full followers-following-list ${className}`}>
      {/* Header */}
      <div className={`p-4 border-b ${
        currentTheme === 'dark' ? 'border-gray-700' : 'border-gray-200'
      }`}>
        <div className="flex items-center justify-between mb-4">
          <h2 className={`text-lg font-semibold ${
            currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900'
          }`}>
            Start Conversation
          </h2>

          <div className="flex items-center space-x-2">
            {/* Connection Status */}
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
              isOnline
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
            }`}>
              {isOnline ? <FiWifi className="w-3 h-3" /> : <FiWifiOff className="w-3 h-3" />}
              <span>{isOnline ? 'Online' : 'Offline'}</span>
            </div>

            {/* Auto-refresh Toggle */}
            <button
              onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
              className={`px-2 py-1 rounded text-xs transition-all duration-200 ${
                autoRefreshEnabled
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                  : currentTheme === 'dark'
                  ? 'text-gray-500 hover:bg-gray-700'
                  : 'text-gray-400 hover:bg-gray-100'
              }`}
              title={autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'}
            >
              Auto
            </button>

            {/* Manual Refresh */}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className={`p-2 rounded-lg transition-colors ${
                currentTheme === 'dark'
                  ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-300'
                  : 'hover:bg-gray-100 text-gray-500 hover:text-gray-600'
              } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Refresh"
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className={`flex rounded-lg p-1 chat-tabs ${
          currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
        }`}>
          <button
            onClick={() => setActiveTab('followers')}
            className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium chat-tab ${
              activeTab === 'followers' ? 'active' : ''
            } ${
              activeTab === 'followers'
                ? currentTheme === 'dark'
                  ? 'bg-gray-600 text-white'
                  : 'bg-white text-gray-900 shadow-sm'
                : currentTheme === 'dark'
                  ? 'text-gray-300 hover:text-white'
                  : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiUsers className="w-4 h-4 mr-2" />
            Followers ({followers.length})
          </button>
          <button
            onClick={() => setActiveTab('following')}
            className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium chat-tab ${
              activeTab === 'following' ? 'active' : ''
            } ${
              activeTab === 'following'
                ? currentTheme === 'dark'
                  ? 'bg-gray-600 text-white'
                  : 'bg-white text-gray-900 shadow-sm'
                : currentTheme === 'dark'
                  ? 'text-gray-300 hover:text-white'
                  : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <FiUserCheck className="w-4 h-4 mr-2" />
            Following ({following.length})
          </button>
        </div>

        {/* Search */}
        <div className="mt-4 relative">
          <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
            currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`} />
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border search-input ${
              currentTheme === 'dark'
                ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400 focus:border-blue-500'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <FiLoader className="w-6 h-6 animate-spin text-blue-500" />
            <span className={`ml-2 ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Loading {activeTab}...
            </span>
          </div>
        ) : error ? (
          <div className="p-4 text-center">
            <p className={`text-sm ${
              currentTheme === 'dark' ? 'text-red-400' : 'text-red-600'
            }`}>
              {error}
            </p>
            <button
              onClick={handleRefresh}
              className="mt-2 text-sm text-blue-500 hover:text-blue-600"
            >
              Try again
            </button>
          </div>
        ) : filteredList.length === 0 ? (
          <div className="p-4 text-center">
            <FiUsers className={`w-12 h-12 mx-auto mb-3 ${
              currentTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <p className={`text-sm ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {searchQuery 
                ? `No ${activeTab} found matching "${searchQuery}"`
                : `No ${activeTab} yet`
              }
            </p>
          </div>
        ) : (
          <div className="p-2">
            {filteredList.map((user) => (
              <div
                key={user.id}
                className={`flex items-center p-3 rounded-lg mb-2 user-item cursor-pointer ${
                  currentTheme === 'dark'
                    ? 'hover:bg-gray-700'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleStartConversation(user)}
              >
                {/* Avatar */}
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm mr-3 user-avatar">
                  {user.profile_picture ? (
                    <img
                      src={user.profile_picture}
                      alt={user.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    getUserInitials(user)
                  )}
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className={`font-medium truncate ${
                      currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900'
                    }`}>
                      {user.username || 'Unknown User'}
                    </h3>
                    {user.user_type && (
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium user-type-badge ${getUserTypeColor(user.user_type)}`}>
                        {user.user_type}
                      </span>
                    )}
                  </div>
                  {user.email && (
                    <p className={`text-sm truncate ${
                      currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {user.email}
                    </p>
                  )}
                </div>

                {/* Message Icon */}
                <div className="flex-shrink-0 ml-2">
                  <FiMessageCircle className={`w-5 h-5 ${
                    currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  }`} />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FollowersFollowingList;
