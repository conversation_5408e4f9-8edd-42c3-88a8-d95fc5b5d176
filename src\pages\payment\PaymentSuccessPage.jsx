/**
 * PaymentSuccessPage Component
 * 
 * Handles the success return flow from PayFast payments.
 * Displays payment confirmation and registration details.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiCalendar,
  FiUser,
  FiLoader
} from 'react-icons/fi';
import { PaymentConfirmation, PaymentStatus } from '../../components/payment';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import {
  getPaymentStatus,
  pollPaymentStatus,
  selectPaymentStatus,
  selectStatusLoading,
  selectStatusError,
  selectPollingLoading
} from '../../store/slices/PaymentSlice';
import {
  checkRegistrationPaymentStatus,
  selectCurrentRegistration,
  selectRegistrationPaymentStatusLoading
} from '../../store/slices/EventsSlice';

const PaymentSuccessPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  
  // Get URL parameters
  const paymentId = searchParams.get('payment_id');
  const registrationId = searchParams.get('registration_id');
  
  // Local state
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const [event, setEvent] = useState(null);

  // Redux selectors
  const paymentStatus = useSelector(selectPaymentStatus);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);
  const pollingLoading = useSelector(selectPollingLoading);
  const currentRegistration = useSelector(selectCurrentRegistration);
  const registrationStatusLoading = useSelector(selectRegistrationPaymentStatusLoading);

  // Verify payment status on mount
  useEffect(() => {
    const verifyPayment = async () => {
      if (!paymentId) {
        setIsVerifying(false);
        return;
      }

      try {
        // Get payment status
        await dispatch(getPaymentStatus(paymentId)).unwrap();
        
        // If we have registration ID, check registration status too
        if (registrationId) {
          await dispatch(checkRegistrationPaymentStatus({
            registrationId,
            paymentId
          })).unwrap();
        }
        
        setVerificationComplete(true);
      } catch (error) {
        console.error('Payment verification failed:', error);
      } finally {
        setIsVerifying(false);
      }
    };

    verifyPayment();
  }, [dispatch, paymentId, registrationId]);

  // Poll payment status if still pending
  useEffect(() => {
    if (paymentStatus && paymentStatus.status === 'pending' && !pollingLoading) {
      const pollTimer = setTimeout(() => {
        dispatch(pollPaymentStatus({ paymentId, maxAttempts: 10, interval: 3000 }));
      }, 2000);

      return () => clearTimeout(pollTimer);
    }
  }, [dispatch, paymentStatus, paymentId, pollingLoading]);

  // Handle download receipt
  const handleDownloadReceipt = () => {
    if (paymentStatus) {
      // Create receipt data
      const receiptData = {
        paymentId: paymentStatus.payment_id,
        amount: paymentStatus.amount,
        currency: paymentStatus.currency,
        date: paymentStatus.processed_at || paymentStatus.created_at,
        status: paymentStatus.status,
        registration: currentRegistration,
        event: event
      };

      // Generate and download receipt (simplified version)
      const receiptContent = `
PAYMENT RECEIPT
===============

Payment ID: ${receiptData.paymentId}
Amount: ${receiptData.currency} ${receiptData.amount}
Date: ${new Date(receiptData.date).toLocaleString()}
Status: ${receiptData.status.toUpperCase()}

${receiptData.registration ? `
Registration Number: ${receiptData.registration.confirmation_number}
Event: ${receiptData.event?.title || 'N/A'}
` : ''}

Thank you for your payment!
EduFair Platform
      `;

      const blob = new Blob([receiptContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `receipt-${paymentStatus.payment_id.slice(-8)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Handle share confirmation
  const handleShareConfirmation = () => {
    if (navigator.share && currentRegistration) {
      navigator.share({
        title: 'Event Registration Confirmed',
        text: `I've successfully registered for ${event?.title || 'an event'}! Confirmation: ${currentRegistration.confirmation_number}`,
        url: window.location.href
      });
    } else {
      // Fallback to clipboard
      const shareText = `I've successfully registered for ${event?.title || 'an event'}! Confirmation: ${currentRegistration?.confirmation_number}`;
      navigator.clipboard.writeText(shareText);
      // You could show a toast notification here
    }
  };

  // Handle navigation
  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoToEvents = () => {
    navigate('/events');
  };

  const handleGoToProfile = () => {
    const userRole = localStorage.getItem('role')?.toLowerCase();
    if (userRole === 'student') {
      // Redirect students to My Events instead of dashboard
      navigate('/student/events/my');
    } else if (userRole) {
      navigate(`/${userRole}/dashboard`);
    } else {
      navigate('/profile');
    }
  };

  // Loading state
  if (isVerifying || statusLoading || registrationStatusLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <h2 className="text-xl font-semibold text-gray-900 mt-4">
            Verifying Payment
          </h2>
          <p className="text-gray-600 mt-2">
            Please wait while we confirm your payment...
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (statusError && !paymentStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorMessage 
            message={statusError}
            onRetry={() => paymentId && dispatch(getPaymentStatus(paymentId))}
          />
        </div>
      </div>
    );
  }

  // No payment ID provided
  if (!paymentId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Payment Reference
          </h2>
          <p className="text-gray-600 mb-4">
            No payment information was found. Please check your payment confirmation email.
          </p>
          <button
            onClick={handleGoHome}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Payment Confirmation
              </h1>
              <p className="text-sm text-gray-500">
                Your payment has been processed
              </p>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={handleGoToEvents}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FiCalendar className="w-4 h-4" />
                <span>Events</span>
              </button>
              
              <button
                onClick={handleGoToProfile}
                className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FiUser className="w-4 h-4" />
                <span>Profile</span>
              </button>
              
              <button
                onClick={handleGoHome}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiHome className="w-4 h-4" />
                <span>Home</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Payment Status Check */}
        {paymentStatus && paymentStatus.status === 'pending' && (
          <div className="mb-8">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <FiLoader className="w-5 h-5 text-yellow-600 animate-spin" />
                <div>
                  <h3 className="font-semibold text-yellow-800">
                    Payment Processing
                  </h3>
                  <p className="text-yellow-700 text-sm">
                    Your payment is still being processed. This page will update automatically.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {paymentStatus && paymentStatus.status === 'completed' ? (
              <PaymentConfirmation
                payment={paymentStatus}
                registration={currentRegistration}
                event={event}
                onDownloadReceipt={handleDownloadReceipt}
                onShareConfirmation={handleShareConfirmation}
              />
            ) : (
              <PaymentStatus
                paymentId={paymentId}
                autoRefresh={true}
                refreshInterval={5000}
                onStatusChange={(status) => {
                  if (status.status === 'completed' && !verificationComplete) {
                    // Refresh registration status when payment completes
                    if (registrationId) {
                      dispatch(checkRegistrationPaymentStatus({
                        registrationId,
                        paymentId
                      }));
                    }
                  }
                }}
              />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              
              <div className="space-y-3">
                <button
                  onClick={handleGoToEvents}
                  className="w-full flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FiCalendar className="w-4 h-4" />
                  <span>Browse More Events</span>
                </button>
                
                <button
                  onClick={handleGoToProfile}
                  className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <FiUser className="w-4 h-4" />
                  <span>View My Registrations</span>
                </button>
                
                <button
                  onClick={handleGoHome}
                  className="w-full flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <FiHome className="w-4 h-4" />
                  <span>Go to Homepage</span>
                </button>
              </div>
            </div>

            {/* Support */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">
                Need Help?
              </h4>
              <p className="text-sm text-blue-700 mb-3">
                If you have any questions about your payment or registration, we're here to help.
              </p>
              <div className="space-y-1 text-sm text-blue-600">
                <p>Email: <EMAIL></p>
                <p>Phone: +27 12 345 6789</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
