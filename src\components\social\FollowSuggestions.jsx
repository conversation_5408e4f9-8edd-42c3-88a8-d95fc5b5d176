import React, { useState, useEffect } from 'react';
import { FiUsers, FiRefreshCw, FiLoader, FiAlertCircle, FiX } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';
import FollowButton from './FollowButton';
import SocialUserListItem from './UserListItem';
import { extractSuggestionsFromResponse, extractErrorMessage } from '../../utils/helpers/apiResponseHelpers';

/**
 * FollowSuggestions Component
 * Displays suggested users to follow based on mutual connections
 */
const FollowSuggestions = ({ 
  limit = 5,
  layout = 'card', // 'card' or 'list'
  showHeader = true,
  showRefresh = true,
  className = '',
  onSuggestionFollowed = null
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dismissedSuggestions, setDismissedSuggestions] = useState(new Set());

  useEffect(() => {
    loadSuggestions();
  }, [limit]);

  const loadSuggestions = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await socialFollowService.getFollowSuggestions(limit);
      console.log('Follow suggestions response:', response);

      // Use helper function to extract suggestions from response
      const suggestionsData = extractSuggestionsFromResponse(response);
      setSuggestions(suggestionsData);
    } catch (err) {
      console.error('Failed to load follow suggestions:', err);
      setError(extractErrorMessage(err));
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing, userId, user) => {
    if (isFollowing) {
      // Remove from suggestions when followed
      setSuggestions(prev => prev.filter(suggestion => suggestion.id !== userId));
      onSuggestionFollowed?.(user, userId);
    }
  };

  const handleDismiss = (userId) => {
    setDismissedSuggestions(prev => new Set([...prev, userId]));
    setSuggestions(prev => prev.filter(suggestion => suggestion.id !== userId));
  };

  const handleRefresh = () => {
    setDismissedSuggestions(new Set());
    loadSuggestions();
  };

  const visibleSuggestions = Array.isArray(suggestions)
    ? suggestions.filter(suggestion => !dismissedSuggestions.has(suggestion.id))
    : [];

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <FiLoader className="w-6 h-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading suggestions...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 dark:bg-red-900/20 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          <FiAlertCircle className="w-5 h-5 text-red-500" />
          <h3 className="font-semibold text-red-700 dark:text-red-400">
            Failed to Load Suggestions
          </h3>
        </div>
        <p className="text-red-600 dark:text-red-400 text-sm mb-3">{error}</p>
        <button
          onClick={loadSuggestions}
          className="px-3 py-1.5 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (visibleSuggestions.length === 0) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <FiUsers className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
        <h3 className="font-semibold text-gray-700 dark:text-gray-300 mb-1">
          No Suggestions Available
        </h3>
        <p className="text-gray-500 dark:text-gray-400 text-sm">
          Check back later for new suggestions
        </p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FiUsers className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Suggested for You
            </h3>
          </div>
          {showRefresh && (
            <button
              onClick={handleRefresh}
              className="p-1.5 text-gray-500 hover:text-blue-600 transition-colors"
              title="Refresh suggestions"
            >
              <FiRefreshCw className="w-4 h-4" />
            </button>
          )}
        </div>
      )}

      {/* Suggestions */}
      <div className={layout === 'card' ? 'space-y-4' : 'space-y-3'}>
        {visibleSuggestions.map((user) => {
          // user is now the direct user object from the API
          
          if (layout === 'list') {
            return (
              <div key={user.id} className="relative">
                <SocialUserListItem
                  user={user}
                  showFollowButton={true}
                  onFollowChange={handleFollowChange}
                />
                <button
                  onClick={() => handleDismiss(user.id)}
                  className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Dismiss suggestion"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
            );
          }

          // Card layout
          return (
            <div
              key={user.id}
              className="relative p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
            >
              {/* Dismiss button */}
              <button
                onClick={() => handleDismiss(user.id)}
                className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="Dismiss suggestion"
              >
                <FiX className="w-4 h-4" />
              </button>

              {/* User info */}
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold">
                  {user.profile_picture ? (
                    <img
                      src={user.profile_picture}
                      alt={user.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    user.username?.charAt(0)?.toUpperCase() || '?'
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-gray-900 dark:text-white truncate">
                    {user.username || 'Unknown User'}
                  </h4>
                  {user.user_type && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {user.user_type}
                    </span>
                  )}
                </div>
              </div>

              {/* User type badge */}
              {user.user_type && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {user.user_type.charAt(0).toUpperCase() + user.user_type.slice(1)}
                </p>
              )}

              {/* User location */}
              {user.country && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                  📍 {user.country}
                </p>
              )}

              {/* Follow button */}
              <FollowButton
                userId={user.id}
                onFollowChange={handleFollowChange}
                size="small"
                variant="primary"
                className="w-full"
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default FollowSuggestions;
