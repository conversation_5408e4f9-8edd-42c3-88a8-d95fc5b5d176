import { useState, useEffect, useRef } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { ChevronDownIcon, ChevronLeftIcon } from "@heroicons/react/24/outline";

function Sidebar({ sidebarOpen, setSidebarOpen, config = [], variant = 'default' }) {
  const location = useLocation();
  const { pathname } = location;
  const trigger = useRef(null);
  const sidebar = useRef(null);

  // Initialize sidebar as expanded by default on desktop
  const [sidebarExpanded, setSidebarExpanded] = useState(() => {
    const stored = localStorage.getItem("sidebar-expanded");
    return stored === null || stored === 'true';
  });

  // Close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!sidebar.current || !trigger.current) return;
      if (!sidebarOpen || sidebar.current.contains(target) || trigger.current.contains(target)) return;
      setSidebarOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // Close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!sidebarOpen || keyCode !== 27) return;
      setSidebarOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  // Handle sidebar expansion
  useEffect(() => {
    localStorage.setItem("sidebar-expanded", sidebarExpanded);
    const body = document.querySelector("body");
    if (sidebarExpanded) {
      body.classList.add("sidebar-expanded");
    } else {
      body.classList.remove("sidebar-expanded");
    }
  }, [sidebarExpanded]);

  // Check if a path is active
  const isPathActive = (path) => {
    if (path === "/") {
      return pathname === "/" || pathname.includes("dashboard");
    }
    return pathname.includes(path);
  };

  // Component for navigation items with children (dropdown)
  const NavItemWithChildren = ({ item, isActive }) => {
    const [isOpen, setIsOpen] = useState(isActive);

    const handleToggle = () => {
      setIsOpen(!isOpen);
      // Auto-expand sidebar when clicking on collapsed item
      if (!sidebarExpanded) {
        setSidebarExpanded(true);
      }
    };

    return (
      <li className="mb-1">
        <button
          className={`
            flex items-center justify-between w-full px-3 py-2.5 rounded-lg transition-all duration-200 text-left
            ${isActive
              ? 'bg-violet-50 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300'
              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
            }

          `}
          onClick={handleToggle}
          title={item.label}
        >
          <div className="flex items-center">
            {item.icon && (
              <item.icon className={`w-5 h-5 flex-shrink-0 ${isActive ? 'text-violet-500' : 'text-gray-400 dark:text-gray-500'}`} />
            )}
            <span className="text-sm font-medium ml-3">
              {item.label}
            </span>
          </div>
          <ChevronDownIcon
            className={`w-4 h-4 flex-shrink-0 text-gray-400 dark:text-gray-500 transition-all duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </button>

        {/* Submenu */}
        <div className="overflow-hidden transition-all duration-200">
          <ul className={`ml-6 mt-2 space-y-1 ${!isOpen ? 'hidden' : ''}`}>
            {item.children?.map((child) => (
              <li key={child.label}>
                <NavLink
                  to={child.path}
                  className={({ isActive }) =>
                    `flex items-center px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                      isActive
                        ? 'text-violet-600 dark:text-violet-400 bg-violet-50 dark:bg-violet-900/20'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                    }`
                  }
                  onClick={() => {
                    // Close mobile sidebar after navigation
                    if (window.innerWidth < 1024) {
                      setTimeout(() => setSidebarOpen(false), 150);
                    }
                  }}
                >
                  {child.label}
                </NavLink>
              </li>
            ))}
          </ul>
        </div>
      </li>
    );
  };

  // Component for simple navigation items
  const SimpleNavItem = ({ item, isActive }) => {
    const linkClasses = `
      flex items-center px-3 py-2.5 rounded-lg transition-all duration-200 w-full text-left
      ${isActive
        ? 'bg-violet-50 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300'
        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
      }

    `;

    const itemContent = (
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          {item.icon && (
            <item.icon className={`w-5 h-5 flex-shrink-0 ${isActive ? 'text-violet-500' : 'text-gray-400 dark:text-gray-500'}`} />
          )}
          <span className="text-sm font-medium ml-3">
            {item.label}
          </span>
        </div>
        {item.badge && (
          <span className="inline-flex items-center justify-center h-5 text-xs font-medium text-white bg-violet-400 px-2 rounded-sm">
            {item.badge}
          </span>
        )}
      </div>
    );

    return (
      <li>
        {item.onClick ? (
          <button
            className={linkClasses}
            onClick={() => {
              item.onClick();
              // Close mobile sidebar after action
              if (window.innerWidth < 1024) {
                setTimeout(() => setSidebarOpen(false), 150);
              }
            }}
            title={item.label}
          >
            {itemContent}
          </button>
        ) : (
          <NavLink
            to={item.path}
            className={linkClasses}
            title={item.label}
            onClick={() => {
              // Close mobile sidebar after navigation
              if (window.innerWidth < 1024) {
                setTimeout(() => setSidebarOpen(false), 150);
              }
            }}
          >
            {itemContent}
          </NavLink>
        )}
      </li>
    );
  };

  // Render navigation item based on type
  const renderNavItem = (item) => {
    const isActive = isPathActive(item.path);

    if (item.children) {
      return <NavItemWithChildren key={item.label} item={item} isActive={isActive} />;
    }

    return <SimpleNavItem key={item.label} item={item} isActive={isActive} />;
  };

  return (
    <div className="min-w-fit">
      {/* Mobile backdrop */}
      <div
        className={`fixed inset-0 bg-gray-900/50 z-40 lg:hidden transition-opacity duration-300 ${
          sidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div
        ref={sidebar}
        className={`
          flex flex-col fixed z-50 left-0 top-0 h-full bg-white dark:bg-gray-800 overflow-y-auto
          transition-all duration-300 ease-in-out
          lg:static lg:translate-x-0 lg:z-auto
          ${sidebarOpen ? "translate-x-0" : "-translate-x-full"}
          w-64
          ${variant === 'v2' ? 'border-r border-gray-200 dark:border-gray-700/60' : 'lg:rounded-r-2xl shadow-lg lg:shadow-xs'}
          p-4
        `}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          {/* Logo */}
          <NavLink
            to="/"
            className="flex items-center space-x-3 transition-all duration-200"
          >
            <svg className="fill-violet-500 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" width={32} height={32}>
              <path d="M31.956 14.8C31.372 6.92 25.08.628 17.2.044V5.76a9.04 9.04 0 0 0 9.04 9.04h5.716ZM14.8 26.24v5.716C6.92 31.372.63 25.08.044 17.2H5.76a9.04 9.04 0 0 1 9.04 9.04Zm11.44-9.04h5.716c-.584 7.88-6.876 14.172-14.756 14.756V26.24a9.04 9.04 0 0 1 9.04-9.04ZM.044 14.8C.63 6.92 6.92.628 14.8.044V5.76a9.04 9.04 0 0 1-9.04 9.04H.044Z" />
            </svg>
            <span className="font-bold text-xl text-gray-900 dark:text-white">
              EduFair
            </span>
          </NavLink>

          {/* Close button (mobile only) */}
          <button
            ref={trigger}
            className="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
            onClick={() => setSidebarOpen(false)}
          >
            <span className="sr-only">Close sidebar</span>
            <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24">
              <path d="M18.3 5.71a.996.996 0 0 0-1.41 0L12 10.59 7.11 5.7A.996.996 0 1 0 5.7 7.11L10.59 12 5.7 16.89a.996.996 0 1 0 1.41 1.41L12 13.41l4.89 4.89a.996.996 0 1 0 1.41-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z"/>
            </svg>
          </button>
        </div>

        {/* Navigation */}
        <div className="flex-1">
          <div className="mb-4">
            <h3 className="text-xs uppercase text-gray-400 dark:text-gray-500 font-semibold mb-4 px-3">
              Navigation
            </h3>
            <ul className="space-y-1">
              {config.map(renderNavItem)}
            </ul>
          </div>
        </div>

        {/* Expand/Collapse button */}
        <div className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="hidden lg:flex justify-center">
            <button
              className={`p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200 ${
                !sidebarExpanded ? 'rotate-180' : ''
              }`}
              onClick={() => setSidebarExpanded(!sidebarExpanded)}
              title={sidebarExpanded ? 'Collapse sidebar' : 'Expand sidebar'}
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
