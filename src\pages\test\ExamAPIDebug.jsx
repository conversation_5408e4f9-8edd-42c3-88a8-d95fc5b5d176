/**
 * Exam API Debug Page
 * Temporary page to debug exam API issues
 */

import React from 'react';
import QuickExamAPITest from '../../components/test/QuickExamAPITest';

const ExamAPIDebug = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Exam API Debug</h1>
          <p className="text-gray-600">
            This page helps debug exam API issues. Use this to test if the exam endpoints are working correctly.
          </p>
        </div>
        
        <QuickExamAPITest />
        
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-medium text-yellow-800 mb-2">Instructions:</h3>
          <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
            <li>Make sure you're logged in (token should be in localStorage)</li>
            <li>Click "Test API" to check if the exam endpoints are working</li>
            <li>Check the browser console for detailed logs</li>
            <li>If the API test passes, the issue might be in the ExamAttemptManager component</li>
            <li>If the API test fails, check your backend server and authentication</li>
          </ol>
        </div>

        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">Access this page:</h3>
          <p className="text-sm text-blue-700">
            Add this route to your router: <code className="bg-blue-100 px-1 rounded">/debug/exam-api</code>
          </p>
          <p className="text-sm text-blue-700 mt-1">
            Or navigate directly to: <code className="bg-blue-100 px-1 rounded">{window.location.origin}/debug/exam-api</code>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ExamAPIDebug;
