import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async thunks for email verification operations

// Send verification email
export const sendVerificationEmail = createAsyncThunk(
  'emailVerification/sendVerificationEmail',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/send-verification`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Failed to send verification email';
      return rejectWithValue(errorMessage);
    }
  }
);

// Verify email with code
export const verifyEmailCode = createAsyncThunk(
  'emailVerification/verifyEmailCode',
  async (verificationCode, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/verify-email`,
        { verification_code: verificationCode },
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Failed to verify email';
      return rejectWithValue(errorMessage);
    }
  }
);

// Resend verification email
export const resendVerificationEmail = createAsyncThunk(
  'emailVerification/resendVerificationEmail',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/auth/resend-verification`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Failed to resend verification email';
      return rejectWithValue(errorMessage);
    }
  }
);

// Check verification status
export const checkVerificationStatus = createAsyncThunk(
  'emailVerification/checkVerificationStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/api/auth/verification-status`,
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`
          }
        }
      );
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Failed to check verification status';
      return rejectWithValue(errorMessage);
    }
  }
);

// Initial state
const initialState = {
  // Verification status
  isEmailVerified: false,
  email: null,
  verificationSentAt: null,
  verifiedAt: null,
  
  // Loading states
  sendingEmail: false,
  verifyingCode: false,
  resendingEmail: false,
  checkingStatus: false,
  
  // Success states
  emailSent: false,
  emailVerified: false,
  emailResent: false,
  
  // Error states
  sendError: null,
  verifyError: null,
  resendError: null,
  statusError: null,
  
  // UI state
  showSuccessMessage: false,
  successMessage: null,
  
  // Rate limiting
  canResend: true,
  resendCooldown: 0,
};

// Email verification slice
const emailVerificationSlice = createSlice({
  name: 'emailVerification',
  initialState,
  reducers: {
    // Clear all errors
    clearErrors: (state) => {
      state.sendError = null;
      state.verifyError = null;
      state.resendError = null;
      state.statusError = null;
    },
    
    // Clear success states
    clearSuccessStates: (state) => {
      state.emailSent = false;
      state.emailVerified = false;
      state.emailResent = false;
      state.showSuccessMessage = false;
      state.successMessage = null;
    },
    
    // Set verification status manually (for when user data is updated elsewhere)
    setVerificationStatus: (state, action) => {
      state.isEmailVerified = action.payload.isEmailVerified;
      state.email = action.payload.email;
      state.verifiedAt = action.payload.verifiedAt;
    },
    
    // Update resend cooldown
    updateResendCooldown: (state, action) => {
      state.resendCooldown = action.payload;
      state.canResend = action.payload <= 0;
    },
    
    // Reset verification state (for logout)
    resetVerificationState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      // Send verification email
      .addCase(sendVerificationEmail.pending, (state) => {
        state.sendingEmail = true;
        state.sendError = null;
        state.emailSent = false;
      })
      .addCase(sendVerificationEmail.fulfilled, (state, action) => {
        state.sendingEmail = false;
        state.emailSent = true;
        state.email = action.payload.email;
        state.showSuccessMessage = true;
        state.successMessage = action.payload.message || 'Verification email sent successfully';
        // Set resend cooldown (60 seconds)
        state.canResend = false;
        state.resendCooldown = 60;
      })
      .addCase(sendVerificationEmail.rejected, (state, action) => {
        state.sendingEmail = false;
        state.sendError = action.payload;
        state.emailSent = false;
      })
      
      // Verify email code
      .addCase(verifyEmailCode.pending, (state) => {
        state.verifyingCode = true;
        state.verifyError = null;
        state.emailVerified = false;
      })
      .addCase(verifyEmailCode.fulfilled, (state, action) => {
        state.verifyingCode = false;
        state.emailVerified = true;
        state.isEmailVerified = true;
        state.email = action.payload.email;
        state.verifiedAt = action.payload.verified_at;
        state.showSuccessMessage = true;
        state.successMessage = action.payload.message || 'Email verified successfully';
      })
      .addCase(verifyEmailCode.rejected, (state, action) => {
        state.verifyingCode = false;
        state.verifyError = action.payload;
        state.emailVerified = false;
      })
      
      // Resend verification email
      .addCase(resendVerificationEmail.pending, (state) => {
        state.resendingEmail = true;
        state.resendError = null;
        state.emailResent = false;
      })
      .addCase(resendVerificationEmail.fulfilled, (state, action) => {
        state.resendingEmail = false;
        state.emailResent = true;
        state.email = action.payload.email;
        state.showSuccessMessage = true;
        state.successMessage = action.payload.message || 'Verification email resent successfully';
        // Set resend cooldown (60 seconds)
        state.canResend = false;
        state.resendCooldown = 60;
      })
      .addCase(resendVerificationEmail.rejected, (state, action) => {
        state.resendingEmail = false;
        state.resendError = action.payload;
        state.emailResent = false;
      })
      
      // Check verification status
      .addCase(checkVerificationStatus.pending, (state) => {
        state.checkingStatus = true;
        state.statusError = null;
      })
      .addCase(checkVerificationStatus.fulfilled, (state, action) => {
        state.checkingStatus = false;
        state.isEmailVerified = action.payload.is_email_verified;
        state.email = action.payload.email;
        state.verificationSentAt = action.payload.verification_sent_at;
        state.verifiedAt = action.payload.verified_at;
      })
      .addCase(checkVerificationStatus.rejected, (state, action) => {
        state.checkingStatus = false;
        state.statusError = action.payload;
      });
  },
});

// Export actions
export const {
  clearErrors,
  clearSuccessStates,
  setVerificationStatus,
  updateResendCooldown,
  resetVerificationState,
} = emailVerificationSlice.actions;

// Selectors
export const selectEmailVerification = (state) => state.emailVerification;
export const selectIsEmailVerified = (state) => state.emailVerification.isEmailVerified;
export const selectVerificationEmail = (state) => state.emailVerification.email;
export const selectSendingEmail = (state) => state.emailVerification.sendingEmail;
export const selectVerifyingCode = (state) => state.emailVerification.verifyingCode;
export const selectResendingEmail = (state) => state.emailVerification.resendingEmail;
export const selectCheckingStatus = (state) => state.emailVerification.checkingStatus;
export const selectCanResend = (state) => state.emailVerification.canResend;
export const selectResendCooldown = (state) => state.emailVerification.resendCooldown;
export const selectEmailSent = (state) => state.emailVerification.emailSent;
export const selectVerificationErrors = createSelector(
  [
    (state) => state.emailVerification.sendError,
    (state) => state.emailVerification.verifyError,
    (state) => state.emailVerification.resendError,
    (state) => state.emailVerification.statusError,
  ],
  (sendError, verifyError, resendError, statusError) => ({
    sendError,
    verifyError,
    resendError,
    statusError,
  })
);

// Export reducer
export default emailVerificationSlice.reducer;
