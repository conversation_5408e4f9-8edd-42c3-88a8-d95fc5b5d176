import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiUsers, FiSearch, FiLoader, FiAlertCircle } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';
import SocialUserListItem from '../../components/social/UserListItem';

/**
 * FollowersPage Component
 * Displays paginated list of user's followers
 */
const FollowersPage = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [followers, setFollowers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrevious, setHasPrevious] = useState(false);
  const pageSize = 20;

  const currentUserId = localStorage.getItem('userId');
  const isOwnProfile = userId === currentUserId;

  useEffect(() => {
    if (userId) {
      loadFollowers();
    }
  }, [userId, currentPage]);

  const loadFollowers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await socialFollowService.getUserFollowers(userId, currentPage, pageSize);
      
      setFollowers(response.users || []);
      setTotalCount(response.total_count || 0);
      setHasNext(response.has_next || false);
      setHasPrevious(response.has_previous || false);
    } catch (err) {
      console.error('Failed to load followers:', err);
      setError(err.message || 'Failed to load followers');
      setFollowers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing, targetUserId, user) => {
    // Update the followers list optimistically
    setFollowers(prev => prev.map(follower => 
      follower.id === targetUserId 
        ? { ...follower, isFollowing } 
        : follower
    ));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredFollowers = followers.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePreviousPage = () => {
    if (hasPrevious && currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (hasNext) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const goBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={goBack}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <FiArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          
          <div className="flex items-center gap-3">
            <FiUsers className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {isOwnProfile ? 'Your Followers' : 'Followers'}
            </h1>
            {totalCount > 0 && (
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-medium">
                {totalCount}
              </span>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search followers..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-10 pr-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <FiLoader className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600 dark:text-gray-400">Loading followers...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Failed to Load Followers
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
              <button
                onClick={loadFollowers}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredFollowers.length === 0 && (
          <div className="text-center py-12">
            <FiUsers className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {searchTerm ? 'No followers found' : 'No followers yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : isOwnProfile 
                  ? 'When people follow you, they\'ll appear here'
                  : 'This user doesn\'t have any followers yet'
              }
            </p>
          </div>
        )}

        {/* Followers List */}
        {!loading && !error && filteredFollowers.length > 0 && (
          <div className="space-y-4">
            {filteredFollowers.map((user) => (
              <SocialUserListItem
                key={user.id}
                user={user}
                showFollowButton={true}
                onFollowChange={handleFollowChange}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && !error && followers.length > 0 && (hasPrevious || hasNext) && (
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handlePreviousPage}
              disabled={!hasPrevious}
              className="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} • {totalCount} total followers
            </span>
            
            <button
              onClick={handleNextPage}
              disabled={!hasNext}
              className="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FollowersPage;

// Similar component for Following page - will create next
