import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { FiMessageCircle, FiLoader } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';
import { selectConversations } from '../../store/slices/chatSlice';

/**
 * MessageButton Component
 * Button to start a conversation with a user
 */
const MessageButton = ({
  userId,
  username,
  size = 'medium',
  variant = 'primary',
  showIcon = true,
  disabled = false,
  className = ''
}) => {
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const [loading, setLoading] = useState(false);
  
  const conversations = useSelector(selectConversations);
  const currentUserId = localStorage.getItem('userId');
  const userRole = localStorage.getItem('role') || 'student';

  // Don't show button for current user
  if (userId === currentUserId) {
    return null;
  }

  // Check if conversation already exists
  const existingConversation = conversations.find(
    conv => conv.other_user.id === userId
  );

  const handleMessageClick = async () => {
    if (disabled || loading) return;

    setLoading(true);
    
    try {
      // Navigate to chat page with user parameter
      navigate(`/${userRole}/chat?user=${userId}`);
    } catch (error) {
      console.error('Failed to open chat:', error);
    } finally {
      setLoading(false);
    }
  };

  // Size classes
  const sizeClasses = {
    small: 'px-2 py-1 text-xs',
    medium: 'px-3 py-2 text-sm',
    large: 'px-4 py-3 text-base'
  };

  // Variant classes
  const getVariantClasses = () => {
    const baseClasses = 'inline-flex items-center gap-2 rounded-lg font-medium transition-all duration-200 border';
    
    if (disabled) {
      return `${baseClasses} opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-700 text-gray-400 border-gray-200 dark:border-gray-600`;
    }

    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-blue-500 hover:bg-blue-600 text-white border-blue-500 hover:border-blue-600 shadow-sm hover:shadow-md`;
      case 'secondary':
        return `${baseClasses} ${
          currentTheme === 'dark'
            ? 'bg-gray-700 hover:bg-gray-600 text-gray-200 border-gray-600'
            : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300'
        }`;
      case 'outline':
        return `${baseClasses} bg-transparent hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-300 dark:border-blue-600 hover:border-blue-400`;
      case 'ghost':
        return `${baseClasses} bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 border-transparent`;
      default:
        return `${baseClasses} bg-blue-500 hover:bg-blue-600 text-white border-blue-500`;
    }
  };

  const buttonText = existingConversation ? 'Message' : 'Send Message';
  const IconComponent = loading ? FiLoader : FiMessageCircle;

  return (
    <button
      onClick={handleMessageClick}
      disabled={disabled || loading}
      className={`
        ${getVariantClasses()}
        ${sizeClasses[size]}
        ${className}
      `}
      title={`Send a message to ${username || 'this user'}`}
    >
      {showIcon && (
        <IconComponent 
          className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} 
        />
      )}
      <span>{buttonText}</span>
    </button>
  );
};

export default MessageButton;
