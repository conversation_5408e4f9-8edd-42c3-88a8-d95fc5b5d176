import React, { useState } from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiExternalLink,
  FiDollarSign,
  FiCreditCard,
  FiEye,
  FiUserPlus,
  FiShield,
  FiInfo
} from 'react-icons/fi';
import { format } from 'date-fns';
import { usePaymentAccess } from '../../hooks/usePaymentAccess';
import EventTicketPurchase from './EventTicketPurchase';
import EventTimingBadge from './EventTimingBadge';

const EventCard = ({
  event,
  onViewDetails,
  onRegister,
  isRegistered = false,
  showActions = true,
  variant = 'default' // 'default', 'featured', 'compact'
}) => {
  // Payment access control
  const {
    canPurchase,
    paymentAccess,
    getPaymentButtonConfig,
    isEventOrganizer,
    isAdmin
  } = usePaymentAccess(event);

  // Local state for payment modal
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const {
    id,
    title,
    description,
    short_description,
    // Handle both API format and legacy format for dates
    start_datetime,
    start_date = start_datetime,
    end_datetime,
    end_date = end_datetime,
    // Handle both API format and legacy format for images
    banner_image_url,
    image_url = banner_image_url,
    category,
    location,
    venue_address,
    // Handle both API format and legacy format for participants
    max_attendees,
    max_participants = max_attendees,
    current_participants = 0, // Default since API doesn't return this yet
    registration_fee = 0, // Default since API doesn't return this yet
    currency = 'ZAR',
    status,
    // Handle both API format and legacy format for registration deadline
    registration_end,
    registration_deadline = registration_end,
    organizer_name,
    organizer_id,
    tags,
    requirements,
    agenda,
    is_featured,
    is_competition,
    is_public
  } = event;

  const isRegistrationOpen = registration_deadline ? new Date() < new Date(registration_deadline) : true;
  const isFull = max_participants ? current_participants >= max_participants : false;
  const attendancePercentage = max_participants ? (current_participants / max_participants) * 100 : 0;
  const requiresPayment = registration_fee && registration_fee > 0;
  // Handle both uppercase (API) and lowercase (legacy) status values
  const normalizedStatus = status?.toLowerCase();
  const isUpcoming = normalizedStatus === 'upcoming' || status === 'PUBLISHED';
  const isOngoing = normalizedStatus === 'ongoing';
  const isCompleted = normalizedStatus === 'completed' || status === 'COMPLETED';
  const isCancelled = normalizedStatus === 'cancelled' || status === 'CANCELLED';
  const isDraft = normalizedStatus === 'draft' || status === 'DRAFT';

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const formatCurrency = (amount, currencyCode = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currencyCode
    }).format(amount);
  };

  const handleRegister = (e) => {
    e.stopPropagation();
    if (onRegister && !isRegistered && !isFull && isRegistrationOpen) {
      onRegister(event);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(event);
    }
  };

  const handlePaymentClick = (e) => {
    e.stopPropagation();
    setShowPaymentModal(true);
  };

  const renderActionButton = () => {
    // Admin access - show info badge
    if (isAdmin(event)) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-800 bg-blue-100 rounded-md">
          <FiShield className="h-4 w-4 mr-1" />
          Admin Access
        </span>
      );
    }

    // Event organizer - show organizer badge
    if (isEventOrganizer(event)) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-purple-800 bg-purple-100 rounded-md">
          <FiInfo className="h-4 w-4 mr-1" />
          Event Organizer
        </span>
      );
    }

    // Already registered
    if (isRegistered) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-md">
          <FiUsers className="h-4 w-4 mr-1" />
          Registered
        </span>
      );
    }

    // Registration closed
    if (!isRegistrationOpen) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
          <FiClock className="h-4 w-4 mr-1" />
          Registration Closed
        </span>
      );
    }

    // Event full
    if (isFull) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-800 bg-red-100 rounded-md">
          <FiUsers className="h-4 w-4 mr-1" />
          Full
        </span>
      );
    }

    // Can purchase tickets
    if (canPurchase && isRegistrationOpen && !isFull) {
      return (
        <button
          onClick={handlePaymentClick}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
        >
          <FiCreditCard className="h-4 w-4 mr-1" />
          {requiresPayment ? 'Buy Tickets' : 'Register'}
        </button>
      );
    }

    // Cannot purchase - show reason
    if (paymentAccess && !paymentAccess.canPurchase) {
      return (
        <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-yellow-800 bg-yellow-100 rounded-md">
          <FiInfo className="h-4 w-4 mr-1" />
          {paymentAccess.reason === 'role_restricted' ? 'Not Available' : 'Access Restricted'}
        </span>
      );
    }

    // Fallback - regular register button
    return (
      <button
        onClick={handleRegister}
        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <FiUserPlus className="h-4 w-4 mr-1" />
        Register
      </button>
    );
  };

  if (variant === 'compact') {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleViewDetails}
      >
        <div className="flex items-start space-x-3">
          {image_url && (
            <img
              src={image_url}
              alt={title}
              className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
            />
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <EventTimingBadge event={event} size="small" showTimeRemaining={false} />
              {is_featured && (
                <FiStar className="h-4 w-4 text-yellow-500 fill-current" />
              )}
              {is_competition && (
                <FiAward className="h-4 w-4 text-purple-500" />
              )}
              {category && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {typeof category === 'string' ? category : category.name}
                </span>
              )}
            </div>
            <h3 className="text-sm font-semibold text-gray-900 truncate">{title}</h3>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <FiCalendar className="h-3 w-3 mr-1" />
              {formatDate(start_datetime)}
              <FiClock className="h-3 w-3 ml-2 mr-1" />
              {formatTime(start_datetime)}
            </div>
            {requiresPayment && (
              <div className="flex items-center text-xs text-green-600 mt-1 font-medium">
                <FiDollarSign className="h-3 w-3 mr-1" />
                {formatCurrency(registration_fee, currency)}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer ${
        variant === 'featured' ? 'ring-2 ring-yellow-400' : ''
      }`}
      onClick={handleViewDetails}
    >
      {/* Event Image */}
      {image_url && (
        <div className="relative h-48 bg-gray-200">
          <img
            src={image_url}
            alt={title}
            className="w-full h-full object-cover"
          />
          {is_featured && (
            <div className="absolute top-2 left-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <FiStar className="h-3 w-3 mr-1 fill-current" />
                Featured
              </span>
            </div>
          )}
          {is_competition && (
            <div className="absolute top-2 right-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <FiAward className="h-3 w-3 mr-1" />
                Competition
              </span>
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {/* Event Status and Category */}
        <div className="flex items-center justify-between mb-3">
          <EventTimingBadge event={event} variant="detailed" size="default" />
          {category && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {category.name}
            </span>
          )}
        </div>

        {/* Title and Description */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{short_description}</p>

        {/* Event Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <FiCalendar className="h-4 w-4 mr-2" />
            {formatDate(start_datetime)} - {formatDate(end_datetime)}
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <FiClock className="h-4 w-4 mr-2" />
            {start_date && formatTime(start_date)} {start_date && end_date && ' - '} {end_date && formatTime(end_date)}
          </div>
          {location && (
            <div className="flex items-center text-sm text-gray-500">
              <FiMapPin className="h-4 w-4 mr-2" />
              {location.name}
            </div>
          )}
          {requiresPayment && (
            <div className="flex items-center text-sm text-green-600 font-medium">
              <FiDollarSign className="h-4 w-4 mr-2" />
              {formatCurrency(registration_fee, currency)}
            </div>
          )}
        </div>

        {/* Attendance */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span className="flex items-center">
              <FiUsers className="h-4 w-4 mr-1" />
              {current_participants} / {max_participants || 'Unlimited'} attendees
            </span>
            <span>{Math.round(attendancePercentage)}% full</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                attendancePercentage >= 90 ? 'bg-red-500' : 
                attendancePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(attendancePercentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between">
            <button
              onClick={handleViewDetails}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              <FiEye className="h-4 w-4 mr-1" />
              View Details
            </button>

            {/* Payment Access Control */}
            {renderActionButton()}
          </div>
        )}

        {/* Payment Modal */}
        {showPaymentModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <EventTicketPurchase
              event={event}
              onClose={() => setShowPaymentModal(false)}
              onSuccess={(payment) => {
                setShowPaymentModal(false);
                // Handle successful payment
                console.log('Payment successful:', payment);
              }}
              onError={(error) => {
                console.error('Payment error:', error);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default EventCard;
