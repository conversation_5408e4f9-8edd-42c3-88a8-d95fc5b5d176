import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchClasses } from '../../store/slices/ClassesSlice';
import ExamCreationWizard from '../../components/exam/ExamCreationWizard';

const InstituteCreateExam = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Extract exam ID from URL path for editing
  const pathParts = location.pathname.split('/');
  const examId = pathParts.includes('edit') ? pathParts[pathParts.indexOf('edit') - 1] : null;
  const isEditing = !!examId;

  console.log('🎯 InstituteCreateExam - URL:', location.pathname);
  console.log('🎯 InstituteCreateExam - Extracted examId:', examId);
  console.log('🎯 InstituteCreateExam - isEditing:', isEditing);

  useEffect(() => {
    // Load necessary data (same as teacher)
    dispatch(fetchAllOwnClasses());
    dispatch(fetchSubjects());
    dispatch(fetchClasses());
  }, [dispatch]);

  // Override the success navigation to go to institute exams
  const handleSuccess = () => {
    navigate('/institute/exams');
  };

  return (
    <ExamCreationWizard 
      examId={examId} 
      isEditing={isEditing}
      onSuccess={handleSuccess}
      userType="institute" // Pass user type for any institute-specific logic
    />
  );
};

export default InstituteCreateExam;
