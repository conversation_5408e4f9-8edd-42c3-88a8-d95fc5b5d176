/* Chat Page Instagram-like Enhancements */

/* Followers/Following List Styling */
.followers-following-list {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dark .followers-following-list {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

/* User Item Hover Effects */
.user-item {
  transition: all 0.2s ease-in-out;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.user-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .user-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Avatar Enhancements */
.user-avatar {
  position: relative;
  transition: all 0.3s ease;
}

.user-avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-item:hover .user-avatar::after {
  opacity: 1;
}

/* Tab Styling */
.chat-tabs {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .chat-tabs {
  background: rgba(31, 41, 55, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.chat-tab {
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.chat-tab.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.chat-tab:not(.active):hover {
  background: rgba(59, 130, 246, 0.1);
}

/* Search Input Enhancements */
.search-input {
  transition: all 0.3s ease;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.dark .search-input {
  background: rgba(31, 41, 55, 0.9);
}

.search-input:focus {
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

/* Message Button Styling */
.message-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.message-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* User Type Badge Enhancements */
.user-type-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.user-type-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.user-item:hover .user-type-badge::before {
  left: 100%;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Empty State Styling */
.empty-state {
  text-align: center;
  padding: 2rem;
  opacity: 0.7;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Scroll Enhancements */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Header Button Enhancements */
.header-button {
  transition: all 0.2s ease;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.header-button:hover {
  transform: scale(1.05);
}

.header-button.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.header-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.header-button:active::before {
  width: 100px;
  height: 100px;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .user-item {
    padding: 12px;
  }
  
  .user-avatar {
    width: 40px;
    height: 40px;
  }
  
  .chat-tabs {
    padding: 2px;
  }
  
  .search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Accessibility Enhancements */
.user-item:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.chat-tab:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.header-button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .user-item,
  .user-avatar,
  .chat-tab,
  .search-input,
  .message-button,
  .header-button {
    transition: none;
  }
  
  .loading-shimmer {
    animation: none;
  }
}
