import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FiEdit,
  FiBarChart2,
  <PERSON><PERSON><PERSON>,
  FiArrowLeft
} from 'react-icons/fi';
import MentorEvaluationDashboard from '../../components/mentor/MentorEvaluationDashboard';
import CompetitionSubmissionsList from '../../components/mentor/CompetitionSubmissionsList';
import SubmissionMarkingInterface from '../../components/mentor/SubmissionMarkingInterface';
import CompetitionRankings from '../../components/mentor/CompetitionRankings';
import CertificateGenerationInterface from '../../components/mentor/CertificateGenerationInterface';
import CertificateViewer from '../../components/mentor/CertificateViewer';
import MentorEvaluationErrorBoundary from '../../components/mentor/MentorEvaluationErrorBoundary';
import MentorEvaluationNotifications, { useMentorEvaluationNotifications } from '../../components/mentor/MentorEvaluationNotifications';

const MentorEvaluationPage = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCompetition, setSelectedCompetition] = useState(null);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [certificateModal, setCertificateModal] = useState({
    isOpen: false,
    participant: null,
    position: null
  });
  const [certificateViewer, setCertificateViewer] = useState({
    isOpen: false,
    certificate: null
  });

  // Notifications
  const {
    notifications,
    dismissNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  } = useMentorEvaluationNotifications();

  const handleCompetitionSelect = (competition) => {
    setSelectedCompetition(competition);
    setActiveTab('submissions');
  };

  const handleSubmissionSelect = (submission) => {
    setSelectedSubmission(submission);
    setActiveTab('marking');
  };

  const handleSubmissionMarked = () => {
    // Show success notification
    showSuccess('Submission marked successfully!', 'Marking Complete');

    // Refresh submissions list and go back
    setSelectedSubmission(null);
    setActiveTab('submissions');
  };

  const handleBackToSubmissions = () => {
    setSelectedSubmission(null);
    setActiveTab('submissions');
  };

  const handleBackToDashboard = () => {
    setSelectedCompetition(null);
    setSelectedSubmission(null);
    setActiveTab('dashboard');
  };

  const handleViewRankings = () => {
    setActiveTab('rankings');
  };

  const handleGenerateCertificate = (participant, position) => {
    setCertificateModal({
      isOpen: true,
      participant,
      position
    });
  };

  const handleViewCertificate = (certificate) => {
    setCertificateViewer({
      isOpen: true,
      certificate
    });
  };

  const handleCertificateGenerated = (certificateData) => {
    console.log('Certificate generated:', certificateData);
    showSuccess(
      `Certificate generated successfully for ${certificateData.student_name || 'participant'}!`,
      'Certificate Generated',
      `Certificate ID: ${certificateData.certificate_id}`
    );
    setCertificateModal({ isOpen: false, participant: null, position: null });
  };

  const TabButton = ({ id, label, icon: Icon, isActive, onClick, disabled = false }) => (
    <button
      onClick={() => !disabled && onClick(id)}
      disabled={disabled}
      className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-700 border border-blue-200'
          : disabled
          ? 'text-gray-400 cursor-not-allowed'
          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
      }`}
    >
      <Icon className="h-4 w-4 mr-2" />
      {label}
    </button>
  );

  const renderBreadcrumb = () => {
    const breadcrumbs = [];
    
    if (activeTab === 'dashboard') {
      breadcrumbs.push({ label: 'Evaluation Dashboard', active: true });
    } else if (activeTab === 'submissions') {
      breadcrumbs.push(
        { label: 'Dashboard', onClick: handleBackToDashboard },
        { label: selectedCompetition?.competition_title || 'Submissions', active: true }
      );
    } else if (activeTab === 'marking') {
      breadcrumbs.push(
        { label: 'Dashboard', onClick: handleBackToDashboard },
        { label: 'Submissions', onClick: handleBackToSubmissions },
        { label: 'Mark Submission', active: true }
      );
    } else if (activeTab === 'rankings') {
      breadcrumbs.push(
        { label: 'Dashboard', onClick: handleBackToDashboard },
        { label: selectedCompetition?.competition_title || 'Rankings', active: true }
      );
    }

    return (
      <nav className="flex mb-6" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          {breadcrumbs.map((crumb, index) => (
            <li key={index} className="inline-flex items-center">
              {index > 0 && (
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {crumb.active ? (
                <span className="text-gray-500 text-sm font-medium">{crumb.label}</span>
              ) : (
                <button
                  onClick={crumb.onClick}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  {crumb.label}
                </button>
              )}
            </li>
          ))}
        </ol>
      </nav>
    );
  };

  return (
    <MentorEvaluationErrorBoundary>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentor Evaluation System</h1>
          <p className="text-gray-600 mt-1">Evaluate competition submissions and manage rankings</p>
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      {renderBreadcrumb()}

      {/* Navigation Tabs */}
      <div className="flex space-x-2 border-b border-gray-200 pb-4">
        <TabButton
          id="dashboard"
          label="Dashboard"
          icon={FiHome}
          isActive={activeTab === 'dashboard'}
          onClick={setActiveTab}
        />
        
        <TabButton
          id="submissions"
          label="Submissions"
          icon={FiList}
          isActive={activeTab === 'submissions'}
          onClick={setActiveTab}
          disabled={!selectedCompetition}
        />
        
        <TabButton
          id="marking"
          label="Mark Submission"
          icon={FiEdit}
          isActive={activeTab === 'marking'}
          onClick={setActiveTab}
          disabled={!selectedSubmission}
        />
        
        <TabButton
          id="rankings"
          label="Rankings"
          icon={FiBarChart2}
          isActive={activeTab === 'rankings'}
          onClick={setActiveTab}
          disabled={!selectedCompetition}
        />
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {activeTab === 'dashboard' && (
          <MentorEvaluationDashboard onCompetitionSelect={handleCompetitionSelect} />
        )}

        {activeTab === 'submissions' && selectedCompetition && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {selectedCompetition.competition_title}
                </h2>
                <p className="text-sm text-gray-600">
                  Progress: {selectedCompetition.checked_submissions}/{selectedCompetition.total_submissions} submissions evaluated
                </p>
              </div>
              <button
                onClick={handleViewRankings}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiBarChart2 className="h-4 w-4 mr-2" />
                View Rankings
              </button>
            </div>
            <CompetitionSubmissionsList
              competitionId={selectedCompetition.competition_id}
              onSubmissionSelect={handleSubmissionSelect}
            />
          </div>
        )}

        {activeTab === 'marking' && selectedSubmission && (
          <SubmissionMarkingInterface
            attemptId={selectedSubmission.attempt_id}
            onBack={handleBackToSubmissions}
            onSubmissionMarked={handleSubmissionMarked}
          />
        )}

        {activeTab === 'rankings' && selectedCompetition && (
          <CompetitionRankings
            competitionId={selectedCompetition.competition_id}
            onGenerateCertificate={handleGenerateCertificate}
            onViewCertificate={handleViewCertificate}
          />
        )}
      </div>

      {/* Certificate Generation Modal */}
      <CertificateGenerationInterface
        competitionId={selectedCompetition?.competition_id}
        participant={certificateModal.participant}
        position={certificateModal.position}
        isOpen={certificateModal.isOpen}
        onClose={() => setCertificateModal({ isOpen: false, participant: null, position: null })}
        onCertificateGenerated={handleCertificateGenerated}
      />

      {/* Certificate Viewer Modal */}
      <CertificateViewer
        isOpen={certificateViewer.isOpen}
        onClose={() => setCertificateViewer({ isOpen: false, certificate: null })}
        certificate={certificateViewer.certificate}
      />

      {/* Notifications */}
      <MentorEvaluationNotifications
        notifications={notifications}
        onDismiss={dismissNotification}
      />
    </div>
    </MentorEvaluationErrorBoundary>
  );
};

export default MentorEvaluationPage;
