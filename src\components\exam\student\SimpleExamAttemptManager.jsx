/**
 * Simple Exam Attempt Manager
 * Simplified version without Redux dependencies for debugging
 */

import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { FiAlertTriangle } from 'react-icons/fi';

// Redux actions
import {
  requestExamSession,
  setSessionId,
  setSessionStatus,
  clearSession,
  setRemainingTime
} from '../../../store/slices/exam/examSessionSlice';
import {
  getExamDataBySession,
  clearSessionData
} from '../../../store/slices/exam/examSessionDataSlice';

// Import components
import ExamStartScreen from './ExamStartScreen';
import ExamInterface from './ExamInterface/ExamInterface';
import DirectExamInterface from './DirectExamInterface';

const SimpleExamAttemptManager = ({
  examId,
  examData = null,
  skipAPICall = false,
  competitionMode = false,
  competitionEvent = null,
  onBackToExams = null
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Redux state
  const examSession = useSelector(state => state.examSession);
  const examSessionData = useSelector(state => state.examSessionData);

  // Local state only
  const [currentPhase, setCurrentPhase] = useState(
    skipAPICall && examData ? 'active' : 'start'
  ); // 'start', 'loading', 'active', 'error'
  const [error, setError] = useState(null);
  const [sessionData, setSessionData] = useState(
    skipAPICall && examData ? {
      session_id: examData.session_id,
      exam_id: examId,
      examData: examData,
      sessionStatus: 'active',
      currentAnswers: {}
    } : null
  );
  const [isStarting, setIsStarting] = useState(false);
  const [violationCount, setViolationCount] = useState(0);
  const [maxViolations] = useState(3); // Maximum violations allowed

  /**
   * Handle exam start - simplified version
   */
  const handleStartExam = useCallback(async () => {
    try {
      setIsStarting(true);
      setCurrentPhase('loading');
      setError(null);

      // If we already have exam data, skip API calls but still initialize Redux
      if (skipAPICall && examData) {
        console.log('🎯 [SIMPLE] Using existing exam data, initializing Redux state');

        // Initialize Redux state with provided data
        dispatch(setSessionId(examData.session_id));
        dispatch(setSessionStatus('active'));
        dispatch(setRemainingTime(examData.remaining_time_seconds || 0));

        const combinedData = {
          session_id: examData.session_id,
          exam_id: examId,
          examData: examData,
          sessionStatus: 'active',
          currentAnswers: {}
        };
        setSessionData(combinedData);

        setCurrentPhase('active');
        setIsStarting(false);

        // Activate security measures AFTER exam becomes active
        setTimeout(async () => {
          console.log('🔒 [SIMPLE] Activating security measures...');
          try {
            const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
            AntiCheatService.default.activate();
            // Mark exam as started for anti-cheat service
            AntiCheatService.default.isExamStarted = true;
            console.log('✅ [SIMPLE] Anti-cheat service activated');
          } catch (error) {
            console.warn('⚠️ [SIMPLE] Could not activate anti-cheat service:', error);
          }
        }, 1000);

        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('🎯 [SIMPLE] Starting exam session via Redux for exam:', examId);

      // Step 1: Request session ID using Redux action
      console.log('📡 [SIMPLE] Step 1: Requesting session ID via Redux...');
      const sessionResult = await dispatch(requestExamSession({ examId })).unwrap();
      const sessionId = sessionResult.sessionId;

      console.log('✅ [SIMPLE] Step 1 Complete: Got session ID via Redux:', sessionId);

      // Step 2: Connect WebSocket first
      console.log('🔌 [SIMPLE] Step 2: Connecting WebSocket...');
      try {
        const ExamWebSocketService = await import('../../../services/exam/websocket/ExamWebSocketService.js');
        await ExamWebSocketService.default.connect(sessionId, token);

        // Set up disconnection monitoring during exam
        const handleWebSocketDisconnection = () => {
          console.log('🚨 [SIMPLE] WebSocket disconnected during exam - redirecting to safe state');
          setError('Connection lost during exam. Please contact support.');
          setCurrentPhase('error');
        };

        const handleWebSocketError = (errorData) => {
          console.log('🚨 [SIMPLE] WebSocket error during exam:', errorData);
          if (errorData.code === 'MAX_RECONNECT_ATTEMPTS' || errorData.code === 'POLICY_VIOLATION') {
            setError('Connection error during exam. Please contact support.');
            setCurrentPhase('error');
          }
        };

        // Add listeners for disconnection monitoring
        ExamWebSocketService.default.on('disconnected', handleWebSocketDisconnection);
        ExamWebSocketService.default.on('error', handleWebSocketError);

        console.log('✅ [SIMPLE] Step 2 Complete: WebSocket connected successfully');
      } catch (wsError) {
        console.error('❌ [SIMPLE] WebSocket connection failed:', wsError);
        throw new Error('Failed to establish secure connection. Please try again.');
      }

      // Step 3: Get exam data using Redux action (only after WebSocket is connected)
      console.log('📚 [SIMPLE] Step 3: Getting exam data via Redux...');
      const examDataResult = await dispatch(getExamDataBySession({ sessionId })).unwrap();
      const examDataResponse = examDataResult.examData;

      console.log('✅ [SIMPLE] Step 3 Complete: Got exam data via Redux:', examDataResponse);

      // Store session data for the interface
      const combinedData = {
        session_id: sessionId,
        exam_id: examId,
        examData: examDataResponse,
        sessionStatus: 'active',
        currentAnswers: {}
      };
      setSessionData(combinedData);
      
      console.log('🎉 [SIMPLE] Exam setup complete, transitioning to active phase');

      // Transition to active exam phase first
      setCurrentPhase('active');

      // Activate security measures AFTER exam becomes active
      console.log('🔒 [SIMPLE] Activating security measures...');
      setTimeout(async () => {
        try {
          const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
          AntiCheatService.default.activate();
          // Mark exam as started for anti-cheat service
          AntiCheatService.default.isExamStarted = true;
          console.log('✅ [SIMPLE] Anti-cheat service activated');
        } catch (error) {
          console.warn('⚠️ [SIMPLE] Could not activate anti-cheat service:', error);
        }
      }, 1000); // Delay to ensure component is fully rendered

    } catch (error) {
      console.error('❌ [SIMPLE] Failed to start exam:', error);
      setError(error.message);
      setCurrentPhase('error');
    } finally {
      setIsStarting(false);
    }
  }, [examId, skipAPICall, examData, dispatch]);

  /**
   * Handle exam submission - simplified
   */
  const handleExamSubmit = useCallback(async (isAutoSubmit = false) => {
    try {
      console.log('📤 [SIMPLE] Submitting exam...', { isAutoSubmit });
      // For now, just navigate away
      if (onBackToExams) {
        onBackToExams();
      } else {
        navigate('/student/exams');
      }
      return true;
    } catch (error) {
      console.error('❌ [SIMPLE] Error during exam submission:', error);
      throw error;
    }
  }, [onBackToExams, navigate]);

  /**
   * Handle answer changes - simplified
   */
  const handleAnswerChange = useCallback((questionId, answer) => {
    console.log('📝 [SIMPLE] Answer changed:', { questionId, answer });
  }, []);

  /**
   * Handle back to exams
   */
  const handleBackToExams = useCallback(() => {
    if (onBackToExams) {
      onBackToExams();
    } else {
      navigate('/student/exams');
    }
  }, [onBackToExams, navigate]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      console.log('🧹 [SIMPLE] Cleaning up SimpleExamAttemptManager');

      // Clear Redux state
      dispatch(clearSession());
      dispatch(clearSessionData());

      // Permanently disconnect WebSocket to prevent reconnections
      try {
        import('../../../services/exam/websocket/ExamWebSocketService.js').then(module => {
          module.default.permanentDisconnect();
          console.log('✅ WebSocket permanently disconnected on cleanup');
        });
      } catch (error) {
        console.warn('⚠️ Could not disconnect WebSocket on cleanup:', error);
      }

      // Deactivate anti-cheat measures
      try {
        import('../../../services/exam/security/AntiCheatService.js').then(module => {
          module.default.deactivate();
          console.log('✅ Anti-cheat service deactivated on cleanup');
        });
      } catch (error) {
        console.warn('⚠️ Could not deactivate anti-cheat on cleanup:', error);
      }
    };
  }, [dispatch]);

  // Render based on current phase
  switch (currentPhase) {
    case 'start':
      return (
        <ExamStartScreen
          exam={examData}
          onStartExam={handleStartExam}
          onBackToExams={handleBackToExams}
          isStarting={isStarting}
        />
      );

    case 'loading':
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Preparing Exam Environment</h2>
            <p className="text-gray-600 mb-4">Setting up your exam session...</p>
            <div className="text-sm text-gray-500">
              <p>• Requesting session ID</p>
              <p>• Loading exam data</p>
              <p>• Preparing interface</p>
            </div>
            <div className="mt-4 space-x-2">
              <button
                onClick={handleBackToExams}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('🔄 [SIMPLE] Force retry...');
                  handleStartExam();
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      );

    case 'active':
      if (!sessionData) {
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <p>No session data available</p>
              <button
                onClick={handleStartExam}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg"
              >
                Retry
              </button>
            </div>
          </div>
        );
      }

      // Use the DirectExamInterface for a clean, simple exam experience
      return (
        <DirectExamInterface
          examData={sessionData.examData}
          examId={examId}
          onBackToExams={handleBackToExams}
        />
      );

    case 'error':
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center max-w-md">
            <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Start Exam</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex space-x-3 justify-center">
              <button
                onClick={handleStartExam}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Try Again
              </button>
              <button
                onClick={handleBackToExams}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Back to Exams
              </button>
            </div>
          </div>
        </div>
      );

    default:
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <p>Unknown state: {currentPhase}</p>
            <button
              onClick={() => setCurrentPhase('start')}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg"
            >
              Reset
            </button>
          </div>
        </div>
      );
  }
};



export default SimpleExamAttemptManager;
