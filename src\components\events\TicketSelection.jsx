/**
 * TicketSelection Component
 * 
 * Handles ticket selection and booking for events with PayFast integration
 */

import React, { useState, useEffect } from 'react';
import {
  FiCreditCard,
  FiUsers,
  FiCheck,
  FiMinus,
  FiPlus,
  FiShield,
  FiInfo
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../ui';

import newEventService from '../../services/newEventService';

/**
 * TicketSelection Component
 *
 * ✅ CORRECT FLOW: Register first, then buy ticket
 *
 * Flow:
 * 1. User registers for event (creates registration record)
 * 2. If paid ticket → User can purchase ticket later
 * 3. If free ticket → Registration is immediately confirmed
 *
 * API: POST /api/events/registrations/
 */
const TicketSelection = ({ event, onBookingSuccess, onBookingError }) => {
  const { showSuccess, showError } = useNotification();

  // Get user data from localStorage
  const getUserData = () => {
    try {
      const userdata = localStorage.getItem('userdata');
      return userdata ? JSON.parse(userdata) : null;
    } catch (error) {
      console.error('Error parsing userdata from localStorage:', error);
      return null;
    }
  };

  // Local state
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);
  const [attendeeInfo, setAttendeeInfo] = useState({
    name: '',
    email: '',
    phone: '',
    dietary_requirements: '',
    emergency_contact: ''
  });



  // Pre-fill attendee info from localStorage userdata
  useEffect(() => {
    const userData = getUserData();
    if (userData) {
      setAttendeeInfo({
        name: userData.username || '',
        email: userData.email || '',
        phone: userData.mobile || '',
        dietary_requirements: '',
        emergency_contact: ''
      });
    }
  }, []);

  // Calculate total amount
  const totalAmount = selectedTicket ? selectedTicket.price * quantity : 0;
  const currency = selectedTicket?.currency || 'PKR';

  // Handle ticket selection
  const handleTicketSelect = (ticket) => {
    setSelectedTicket(ticket);
    setQuantity(1); // Reset quantity when changing tickets
  };

  // Handle quantity change
  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= (selectedTicket?.max_quantity || 10)) {
      setQuantity(newQuantity);
    }
  };

  // Handle attendee info change
  const handleAttendeeInfoChange = (field, value) => {
    setAttendeeInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form
  const validateForm = () => {
    if (!selectedTicket) {
      showError('Please select a ticket type');
      return false;
    }

    // Get fresh user data from localStorage
    const userData = getUserData();
    if (!userData) {
      showError('Please log in to register for this event');
      return false;
    }

    // Validate name (from userdata)
    const userName = attendeeInfo.name || userData.username;
    if (!userName || !userName.trim()) {
      showError('Your profile is missing a username. Please update your profile first.');
      return false;
    }

    // Validate email (from userdata)
    const userEmail = attendeeInfo.email || userData.email;
    if (!userEmail || !userEmail.trim()) {
      showError('Your profile is missing an email. Please update your profile first.');
      return false;
    }

    return true;
  };

  // Handle booking
  const handleBooking = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const registrationData = {
        ticket_id: selectedTicket.id,
        quantity: quantity,
        attendee_info: {
          name: attendeeInfo.name,
          email: attendeeInfo.email,
          phone: attendeeInfo.phone
        },
        special_requirements: attendeeInfo.dietary_requirements || '',
        emergency_contact: {
          name: attendeeInfo.emergency_contact || '',
          phone: attendeeInfo.emergency_contact || ''
        }
      };

      // Use the correct registration API endpoint
      const registration = await newEventService.registerForEvent(event.id, registrationData);

      if (registration.status === 'PENDING_PAYMENT' || registration.payment_status === 'PENDING') {
        // Paid ticket - registration created, payment needed later
        showSuccess('✅ Registration successful! Redirecting to My Events...');
        if (onBookingSuccess) onBookingSuccess(registration);
      } else {
        // Free ticket - registration complete
        showSuccess('✅ Registration confirmed! Redirecting to My Events...');
        if (onBookingSuccess) onBookingSuccess(registration);
      }

    } catch (error) {
      console.error('Booking failed:', error);
      showError(error.message || 'Booking failed. Please try again.');
      if (onBookingError) onBookingError(error);
    } finally {
      setLoading(false);
    }
  };




  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (!event?.tickets || event.tickets.length === 0) {
    return (
      <div className="bg-gray-50 rounded-lg p-6 text-center">
        <FiInfo className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-600">No tickets available for this event</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Select Tickets</h3>

      {/* Ticket Options */}
      <div className="space-y-4 mb-6">
        {event.tickets.map((ticket) => (
          <div
            key={ticket.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedTicket?.id === ticket.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleTicketSelect(ticket)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                    selectedTicket?.id === ticket.id
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedTicket?.id === ticket.id && (
                      <FiCheck className="w-2 h-2 text-white m-0.5" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{ticket.name}</h4>
                    <p className="text-sm text-gray-600">{ticket.description}</p>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900">
                  {ticket.price === 0 ? 'Free' : formatCurrency(ticket.price)}
                </p>
                <p className="text-sm text-gray-500">
                  {ticket.available_quantity} available
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quantity Selection */}
      {selectedTicket && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Quantity
          </label>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiMinus className="w-4 h-4" />
            </button>
            <span className="px-4 py-2 border border-gray-300 rounded-md min-w-[60px] text-center">
              {quantity}
            </span>
            <button
              onClick={() => handleQuantityChange(1)}
              disabled={quantity >= (selectedTicket?.max_quantity || 10)}
              className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiPlus className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Attendee Information */}
      {selectedTicket && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-4">Attendee Information</h4>

          {/* User Info Display */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <FiUsers className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">
                  {attendeeInfo.name || 'Name not available'}
                </p>
                <p className="text-sm text-gray-600">
                  {attendeeInfo.email || 'Email not available'}
                </p>
                {attendeeInfo.phone && (
                  <p className="text-sm text-gray-600">{attendeeInfo.phone}</p>
                )}
              </div>
              <div className="text-green-600">
                <FiCheck className="w-5 h-5" />
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Using information from your account. Need to update? Visit your profile settings.
            </p>
          </div>

          {/* Optional Additional Information */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Special Requirements (Optional)
              </label>
              <textarea
                value={attendeeInfo.dietary_requirements}
                onChange={(e) => handleAttendeeInfoChange('dietary_requirements', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Any dietary requirements, accessibility needs, or special requests"
              />
            </div>
          </div>
        </div>
      )}

      {/* Total and Book Button */}
      {selectedTicket && (
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-gray-900">
                {totalAmount === 0 ? 'Free' : formatCurrency(totalAmount)}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">{quantity} × {selectedTicket.name}</p>
              <p className="text-sm text-gray-500">
                {totalAmount === 0 ? 'No payment required' : 'Register now, pay later from My Registrations'}
              </p>
            </div>
          </div>

          <button
            onClick={handleBooking}
            disabled={loading || !selectedTicket}
            className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : totalAmount === 0 ? (
              <FiCheck className="w-5 h-5 mr-2" />
            ) : (
              <FiCreditCard className="w-5 h-5 mr-2" />
            )}
            {loading
              ? 'Processing...'
              : totalAmount === 0
                ? 'Register for Free Event'
                : 'Register (Pay Later)'
            }
          </button>

          {totalAmount > 0 && (
            <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
              <FiShield className="w-4 h-4 mr-1" />
              Secure payment powered by PayFast
            </div>
          )}
        </div>
      )}


    </div>
  );
};

export default TicketSelection;
