import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiUser, FiMail, FiMapPin, FiCalendar, FiLoader, FiAlertCircle } from 'react-icons/fi';
import UserSocialStats from '../../components/social/UserSocialStats';
import FollowSuggestions from '../../components/social/FollowSuggestions';

/**
 * UserProfilePage Component
 * Enhanced user profile page with social features
 */
const UserProfilePage = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const currentUserId = localStorage.getItem('userId');
  const isOwnProfile = userId === currentUserId;

  useEffect(() => {
    if (userId) {
      loadUserProfile();
    }
  }, [userId]);

  // Mock function to load user profile - replace with actual API call
  const loadUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock user data - replace with actual API call
      const mockUser = {
        id: userId,
        username: 'john_doe',
        email: '<EMAIL>',
        user_type: 'student',
        profile_picture: null,
        country: 'USA',
        bio: 'Passionate learner interested in technology and education. Always looking to connect with like-minded individuals.',
        joined_date: '2024-01-15T00:00:00Z',
        is_email_verified: true,
        last_active: '2024-09-12T10:30:00Z'
      };

      setUserProfile(mockUser);
    } catch (err) {
      console.error('Failed to load user profile:', err);
      setError(err.message || 'Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    navigate(-1);
  };

  const getUserTypeColor = (userType) => {
    switch (userType?.toLowerCase()) {
      case 'student':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'teacher':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'institute':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'mentor':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Failed to Load Profile
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={loadUserProfile}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiUser className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            User Not Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            The user profile you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={goBack}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <FiArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isOwnProfile ? 'Your Profile' : 'User Profile'}
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Profile Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Profile Header */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-start gap-6">
                {/* Avatar */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-bold text-2xl">
                    {userProfile.profile_picture ? (
                      <img
                        src={userProfile.profile_picture}
                        alt={userProfile.username}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      userProfile.username?.charAt(0)?.toUpperCase() || '?'
                    )}
                  </div>
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userProfile.username}
                    </h2>
                    {userProfile.user_type && (
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getUserTypeColor(userProfile.user_type)}`}>
                        {userProfile.user_type}
                      </span>
                    )}
                  </div>

                  {/* Contact Info */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                      <FiMail className="w-4 h-4" />
                      <span className="text-sm">{userProfile.email}</span>
                      {userProfile.is_email_verified && (
                        <span className="text-green-500 text-xs">✓ Verified</span>
                      )}
                    </div>
                    
                    {userProfile.country && (
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <FiMapPin className="w-4 h-4" />
                        <span className="text-sm">{userProfile.country}</span>
                      </div>
                    )}
                    
                    {userProfile.joined_date && (
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <FiCalendar className="w-4 h-4" />
                        <span className="text-sm">Joined {formatDate(userProfile.joined_date)}</span>
                      </div>
                    )}
                  </div>

                  {/* Bio */}
                  {userProfile.bio && (
                    <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                      {userProfile.bio}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Social Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <UserSocialStats
                userId={userId}
                showFollowButton={!isOwnProfile}
                showMutualFollowers={!isOwnProfile}
                layout="horizontal"
              />
            </div>

            {/* Additional Profile Sections */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Activity
              </h3>
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  Activity feed coming soon...
                </p>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Follow Suggestions (only for other users) */}
            {!isOwnProfile && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <FollowSuggestions
                  limit={3}
                  layout="card"
                  showHeader={true}
                  showRefresh={false}
                />
              </div>
            )}

            {/* Profile Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                Actions
              </h3>
              <div className="space-y-3">
                {isOwnProfile ? (
                  <button
                    onClick={() => navigate('/student/settings')}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Edit Profile
                  </button>
                ) : (
                  <>
                    <button className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                      Send Message
                    </button>
                    <button className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                      Report User
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;
