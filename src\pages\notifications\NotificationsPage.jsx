/**
 * Notifications Management Page
 * 
 * Comprehensive notification management interface with:
 * - Filtering by read/unread status and notification type
 * - Bulk actions (mark as read, delete)
 * - Pagination
 * - Search functionality
 * - Detailed notification view
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  FiBell, 
  FiCheck, 
  FiTrash2, 
  FiFilter, 
  FiSearch, 
  FiRefreshCw,
  FiCheckSquare,
  FiSquare,
  FiLoader,
  FiAlertCircle,
  FiEye
} from 'react-icons/fi';
import { formatDistanceToNow } from 'date-fns';
import { PageContainer, Stack, Card } from '../../components/ui/layout';
import { LoadingSpinner } from '../../components/ui';
import { Pagination } from '../../components/ui/navigation';
import notificationService from '../../services/notificationService';
import { useNotification } from '../../contexts/NotificationContext';
import logger from '../../utils/helpers/logger';

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedIds, setSelectedIds] = useState(new Set());
  const [filters, setFilters] = useState({
    unread_only: false,
    notification_type: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 20,
    total_count: 0,
    has_next: false,
    has_previous: false
  });
  const [stats, setStats] = useState({
    total_notifications: 0,
    unread_notifications: 0
  });

  const { showSuccess, showError } = useNotification();

  // Fetch notifications
  const fetchNotifications = useCallback(async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const currentPage = resetPage ? 1 : pagination.page;
      
      const response = await notificationService.getNotifications({
        page: currentPage,
        page_size: pagination.page_size,
        unread_only: filters.unread_only,
        notification_type: filters.notification_type || undefined
      });
      
      setNotifications(response.notifications || []);
      setPagination({
        page: response.page || currentPage,
        page_size: response.page_size || pagination.page_size,
        total_count: response.total_count || 0,
        has_next: response.has_next || false,
        has_previous: response.has_previous || false
      });
      
      // Update stats
      setStats({
        total_notifications: response.total_count || 0,
        unread_notifications: response.unread_count || 0
      });
      
    } catch (err) {
      logger.error('Failed to fetch notifications', { error: err.message }, 'NotificationsPage');
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.page_size, filters]);

  // Initial load
  useEffect(() => {
    fetchNotifications(true);
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setSelectedIds(new Set()); // Clear selections when filters change
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Selection handlers
  const handleSelectAll = () => {
    if (selectedIds.size === notifications.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(notifications.map(n => n.id)));
    }
  };

  const handleSelectNotification = (id) => {
    const newSelected = new Set(selectedIds);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedIds(newSelected);
  };

  // Action handlers
  const handleMarkAsRead = async (notificationIds) => {
    try {
      if (notificationIds.length === 1) {
        await notificationService.markAsRead(notificationIds[0]);
      } else {
        await notificationService.bulkMarkAsRead(notificationIds);
      }
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notificationIds.includes(notif.id)
            ? { ...notif, is_read: true, read_at: new Date().toISOString() }
            : notif
        )
      );
      
      // Update stats
      setStats(prev => ({
        ...prev,
        unread_notifications: Math.max(0, prev.unread_notifications - notificationIds.length)
      }));
      
      setSelectedIds(new Set());
      showSuccess(`${notificationIds.length} notification(s) marked as read`);
    } catch (err) {
      logger.error('Failed to mark notifications as read', { error: err.message }, 'NotificationsPage');
      showError('Failed to mark notifications as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ 
          ...notif, 
          is_read: true, 
          read_at: new Date().toISOString() 
        }))
      );
      
      setStats(prev => ({ ...prev, unread_notifications: 0 }));
      setSelectedIds(new Set());
      showSuccess('All notifications marked as read');
    } catch (err) {
      logger.error('Failed to mark all notifications as read', { error: err.message }, 'NotificationsPage');
      showError('Failed to mark all notifications as read');
    }
  };

  const handleDelete = async (notificationIds) => {
    try {
      if (notificationIds.length === 1) {
        await notificationService.deleteNotification(notificationIds[0]);
      } else {
        await notificationService.bulkDeleteNotifications(notificationIds);
      }
      
      // Update local state
      setNotifications(prev => 
        prev.filter(notif => !notificationIds.includes(notif.id))
      );
      
      // Update stats
      const deletedUnreadCount = notifications
        .filter(n => notificationIds.includes(n.id) && !n.is_read)
        .length;
      
      setStats(prev => ({
        total_notifications: Math.max(0, prev.total_notifications - notificationIds.length),
        unread_notifications: Math.max(0, prev.unread_notifications - deletedUnreadCount)
      }));
      
      setSelectedIds(new Set());
      showSuccess(`${notificationIds.length} notification(s) deleted`);
    } catch (err) {
      logger.error('Failed to delete notifications', { error: err.message }, 'NotificationsPage');
      showError('Failed to delete notifications');
    }
  };

  // Utility functions
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'follow': return '👥';
      case 'exam': return '📝';
      case 'event': return '📅';
      case 'task': return '✅';
      case 'message': return '💬';
      default: return '📣';
    }
  };

  const formatNotificationTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Recently';
    }
  };

  const getFilteredNotifications = () => {
    if (!filters.search) return notifications;
    
    const searchLower = filters.search.toLowerCase();
    return notifications.filter(notif => 
      notif.title.toLowerCase().includes(searchLower) ||
      notif.message.toLowerCase().includes(searchLower)
    );
  };

  const filteredNotifications = getFilteredNotifications();

  return (
    <PageContainer>
      <Stack gap="lg">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Notifications
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your notifications and preferences
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => fetchNotifications()}
              disabled={loading}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            {stats.unread_notifications > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="flex items-center px-3 py-2 text-sm font-medium text-white bg-violet-600 hover:bg-violet-700 rounded-lg"
              >
                <FiCheck className="w-4 h-4 mr-2" />
                Mark All Read
              </button>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <FiBell className="w-8 h-8 text-violet-600 dark:text-violet-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.total_notifications}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <FiAlertCircle className="w-8 h-8 text-orange-600 dark:text-orange-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Unread</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.unread_notifications}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <FiCheck className="w-8 h-8 text-green-600 dark:text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Read</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.total_notifications - stats.unread_notifications}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-3">
              <select
                value={filters.notification_type}
                onChange={(e) => handleFilterChange('notification_type', e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500 focus:border-transparent"
              >
                <option value="">All Types</option>
                <option value="follow">Follow</option>
                <option value="exam">Exam</option>
                <option value="event">Event</option>
                <option value="task">Task</option>
                <option value="message">Message</option>
              </select>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.unread_only}
                  onChange={(e) => handleFilterChange('unread_only', e.target.checked)}
                  className="w-4 h-4 text-violet-600 bg-gray-100 border-gray-300 rounded focus:ring-violet-500 dark:focus:ring-violet-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Unread only</span>
              </label>
            </div>
          </div>
        </Card>

        {/* Bulk Actions */}
        {selectedIds.size > 0 && (
          <Card className="p-4 bg-violet-50 dark:bg-violet-900/20 border-violet-200 dark:border-violet-800">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-violet-700 dark:text-violet-300">
                {selectedIds.size} notification(s) selected
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleMarkAsRead(Array.from(selectedIds))}
                  className="flex items-center px-3 py-1.5 text-sm font-medium text-violet-700 dark:text-violet-300 bg-white dark:bg-gray-800 border border-violet-300 dark:border-violet-600 rounded hover:bg-violet-50 dark:hover:bg-violet-900/30"
                >
                  <FiCheck className="w-4 h-4 mr-1" />
                  Mark Read
                </button>
                <button
                  onClick={() => handleDelete(Array.from(selectedIds))}
                  className="flex items-center px-3 py-1.5 text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 border border-red-300 dark:border-red-600 rounded hover:bg-red-50 dark:hover:bg-red-900/30"
                >
                  <FiTrash2 className="w-4 h-4 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </Card>
        )}

        {/* Notifications List */}
        <Card>
          {loading && (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading notifications...</span>
            </div>
          )}

          {!loading && error && (
            <div className="text-center py-12">
              <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
              <button
                onClick={() => fetchNotifications()}
                className="px-4 py-2 text-sm font-medium text-white bg-violet-600 hover:bg-violet-700 rounded-lg"
              >
                Try Again
              </button>
            </div>
          )}

          {!loading && !error && filteredNotifications.length === 0 && (
            <div className="text-center py-12">
              <FiBell className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {filters.search || filters.unread_only || filters.notification_type
                  ? 'No notifications match your filters'
                  : 'No notifications yet'
                }
              </p>
            </div>
          )}

          {!loading && !error && filteredNotifications.length > 0 && (
            <div>
              {/* Select All Header */}
              <div className="flex items-center px-6 py-3 border-b border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleSelectAll}
                  className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-violet-600 dark:hover:text-violet-400"
                >
                  {selectedIds.size === filteredNotifications.length ? (
                    <FiCheckSquare className="w-4 h-4 mr-2" />
                  ) : (
                    <FiSquare className="w-4 h-4 mr-2" />
                  )}
                  Select All
                </button>
              </div>

              {/* Notifications */}
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start p-6 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                      !notification.is_read ? 'bg-violet-50 dark:bg-violet-900/10' : ''
                    }`}
                  >
                    {/* Checkbox */}
                    <button
                      onClick={() => handleSelectNotification(notification.id)}
                      className="mt-1 mr-4"
                    >
                      {selectedIds.has(notification.id) ? (
                        <FiCheckSquare className="w-4 h-4 text-violet-600 dark:text-violet-400" />
                      ) : (
                        <FiSquare className="w-4 h-4 text-gray-400" />
                      )}
                    </button>

                    {/* Icon */}
                    <div className="flex-shrink-0 mr-4">
                      <span className="text-2xl">{getNotificationIcon(notification.notification_type)}</span>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-1">
                        <h3 className={`text-sm font-medium ${
                          !notification.is_read
                            ? 'text-gray-900 dark:text-white'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {notification.title}
                        </h3>
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-violet-500 rounded-full ml-2 flex-shrink-0"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-500">
                        <span>{formatNotificationTime(notification.created_at)}</span>
                        <span className="mx-2">•</span>
                        <span className="capitalize">{notification.notification_type}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center ml-4 space-x-2">
                      {!notification.is_read && (
                        <button
                          onClick={() => handleMarkAsRead([notification.id])}
                          className="p-2 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                          title="Mark as read"
                        >
                          <FiCheck className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete([notification.id])}
                        className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Delete"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>

        {/* Pagination */}
        {pagination.total_count > pagination.page_size && (
          <div className="flex justify-center">
            <Pagination
              currentPage={pagination.page}
              totalPages={Math.ceil(pagination.total_count / pagination.page_size)}
              onPageChange={handlePageChange}
              showFirstLast={true}
              showPrevNext={true}
            />
          </div>
        )}
      </Stack>
    </PageContainer>
  );
};

export default NotificationsPage;
