/**
 * Timezone Utility Functions
 *
 * EduFair exam system uses UTC for all exam scheduling to ensure consistent
 * timing across different geographical locations.
 *
 * Backend stores all times in UTC, frontend converts between local and UTC.
 * Uses IP-based timezone detection for better user experience.
 */

// Cache for timezone data
let cachedTimezoneData = null;
let timezoneDetectionPromise = null;

/**
 * Detect user's timezone using IP geolocation with fallback to browser
 * @returns {Promise<Object>} Timezone data with timezone, country, city info
 */
export async function detectUserTimezone() {
  // Return cached data if available
  if (cachedTimezoneData) {
    return cachedTimezoneData;
  }

  // Return existing promise if detection is in progress
  if (timezoneDetectionPromise) {
    return timezoneDetectionPromise;
  }

  timezoneDetectionPromise = (async () => {
    try {
      // Try multiple IP geolocation services for better reliability
      const services = [
        'https://ipapi.co/json/',
        'https://api.ipgeolocation.io/ipgeo?apiKey=free', // Free tier
        'http://ip-api.com/json/?fields=status,country,countryCode,region,regionName,city,timezone,query'
      ];

      for (const serviceUrl of services) {
        try {
          const response = await fetch(serviceUrl);

          if (response.ok) {
            const data = await response.json();

            // Handle different API response formats
            let timezone, country, city, region, countryCode, ip;

            if (serviceUrl.includes('ipapi.co')) {
              timezone = data.timezone;
              country = data.country_name;
              city = data.city;
              region = data.region;
              countryCode = data.country_code;
              ip = data.ip;
            } else if (serviceUrl.includes('ipgeolocation.io')) {
              timezone = data.time_zone?.name;
              country = data.country_name;
              city = data.city;
              region = data.state_prov;
              countryCode = data.country_code2;
              ip = data.ip;
            } else if (serviceUrl.includes('ip-api.com')) {
              if (data.status === 'success') {
                timezone = data.timezone;
                country = data.country;
                city = data.city;
                region = data.regionName;
                countryCode = data.countryCode;
                ip = data.query;
              }
            }

            if (timezone && country) {
              cachedTimezoneData = {
                timezone,
                country,
                countryCode: countryCode || 'XX',
                region: region || 'Unknown',
                city: city || 'Unknown',
                ip: ip || 'Unknown',
                source: 'ip-geolocation',
                detected: true
              };

              console.log('🌍 Timezone detected via IP:', cachedTimezoneData);
              return cachedTimezoneData;
            }
          }
        } catch (serviceError) {
          console.warn(`Service ${serviceUrl} failed:`, serviceError);
          continue; // Try next service
        }
      }
    } catch (error) {
      console.warn('All IP-based timezone detection services failed:', error);
    }

    // Fallback to browser timezone
    const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    cachedTimezoneData = {
      timezone: browserTimezone,
      country: 'Unknown',
      countryCode: 'XX',
      region: 'Unknown',
      city: 'Unknown',
      ip: 'Unknown',
      source: 'browser',
      detected: false
    };

    console.log('🌐 Using browser timezone fallback:', cachedTimezoneData);
    return cachedTimezoneData;
  })();

  return timezoneDetectionPromise;
}

/**
 * Get user's timezone (with caching and IP detection)
 * @returns {string} User's timezone (e.g., "Asia/Karachi", "America/New_York")
 */
export function getUserTimezone() {
  // Return cached timezone if available
  if (cachedTimezoneData) {
    return cachedTimezoneData.timezone;
  }

  // Fallback to browser timezone for synchronous calls
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Get user's location info (async)
 * @returns {Promise<Object>} Location data
 */
export async function getUserLocation() {
  const timezoneData = await detectUserTimezone();
  return {
    country: timezoneData.country,
    city: timezoneData.city,
    region: timezoneData.region,
    timezone: timezoneData.timezone
  };
}

/**
 * Convert local time to UTC for API calls
 * @param {string} localDateTime - Local datetime string (e.g., "2024-01-15T19:00")
 * @returns {string|null} UTC datetime string without 'Z' (e.g., "2024-01-15T14:00:00") or null if invalid
 */
export function convertLocalToUTC(localDateTime) {
  try {
    const localDate = new Date(localDateTime);
    
    if (isNaN(localDate.getTime())) {
      throw new Error('Invalid local date');
    }

    // Convert to UTC ISO string and remove 'Z'
    return localDate.toISOString().slice(0, 19);
  } catch (error) {
    console.error('Error converting local to UTC:', error);
    return null;
  }
}

/**
 * Convert UTC time to local time for display
 * @param {string} utcDateTime - UTC datetime string (e.g., "2024-01-15T14:00:00")
 * @param {string} timezone - Target timezone (optional, defaults to user's timezone)
 * @returns {string} Formatted local time string
 */
export function convertUTCToLocal(utcDateTime, timezone = null) {
  try {
    // Add 'Z' if not present to ensure it's treated as UTC
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);
    
    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const targetTimezone = timezone || getUserTimezone();
    
    return new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(utcDate);
  } catch (error) {
    console.error('Error converting UTC to local:', error);
    return 'Invalid Date';
  }
}

/**
 * Convert UTC time to local datetime-local input format
 * @param {string} utcDateTime - UTC datetime string
 * @returns {string} Local datetime in format "YYYY-MM-DDTHH:mm"
 */
export function convertUTCToLocalInput(utcDateTime) {
  try {
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);
    
    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    // Convert to local time for datetime-local input
    const year = utcDate.getFullYear();
    const month = String(utcDate.getMonth() + 1).padStart(2, '0');
    const day = String(utcDate.getDate()).padStart(2, '0');
    const hours = String(utcDate.getHours()).padStart(2, '0');
    const minutes = String(utcDate.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('Error converting UTC to local input:', error);
    return '';
  }
}

/**
 * Format datetime with timezone information for display (enhanced with location)
 * @param {string} utcDateTime - UTC datetime string
 * @param {string} timezone - Target timezone (optional)
 * @param {boolean} showLocation - Whether to show city/country info
 * @returns {Promise<string>} Formatted string with timezone and location info
 */
export async function formatDateTimeWithTimezone(utcDateTime, timezone = null, showLocation = false) {
  try {
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const timezoneData = await detectUserTimezone();
    const targetTimezone = timezone || timezoneData.timezone;

    const formattedDate = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(utcDate);

    if (showLocation && timezoneData.detected) {
      return `${formattedDate} (${timezoneData.city}, ${timezoneData.country})`;
    } else {
      // Get timezone abbreviation
      const timezoneName = new Intl.DateTimeFormat('en-US', {
        timeZone: targetTimezone,
        timeZoneName: 'short'
      }).formatToParts(utcDate).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

      return `${formattedDate} ${timezoneName}`;
    }
  } catch (error) {
    console.error('Error formatting datetime with timezone:', error);
    return 'Invalid Date';
  }
}

/**
 * Format exam date/time from backend UTC time to user's local time
 * @param {string} utcDateTime - UTC datetime string from backend
 * @param {string} userTimezone - User's timezone (optional)
 * @returns {string} Formatted string in user's timezone
 */
export function formatExamDateTime(utcDateTime, userTimezone = null) {
  try {
    if (!utcDateTime || typeof utcDateTime !== 'string') {
      return 'Not set';
    }

    // Backend sends UTC time, so treat it as UTC
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const targetTimezone = userTimezone || getUserTimezone();

    const formattedDate = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(utcDate);

    // Show location if we have cached data
    if (cachedTimezoneData && cachedTimezoneData.detected) {
      return `${formattedDate} (${cachedTimezoneData.city}, ${cachedTimezoneData.country})`;
    } else {
      // Get timezone abbreviation
      const timezoneName = new Intl.DateTimeFormat('en-US', {
        timeZone: targetTimezone,
        timeZoneName: 'short'
      }).formatToParts(utcDate).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

      return `${formattedDate} ${timezoneName}`;
    }

  } catch (error) {
    console.error('Error formatting exam date time:', error);
    return 'Invalid date';
  }
}

/**
 * Convert backend UTC time to Date object for calculations
 * @param {string} utcDateTime - UTC datetime string from backend
 * @returns {Date} UTC Date object
 */
export function backendTimeToUTC(utcDateTime) {
  try {
    if (!utcDateTime || typeof utcDateTime !== 'string') {
      throw new Error('Invalid datetime input');
    }

    // Backend sends UTC time, so treat it as UTC
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid date format');
    }

    return utcDate;

  } catch (error) {
    console.error('Error converting backend time to UTC:', error);
    return new Date(); // Return current time as fallback
  }
}

/**
 * Legacy function - kept for backward compatibility
 * Now just calls backendTimeToUTC since backend sends UTC times
 * @param {string} dateTime - DateTime string from backend (UTC)
 * @returns {Date} UTC Date object
 */
export function pakistanTimeToUTC(dateTime) {
  return backendTimeToUTC(dateTime);
}

/**
 * Synchronous version for immediate display (uses cached data)
 * @param {string} utcDateTime - UTC datetime string
 * @param {string} timezone - Target timezone (optional)
 * @returns {string} Formatted string
 */
export function formatDateTimeSync(utcDateTime, timezone = null) {
  try {
    // Check if utcDateTime is valid
    if (!utcDateTime || typeof utcDateTime !== 'string') {
      return 'Not set';
    }

    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const targetTimezone = timezone || getUserTimezone();

    const formattedDate = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(utcDate);

    // Show location if we have cached data
    if (cachedTimezoneData && cachedTimezoneData.detected) {
      return `${formattedDate} (${cachedTimezoneData.city}, ${cachedTimezoneData.country})`;
    } else {
      // Get timezone abbreviation
      const timezoneName = new Intl.DateTimeFormat('en-US', {
        timeZone: targetTimezone,
        timeZoneName: 'short'
      }).formatToParts(utcDate).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

      return `${formattedDate} ${timezoneName}`;
    }
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return 'Invalid Date';
  }
}

/**
 * Check if a datetime string is valid
 * @param {string} dateTimeString - Datetime string to validate
 * @returns {boolean} True if valid, false otherwise
 */
export function isValidDateTime(dateTimeString) {
  try {
    const date = new Date(dateTimeString);
    return !isNaN(date.getTime());
  } catch (error) {
    return false;
  }
}

/**
 * Get current UTC time as ISO string without 'Z'
 * @returns {string} Current UTC time (e.g., "2024-01-15T14:30:00")
 */
export function getCurrentUTC() {
  return new Date().toISOString().slice(0, 19);
}

/**
 * Safe conversion wrapper with error handling
 * @param {string} localDateTime - Local datetime string
 * @returns {string|null} UTC string or null if conversion fails
 */
export function safeConvertToUTC(localDateTime) {
  try {
    const date = new Date(localDateTime);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date format');
    }
    return date.toISOString().slice(0, 19);
  } catch (error) {
    console.error('Date conversion error:', error);
    return null;
  }
}

/**
 * Example usage for updating exam time
 * @param {string} examId - Exam ID
 * @param {string} localStartTime - Local start time
 * @param {number} duration - Duration in minutes
 * @returns {Promise} API response
 */
export async function updateExamTime(examId, localStartTime, duration) {
  // Convert local time to UTC
  const utcStartTime = convertLocalToUTC(localStartTime);
  
  if (!utcStartTime) {
    throw new Error('Invalid start time format');
  }
  
  // Send to API
  const response = await fetch(`/api/exams/${examId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify({
      start_time: utcStartTime,
      total_duration: duration
    })
  });
  
  return response.json();
}

/**
 * Validates if a datetime string is in proper UTC format
 * @param {string} dateTimeString - The datetime string to validate
 * @returns {Object} Validation result with isValid, message, and corrected value
 */
export function validateUTCDateTime(dateTimeString) {
  if (!dateTimeString) {
    return {
      isValid: false,
      message: 'DateTime string is empty or null',
      correctedValue: null
    };
  }

  try {
    const date = new Date(dateTimeString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return {
        isValid: false,
        message: 'Invalid date format',
        correctedValue: null
      };
    }

    // Check if string ends with 'Z' (UTC indicator) or has timezone offset
    const isUTCFormat = dateTimeString.endsWith('Z') || /[+-]\d{2}:\d{2}$/.test(dateTimeString);

    if (!isUTCFormat) {
      // Convert to UTC if not already
      const utcString = date.toISOString();
      return {
        isValid: false,
        message: 'DateTime is not in UTC format, converted automatically',
        correctedValue: utcString,
        originalValue: dateTimeString
      };
    }

    return {
      isValid: true,
      message: 'DateTime is in proper UTC format',
      correctedValue: dateTimeString
    };
  } catch (error) {
    return {
      isValid: false,
      message: `Error validating datetime: ${error.message}`,
      correctedValue: null
    };
  }
}

/**
 * Logs timezone conversion information for debugging
 * @param {string} context - Context where this is being called from
 * @param {string} dateTimeString - The datetime string to log
 * @param {Object} additionalInfo - Additional information to log
 */
export function logTimezoneInfo(context, dateTimeString, additionalInfo = {}) {
  const validation = validateUTCDateTime(dateTimeString);
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  console.log(`🕒 ${context} timezone info:`, {
    originalDateTime: dateTimeString,
    validation,
    userTimezone,
    localDisplay: dateTimeString ? new Date(dateTimeString).toLocaleString() : 'Not set',
    ...additionalInfo
  });

  if (!validation.isValid) {
    console.warn(`⚠️ ${context}: ${validation.message}`);
  }

  return validation;
}

// Timezone examples for reference
export const TIMEZONE_EXAMPLES = {
  'Asia/Karachi': 'Pakistan Standard Time (UTC+5)',
  'America/New_York': 'Eastern Time (UTC-5/-4)',
  'Europe/London': 'Greenwich Mean Time (UTC+0/+1)',
  'Asia/Kolkata': 'India Standard Time (UTC+5:30)',
  'America/Los_Angeles': 'Pacific Time (UTC-8/-7)',
  'Asia/Tokyo': 'Japan Standard Time (UTC+9)',
  'Australia/Sydney': 'Australian Eastern Time (UTC+10/+11)'
};
