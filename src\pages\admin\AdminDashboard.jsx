import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>ontainer, Stack } from '../../components/ui/layout';
import { DashboardGrid, QuickActions } from '../../components/dashboard';
import {
  FiUsers,
  FiBookOpen,
  FiTrendingUp,
  FiSettings,
  FiEye,
  FiShield,
  FiMonitor
} from 'react-icons/fi';

function AdminDashboard() {
  const navigate = useNavigate();

  const stats = useMemo(() => [
    {
      key: 'users',
      title: 'Total Users',
      value: '2,847',
      icon: FiUsers,
      color: 'blue',
      onClick: () => navigate('/admin/users')
    },
    {
      key: 'institutes',
      title: 'Institutes',
      value: '156',
      icon: FiBookOpen,
      color: 'green',
      onClick: () => navigate('/admin/institutes')
    },
    {
      key: 'growth',
      title: 'Monthly Growth',
      value: '+12.5%',
      icon: FiTrendingUp,
      color: 'purple'
    },
    {
      key: 'approvals',
      title: 'Pending Approvals',
      value: '23',
      icon: FiShield,
      color: 'yellow',
      onClick: () => navigate('/admin/institute-approvals')
    }
  ], [navigate]);

  const quickActions = useMemo(() => [
    {
      key: 'manage-users',
      title: 'Manage Users',
      description: 'View and manage all users',
      icon: FiEye,
      color: 'blue',
      onClick: () => navigate('/admin/users')
    },
    {
      key: 'approve-institutes',
      title: 'Approve Institutes',
      description: 'Review pending institute applications',
      icon: FiShield,
      color: 'green',
      onClick: () => navigate('/admin/institute-approvals')
    },
    {
      key: 'exam-sessions',
      title: 'Monitor Exam Sessions',
      description: 'View and manage active exam sessions',
      icon: FiMonitor,
      color: 'orange',
      onClick: () => navigate('/admin/exam-sessions')
    },
    {
      key: 'system-settings',
      title: 'System Settings',
      description: 'Configure system parameters',
      icon: FiSettings,
      color: 'purple',
      onClick: () => navigate('/admin/settings')
    }
  ], [navigate]);

  return (
    <PageContainer>
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
          Admin Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          System overview and management tools
        </p>
      </div>

      <Stack gap="lg">
        <DashboardGrid stats={stats} />
        <QuickActions actions={quickActions} />
      </Stack>
    </PageContainer>
  );
}

export default AdminDashboard;
