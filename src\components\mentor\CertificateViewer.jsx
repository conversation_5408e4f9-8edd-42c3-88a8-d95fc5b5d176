import React, { useState } from 'react';
import {
  FiX,
  FiDownload,
  FiPrinter,
  FiShare2,
  FiAward,
  FiCalendar,
  FiUser,
  FiFileText
} from 'react-icons/fi';

const CertificateViewer = ({ certificate, isOpen, onClose }) => {
  const [loading, setLoading] = useState(false);

  if (!isOpen || !certificate) return null;

  const handleDownload = async () => {
    try {
      setLoading(true);

      // If certificate has a download URL, use it
      if (certificate.certificate_url) {
        const baseUrl = 'http://127.0.0.1:8000'; // API base URL
        const downloadUrl = `${baseUrl}${certificate.certificate_url}`;

        // Create a temporary link and click it to download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `certificate-${certificate.participant_name || 'participant'}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Fallback: Create a simple PDF-like view for download
        const printWindow = window.open('', '_blank');
        const certificateHTML = generateCertificateHTML(certificate);

        printWindow.document.write(certificateHTML);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }

    } catch (error) {
      console.error('Error downloading certificate:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    const certificateHTML = generateCertificateHTML(certificate);
    
    printWindow.document.write(certificateHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  const generateCertificateHTML = (cert) => {
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Certificate - ${cert.student_name || 'Participant'}</title>
          <style>
            body {
              font-family: 'Georgia', serif;
              margin: 0;
              padding: 40px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .certificate {
              background: white;
              padding: 60px;
              border-radius: 20px;
              box-shadow: 0 20px 40px rgba(0,0,0,0.1);
              text-align: center;
              max-width: 800px;
              width: 100%;
              border: 8px solid #f8f9fa;
              position: relative;
            }
            .certificate::before {
              content: '';
              position: absolute;
              top: 20px;
              left: 20px;
              right: 20px;
              bottom: 20px;
              border: 3px solid #e9ecef;
              border-radius: 12px;
            }
            .header {
              margin-bottom: 40px;
            }
            .title {
              font-size: 48px;
              color: #2c3e50;
              margin-bottom: 10px;
              font-weight: bold;
              text-transform: uppercase;
              letter-spacing: 3px;
            }
            .subtitle {
              font-size: 18px;
              color: #7f8c8d;
              margin-bottom: 40px;
            }
            .recipient {
              font-size: 32px;
              color: #2980b9;
              margin: 30px 0;
              font-weight: bold;
            }
            .achievement {
              font-size: 20px;
              color: #34495e;
              margin: 20px 0;
              line-height: 1.6;
            }
            .competition {
              font-size: 24px;
              color: #e74c3c;
              margin: 20px 0;
              font-weight: bold;
            }
            .position {
              font-size: 28px;
              color: #f39c12;
              margin: 20px 0;
              font-weight: bold;
            }
            .date {
              font-size: 16px;
              color: #7f8c8d;
              margin-top: 40px;
            }
            .signature-section {
              margin-top: 60px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .signature {
              text-align: center;
              flex: 1;
            }
            .signature-line {
              border-top: 2px solid #34495e;
              width: 200px;
              margin: 0 auto 10px;
            }
            .medal {
              font-size: 64px;
              margin: 20px 0;
            }
            @media print {
              body { background: white; }
              .certificate { box-shadow: none; }
            }
          </style>
        </head>
        <body>
          <div class="certificate">
            <div class="header">
              <div class="title">Certificate of Achievement</div>
              <div class="subtitle">This is to certify that</div>
            </div>
            
            <div class="medal">${getPositionEmoji(cert.position)}</div>
            
            <div class="recipient">${cert.participant_name || cert.student_name || 'Participant Name'}</div>

            <div class="achievement">
              has successfully achieved
            </div>

            <div class="position">${getPositionText(cert.rank_position || cert.position)} Place</div>

            <div class="achievement">
              in the competition
            </div>

            <div class="competition">${cert.competition_title || 'Competition Name'}</div>

            <div class="achievement" style="margin-top: 20px;">
              with a final score of <strong>${cert.final_score || 'N/A'}</strong>
              (${cert.score_percentage || 'N/A'}%)
            </div>
            
            ${cert.custom_message ? `
              <div class="achievement" style="font-style: italic; margin-top: 30px;">
                "${cert.custom_message}"
              </div>
            ` : ''}
            
            <div class="date">
              Issued on ${formatDate(cert.issued_date || cert.issued_at || cert.created_at || new Date())}
            </div>

            ${cert.verification_code ? `
              <div class="achievement" style="margin-top: 20px; font-size: 14px;">
                Verification Code: <strong>${cert.verification_code}</strong>
              </div>
            ` : ''}

            ${cert.evaluator_name ? `
              <div class="achievement" style="margin-top: 10px; font-size: 14px;">
                Evaluated by: <strong>${cert.evaluator_name}</strong>
              </div>
            ` : ''}
            
            <div class="signature-section">
              <div class="signature">
                <div class="signature-line"></div>
                <div>Mentor Signature</div>
              </div>
              <div class="signature">
                <div class="signature-line"></div>
                <div>Institution Seal</div>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  };

  const getPositionEmoji = (position) => {
    switch (position) {
      case 1: return '🏆';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return '🏅';
    }
  };

  const getPositionText = (position) => {
    switch (position) {
      case 1: return '1st';
      case 2: return '2nd';
      case 3: return '3rd';
      default: return `${position}th`;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FiAward className="h-6 w-6 text-yellow-600" />
            <h2 className="text-xl font-semibold text-gray-900">Certificate Viewer</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrint}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiPrinter className="h-4 w-4 mr-2" />
              Print Preview
            </button>
            <button
              onClick={handleDownload}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <FiDownload className="h-4 w-4 mr-2" />
              {loading ? 'Processing...' : certificate.certificate_url ? 'Download PDF' : 'Print Certificate'}
            </button>
            {certificate.certificate_url && (
              <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                Official PDF Available
              </span>
            )}
            <button
              onClick={onClose}
              className="inline-flex items-center p-2 border border-transparent rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiX className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Certificate Preview */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-lg border-2 border-gray-200">
            <div className="bg-white p-12 rounded-lg shadow-lg text-center max-w-3xl mx-auto">
              {/* Certificate Header */}
              <div className="mb-8">
                <h1 className="text-4xl font-bold text-gray-800 mb-2 tracking-wide">
                  CERTIFICATE OF ACHIEVEMENT
                </h1>
                <p className="text-lg text-gray-600">This is to certify that</p>
              </div>

              {/* Medal/Position Icon */}
              <div className="text-6xl mb-6">
                {getPositionEmoji(certificate.position)}
              </div>

              {/* Recipient Name */}
              <div className="mb-6">
                <h2 className="text-3xl font-bold text-blue-600 border-b-2 border-blue-200 pb-2 inline-block">
                  {certificate.participant_name || certificate.student_name || 'Participant Name'}
                </h2>
              </div>

              {/* Achievement Details */}
              <div className="mb-6">
                <p className="text-lg text-gray-700 mb-2">has successfully achieved</p>
                <h3 className="text-2xl font-bold text-yellow-600 mb-2">
                  {getPositionText(certificate.rank_position || certificate.position)} Place
                </h3>
                <p className="text-lg text-gray-700 mb-2">in the competition</p>
                <h4 className="text-xl font-semibold text-red-600">
                  {certificate.competition_title || 'Competition Name'}
                </h4>
              </div>

              {/* Score Information */}
              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <p className="text-sm text-gray-600">Final Score</p>
                    <p className="text-xl font-bold text-blue-600">
                      {certificate.final_score || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Percentage</p>
                    <p className="text-xl font-bold text-green-600">
                      {certificate.score_percentage || 'N/A'}%
                    </p>
                  </div>
                </div>
              </div>

              {/* Custom Message */}
              {certificate.custom_message && (
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <p className="text-gray-700 italic">"{certificate.custom_message}"</p>
                </div>
              )}

              {/* Certificate Details */}
              <div className="grid grid-cols-2 gap-4 mb-8 text-sm text-gray-600">
                <div className="flex items-center justify-center">
                  <FiCalendar className="h-4 w-4 mr-2" />
                  <span>Issued: {formatDate(certificate.issued_date || certificate.issued_at || certificate.created_at)}</span>
                </div>
                <div className="flex items-center justify-center">
                  <FiFileText className="h-4 w-4 mr-2" />
                  <span>Certificate ID: {certificate.certificate_id || 'N/A'}</span>
                </div>
              </div>

              {/* Verification Code */}
              {certificate.verification_code && (
                <div className="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-1">Verification Code</p>
                    <p className="text-lg font-mono font-bold text-yellow-700">
                      {certificate.verification_code}
                    </p>
                  </div>
                </div>
              )}

              {/* Evaluator Information */}
              {certificate.evaluator_name && (
                <div className="mb-6 text-center">
                  <p className="text-sm text-gray-600">
                    Evaluated by: <span className="font-semibold">{certificate.evaluator_name}</span>
                  </p>
                </div>
              )}

              {/* Signature Section */}
              <div className="flex justify-between items-end mt-12">
                <div className="text-center">
                  <div className="w-32 border-t-2 border-gray-400 mb-2"></div>
                  <p className="text-sm text-gray-600">Mentor Signature</p>
                </div>
                <div className="text-center">
                  <div className="w-32 border-t-2 border-gray-400 mb-2"></div>
                  <p className="text-sm text-gray-600">Institution Seal</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateViewer;
