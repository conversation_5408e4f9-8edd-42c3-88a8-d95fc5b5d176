import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>lock,
  FiFileText,
  FiSave,
  FiArrowLeft,
  FiCheckCircle,
  FiAlertTriangle,
  FiMessageSquare,
  FiStar
} from 'react-icons/fi';
import { getSubmissionDetails, markSubmission } from '../../services/mentorEvaluationService';
import { LoadingSpinner, ErrorMessage } from '../ui';

const SubmissionMarkingInterface = ({ attemptId, onBack, onSubmissionMarked, submissionData: propSubmissionData }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [submissionData, setSubmissionData] = useState(null);
  const [marking, setMarking] = useState(false);
  const [markingData, setMarkingData] = useState({
    total_score: 0,
    overall_feedback: '',
    question_scores: []
  });
  const [isAlreadyEvaluated, setIsAlreadyEvaluated] = useState(false);

  useEffect(() => {
    if (propSubmissionData) {
      // Use provided submission data (for demo purposes)
      setSubmissionData(propSubmissionData);
      initializeMarkingData(propSubmissionData);
      setLoading(false);
    } else if (attemptId) {
      loadSubmissionDetails();
    }
  }, [attemptId, propSubmissionData]);

  const initializeMarkingData = (response) => {
    console.log('🔍 Initializing marking data:', response);

    // Check if submission is already evaluated
    const isEvaluated = response.mentor_evaluation?.overall?.is_evaluated || false;
    setIsAlreadyEvaluated(isEvaluated);

    console.log('📊 Evaluation status:', { isEvaluated });

    if (isEvaluated) {
      // Use existing evaluation data
      const questionScores = response.answers?.map((answer, index) => ({
        question_id: answer.question_id || `question_${index}`,
        score: answer.mentor_feedback?.score_awarded || 0,
        feedback: answer.mentor_feedback?.feedback || '',
        max_marks: answer.marks || answer.mentor_feedback?.max_score || 0,
        is_evaluated: answer.mentor_feedback?.is_evaluated || false
      })) || [];

      setMarkingData({
        total_score: response.mentor_evaluation?.overall?.total_score || 0,
        overall_feedback: response.mentor_evaluation?.overall?.overall_feedback || '',
        question_scores: questionScores
      });

      console.log('✅ Loaded existing evaluation:', {
        total_score: response.mentor_evaluation?.overall?.total_score,
        overall_feedback: response.mentor_evaluation?.overall?.overall_feedback,
        questionScores
      });
    } else {
      // Initialize empty marking data for new evaluation
      const questionScores = response.answers?.map((answer, index) => ({
        question_id: answer.question_id || `question_${index}`,
        score: 0,
        feedback: '',
        max_marks: answer.marks || 0,
        is_evaluated: false
      })) || [];

      setMarkingData({
        total_score: 0,
        overall_feedback: '',
        question_scores: questionScores
      });

      console.log('📝 Initialized new evaluation data');
    }
  };

  const loadSubmissionDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getSubmissionDetails(attemptId);
      setSubmissionData(response);
      initializeMarkingData(response);

    } catch (err) {
      console.error('Error loading submission details:', err);
      setError('Failed to load submission details');
    } finally {
      setLoading(false);
    }
  };

  const handleQuestionScoreChange = (questionIndex, field, value) => {
    setMarkingData(prev => {
      const updatedQuestionScores = [...prev.question_scores];
      updatedQuestionScores[questionIndex] = {
        ...updatedQuestionScores[questionIndex],
        [field]: field === 'score' ? Math.max(0, parseInt(value) || 0) : value
      };
      
      // Calculate total score
      const totalScore = updatedQuestionScores.reduce((sum, q) => sum + (q.score || 0), 0);
      
      return {
        ...prev,
        question_scores: updatedQuestionScores,
        total_score: totalScore
      };
    });
  };

  const handleOverallFeedbackChange = (value) => {
    setMarkingData(prev => ({
      ...prev,
      overall_feedback: value
    }));
  };

  const handleSubmitMarking = async () => {
    try {
      setMarking(true);
      setError(null);
      
      const submissionData = {
        attempt_id: attemptId,
        total_score: markingData.total_score,
        overall_feedback: markingData.overall_feedback,
        question_scores: markingData.question_scores
      };
      
      await markSubmission(submissionData);
      
      if (onSubmissionMarked) {
        onSubmissionMarked();
      }
      
    } catch (err) {
      console.error('Error submitting marking:', err);
      setError('Failed to submit marking. Please try again.');
    } finally {
      setMarking(false);
    }
  };

  const isMarkingValid = () => {
    return markingData.overall_feedback.trim().length >= 10 && 
           markingData.question_scores.every(q => q.score >= 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={loadSubmissionDetails} />;
  }

  if (!submissionData) {
    return <ErrorMessage message="Submission data not found" />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
          >
            <FiArrowLeft className="h-4 w-4 mr-2" />
            Back to Submissions
          </button>
          
          <div className="flex items-center space-x-4">
            {isAlreadyEvaluated ? (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <FiCheckCircle className="h-4 w-4 mr-1" />
                Already Evaluated
              </span>
            ) : (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                <FiAlertTriangle className="h-4 w-4 mr-1" />
                Pending Evaluation
              </span>
            )}
          </div>
        </div>

        {/* Student Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">Student</label>
            <p className="text-lg font-semibold text-gray-900">{submissionData.student?.name || 'N/A'}</p>
            <p className="text-sm text-gray-600">{submissionData.student?.email || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Exam</label>
            <p className="text-lg font-semibold text-gray-900">{submissionData.exam?.title || 'N/A'}</p>
            <p className="text-sm text-gray-600">{submissionData.exam?.description || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Competition</label>
            <p className="text-lg font-semibold text-gray-900">{submissionData.competition?.title || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Submitted</label>
            <p className="text-lg font-semibold text-gray-900">{formatDate(submissionData.submission?.submitted_at)}</p>
            <p className="text-sm text-gray-600">Status: {submissionData.submission?.status || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">Score</label>
            <p className="text-lg font-semibold text-gray-900">
              {markingData.total_score} / {submissionData.exam?.total_marks || 0}
            </p>
            <p className="text-sm text-gray-600">
              {submissionData.exam?.total_marks ?
                `${Math.round((markingData.total_score / submissionData.exam.total_marks) * 100)}%` :
                '0%'
              }
            </p>
          </div>
        </div>

        {/* Evaluation Summary for Already Evaluated Submissions */}
        {isAlreadyEvaluated && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center mb-3">
              <FiCheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-green-800">Evaluation Complete</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-green-700">Final Score</p>
                <p className="text-xl font-bold text-green-800">
                  {submissionData.mentor_evaluation?.overall?.total_score || 0} / {submissionData.mentor_evaluation?.overall?.max_possible_score || submissionData.exam?.total_marks || 0}
                </p>
                <p className="text-sm text-green-600">
                  {submissionData.mentor_evaluation?.overall?.score_percentage || 0}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-green-700">Evaluated By</p>
                <p className="text-sm text-green-800">
                  Mentor ID: {submissionData.mentor_evaluation?.overall?.evaluated_by || 'N/A'}
                </p>
                <p className="text-xs text-green-600">
                  {formatDate(submissionData.mentor_evaluation?.overall?.evaluated_at)}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-green-700">Grade Classification</p>
                <p className="text-lg font-bold text-green-800">
                  {submissionData.mentor_evaluation?.performance_analysis?.grade_classification || 'N/A'}
                </p>
              </div>
            </div>
            {submissionData.mentor_evaluation?.overall?.overall_feedback && (
              <div className="mt-4">
                <p className="text-sm font-medium text-green-700">Overall Feedback</p>
                <p className="text-sm text-green-800 bg-white p-3 rounded border border-green-200 mt-1">
                  {submissionData.mentor_evaluation.overall.overall_feedback}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Questions and Answers */}
      <div className="space-y-6">
        {submissionData.answers?.map((answer, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Question {index + 1}
                </h3>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Type:</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {answer.question_type || 'N/A'}
                  </span>
                  <span className="text-sm text-gray-500">Max:</span>
                  <span className="font-medium text-gray-900">{answer.marks || 0} pts</span>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-md mb-4">
                <p className="text-gray-900 whitespace-pre-wrap">{answer.question_text || 'Question text not available'}</p>
              </div>
            </div>

            {/* Correct Answer */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Correct Answer</label>
              <div className="bg-green-50 p-4 rounded-md border border-green-200">
                <p className="text-gray-900 whitespace-pre-wrap">{answer.correct_answer || 'Correct answer not available'}</p>
              </div>
            </div>

            {/* Student Answer */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Student Answer</label>
              <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                <p className="text-gray-900 whitespace-pre-wrap">{answer.student_answer || 'No answer provided'}</p>
              </div>
            </div>

            {/* Scoring */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Score (0 - {answer.marks || 0} points)
                </label>
                {isAlreadyEvaluated ? (
                  <div className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-gray-900">
                        {markingData.question_scores[index]?.score || 0} / {answer.marks || 0}
                      </span>
                      <span className="text-sm text-gray-600">
                        ({answer.marks ? Math.round(((markingData.question_scores[index]?.score || 0) / answer.marks) * 100) : 0}%)
                      </span>
                    </div>
                  </div>
                ) : (
                  <>
                    <input
                      type="number"
                      min="0"
                      max={answer.marks || 0}
                      step="0.5"
                      value={markingData.question_scores[index]?.score || 0}
                      onChange={(e) => handleQuestionScoreChange(index, 'score', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`Enter score (max ${answer.marks || 0})`}
                    />
                    <div className="mt-1 text-xs text-gray-500">
                      Current: {markingData.question_scores[index]?.score || 0} / {answer.marks || 0}
                      ({answer.marks ? Math.round(((markingData.question_scores[index]?.score || 0) / answer.marks) * 100) : 0}%)
                    </div>
                  </>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Question Feedback
                </label>
                {isAlreadyEvaluated ? (
                  <div className="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md min-h-[76px]">
                    <p className="text-gray-900 whitespace-pre-wrap">
                      {markingData.question_scores[index]?.feedback || 'No feedback provided'}
                    </p>
                  </div>
                ) : (
                  <textarea
                    value={markingData.question_scores[index]?.feedback || ''}
                    onChange={(e) => handleQuestionScoreChange(index, 'feedback', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Provide specific feedback for this question..."
                  />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Overall Feedback */}
      {!isAlreadyEvaluated && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Overall Feedback</h3>
          <textarea
            value={markingData.overall_feedback}
            onChange={(e) => handleOverallFeedbackChange(e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Provide comprehensive feedback on the overall submission. Include strengths, areas for improvement, and general comments..."
          />
          <p className="text-sm text-gray-500 mt-2">
            Minimum 10 characters required. Current: {markingData.overall_feedback.length}
          </p>
        </div>
      )}

      {/* Summary and Submit */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {isAlreadyEvaluated ? 'Evaluation Summary' : 'Marking Summary'}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Total Score: <span className="font-medium">{markingData.total_score} points</span>
              {submissionData.exam?.total_marks && (
                <span className="ml-2">
                  ({Math.round((markingData.total_score / submissionData.exam.total_marks) * 100)}%)
                </span>
              )}
            </p>
            {isAlreadyEvaluated && (
              <p className="text-xs text-gray-500 mt-1">
                This submission has already been evaluated and cannot be modified.
              </p>
            )}
          </div>

          {!isAlreadyEvaluated && (
            <button
              onClick={handleSubmitMarking}
              disabled={marking || !isMarkingValid()}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {marking && <LoadingSpinner size="sm" className="mr-2" />}
              <FiSave className="h-5 w-5 mr-2" />
              {marking ? 'Submitting...' : 'Submit Marking'}
            </button>
          )}
        </div>
        
        {!isMarkingValid() && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              Please ensure all questions have scores and provide overall feedback (minimum 10 characters).
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubmissionMarkingInterface;
