import { Fi<PERSON>lertTriangle, FiBook<PERSON><PERSON> } from "react-icons/fi";
import { useThemeProvider } from "../../../providers/ThemeContext";

function ExamLoadingState({ 
  isLoading, 
  error, 
  examId, 
  onRetry, 
  onBackToExams 
}) {
  const { themeBg, themeText, cardBg } = useThemeProvider();

  if (isLoading) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p>Loading exam...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiAlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Exam</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {typeof error === 'string' ? error : error?.message || 'Failed to load exam data'}
          </p>
          <div className="flex space-x-3">
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onBackToExams}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
            >
              Back to Exams
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
      <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
        <FiBookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Loading Exam...</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Please wait while we load your exam details.
        </p>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <p>Exam ID: {examId}</p>
        </div>
      </div>
    </div>
  );
}

export default ExamLoadingState;
