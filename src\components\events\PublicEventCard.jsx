import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiEye,
  FiUserPlus,
  FiXCircle
} from 'react-icons/fi';
import EventRegistrationModal from './EventRegistrationModalNew';
import EventTimingBadge from './EventTimingBadge';
import { getRegistrationStatus, getEventTimingStatus } from '../../utils/eventUtils';

// Registration Status Badge Component
const RegistrationStatusBadge = ({ event, size = 'default' }) => {
  const registrationStatus = getRegistrationStatus(event);

  const getIcon = (iconName) => {
    const iconMap = {
      'user-plus': FiUserPlus,
      'clock': FiClock,
      'x-circle': FiXCircle
    };
    return iconMap[iconName] || FiUserPlus;
  };

  const getSizeClasses = () => {
    const sizeMap = {
      small: 'px-2 py-0.5 text-xs',
      default: 'px-2.5 py-1 text-sm'
    };
    return sizeMap[size] || sizeMap.default;
  };

  // Only show if registration is closed or closing soon (never show "open")
  if (registrationStatus.status === 'open') {
    return null;
  }

  const Icon = getIcon(registrationStatus.icon);
  const sizeClasses = getSizeClasses();

  return (
    <span className={`inline-flex items-center ${sizeClasses} rounded-full font-medium ${registrationStatus.color}`}>
      <Icon className={`${size === 'small' ? 'h-3 w-3' : 'h-4 w-4'} mr-1`} />
      {registrationStatus.label}
    </span>
  );
};

const PublicEventCard = ({ event, onViewDetails, onRegister, variant = 'default', viewMode = 'grid' }) => {
  const navigate = useNavigate();
  const [showRegistration, setShowRegistration] = useState(false);

  // Get event and registration status
  const eventStatus = getEventTimingStatus(event);
  const registrationStatus = getRegistrationStatus(event);

  // Determine if registration is allowed
  const canRegister = eventStatus.status !== 'ended' && registrationStatus.status !== 'closed';

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(event);
    } else {
      navigate(`/events/${event.id}`);
    }
  };

  const handleRegister = () => {
    if (!canRegister) return; // Prevent registration if not allowed

    if (onRegister) {
      onRegister(event);
    } else {
      setShowRegistration(true);
    }
  };

  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    // Show success message or redirect
    alert('Registration successful!');
  };

  const handleRegistrationClose = () => {
    setShowRegistration(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category) => {
    const colors = {
      WORKSHOP: 'bg-slate-100 text-slate-700 border-slate-200',
      CONFERENCE: 'bg-gray-100 text-gray-700 border-gray-200',
      WEBINAR: 'bg-stone-100 text-stone-700 border-stone-200',
      COMPETITION: 'bg-zinc-100 text-zinc-700 border-zinc-200',
      SEMINAR: 'bg-neutral-100 text-neutral-700 border-neutral-200'
    };
    return colors[category] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  // Grid view (default)
  if (viewMode === 'grid') {
    return (
      <>
        <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 ${
          variant === 'featured' ? 'ring-1 ring-blue-500' : ''
        }`}>
          {/* Event Image */}
          <div className="relative">
            {event.banner_image_url ? (
              <div className="h-48 bg-gray-200 overflow-hidden">
                <img
                  src={event.banner_image_url}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <FiCalendar className="w-12 h-12 text-gray-400" />
              </div>
            )}

            {/* Status Badge Overlay - Show only the most important one */}
            <div className="absolute top-3 left-3">
              {eventStatus.status === 'ended' ? (
                <EventTimingBadge event={event} size="small" />
              ) : registrationStatus.status !== 'open' ? (
                <RegistrationStatusBadge event={event} size="small" />
              ) : eventStatus.status === 'ongoing' ? (
                <EventTimingBadge event={event} size="small" />
              ) : null}
            </div>

            {/* Featured/Competition Badges */}
            <div className="absolute top-3 right-3 flex space-x-2">
              {event.is_featured && (
                <div className="bg-yellow-500 text-white p-2 rounded-full shadow-lg">
                  <FiStar className="h-4 w-4" />
                </div>
              )}
              {event.is_competition && (
                <div className="bg-orange-500 text-white p-2 rounded-full shadow-lg">
                  <FiAward className="h-4 w-4" />
                </div>
              )}
            </div>
          </div>

          {/* Event Content */}
          <div className="p-6">
            {/* Category Badge */}
            <div className="mb-4">
              <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getCategoryColor(event.category)}`}>
                {event.category}
              </span>
            </div>

            {/* Title */}
            <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 leading-tight">
              {event.title}
            </h3>

            {/* Description */}
            {event.short_description && (
              <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed">
                {event.short_description}
              </p>
            )}

            {/* Event Details */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm text-gray-600">
                <FiCalendar className="h-4 w-4 text-gray-500 mr-2" />
                <span>{formatDate(event.start_datetime)}</span>
              </div>

              <div className="flex items-center text-sm text-gray-600">
                <FiClock className="h-4 w-4 text-gray-500 mr-2" />
                <span>{formatTime(event.start_datetime)}</span>
              </div>

              {event.location && (
                <div className="flex items-center text-sm text-gray-600">
                  <FiMapPin className="h-4 w-4 text-gray-500 mr-2" />
                  <span className="truncate">{event.location}</span>
                </div>
              )}

              {event.max_attendees && (
                <div className="flex items-center text-sm text-gray-600">
                  <FiUsers className="h-4 w-4 text-gray-500 mr-2" />
                  <span>Max {event.max_attendees} attendees</span>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={handleViewDetails}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <FiEye className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">View Details</span>
                <span className="sm:hidden">View</span>
              </button>
              <button
                onClick={handleRegister}
                disabled={!canRegister}
                className={`flex-1 font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center ${
                  canRegister
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <FiUserPlus className="h-4 w-4 mr-2" />
                {canRegister ? 'Register' :
                  eventStatus.status === 'ended' ? 'Event Ended' :
                  'Registration Closed'}
              </button>
            </div>
          </div>
        </div>

        {/* Registration Modal */}
        <EventRegistrationModal
          isOpen={showRegistration}
          onClose={handleRegistrationClose}
          event={event}
        />
      </>
    );
  }

  // List view
  return (
    <>
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 ${
        variant === 'featured' ? 'ring-1 ring-blue-500' : ''
      }`}>
        <div className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-6">
            {/* Event Image */}
            <div className="flex-shrink-0 w-full sm:w-32 h-24 relative">
              {event.banner_image_url ? (
                <div className="w-full h-full bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={event.banner_image_url}
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                  <FiCalendar className="w-8 h-8 text-gray-400" />
                </div>
              )}

              {/* Status Badge - Show only the most important one */}
              <div className="absolute -top-2 -right-2">
                {eventStatus.status === 'ended' ? (
                  <EventTimingBadge event={event} size="small" showTimeRemaining={false} />
                ) : registrationStatus.status !== 'open' ? (
                  <RegistrationStatusBadge event={event} size="small" />
                ) : eventStatus.status === 'ongoing' ? (
                  <EventTimingBadge event={event} size="small" showTimeRemaining={false} />
                ) : null}
              </div>
            </div>

            {/* Event Content */}
            <div className="flex-1 min-w-0">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getCategoryColor(event.category)}`}>
                    {event.category}
                  </span>
                  {event.is_featured && (
                    <div className="bg-yellow-500 text-white p-1.5 rounded-full">
                      <FiStar className="h-3 w-3" />
                    </div>
                  )}
                  {event.is_competition && (
                    <div className="bg-orange-500 text-white p-1.5 rounded-full">
                      <FiAward className="h-3 w-3" />
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:ml-4 mt-4 sm:mt-0">
                  <button
                    onClick={handleViewDetails}
                    className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
                  >
                    <FiEye className="h-4 w-4 mr-1" />
                    View
                  </button>
                  <button
                    onClick={handleRegister}
                    disabled={!canRegister}
                    className={`px-4 py-2 font-medium rounded-lg transition-all duration-200 flex items-center justify-center ${
                      canRegister
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <FiUserPlus className="h-4 w-4 mr-1" />
                    {canRegister ? 'Register' :
                      eventStatus.status === 'ended' ? 'Event Ended' :
                      'Registration Closed'}
                  </button>
                </div>
              </div>

              {/* Title */}
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                {event.title}
              </h3>

              {/* Description */}
              {event.short_description && (
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {event.short_description}
                </p>
              )}

              {/* Event Details */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <FiCalendar className="h-3 w-3 text-gray-500 mr-1" />
                  <span>{formatDate(event.start_datetime)}</span>
                </div>

                <div className="flex items-center">
                  <FiClock className="h-3 w-3 text-gray-500 mr-1" />
                  <span>{formatTime(event.start_datetime)}</span>
                </div>

                {event.location && (
                  <div className="flex items-center">
                    <FiMapPin className="h-3 w-3 text-gray-500 mr-1" />
                    <span className="truncate">{event.location}</span>
                  </div>
                )}

                {event.max_attendees && (
                  <div className="flex items-center">
                    <FiUsers className="h-3 w-3 text-gray-500 mr-1" />
                    <span>Max {event.max_attendees} attendees</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Registration Modal */}
      <EventRegistrationModal
        isOpen={showRegistration}
        onClose={handleRegistrationClose}
        event={event}
      />
    </>
  );
};

export default PublicEventCard;
