import { useState, useCallback } from 'react';
import QuestionForm from './QuestionForm';

const QuestionFormManager = ({
  subjects,
  subjectId,
  setSubjectId,
  chaptersBySubject,
  topicsByChapter,
  subtopicsByTopic,
  chapterId,
  topicId,
  subtopicId,
  setChapterId,
  setTopicId,
  setSubtopicId,
  chaptersLoading,
  topicsLoading,
  subtopicsLoading,
  themeClasses,
  userType,
  gradeClasses,
  onQuestionAdd,
  onAIGenerate
}) => {
  // Question form state
  const [questionType, setQuestionType] = useState('MCQS');
  const [descType, setDescType] = useState('SHORT');
  const [questionForm, setQuestionForm] = useState({
    text: '',
    options: [
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false }
    ],
    answer: '',
    marks: 1,
    Level: 'EASY'
  });

  // AI Generation state
  const [aiNoOfQuestions, setAiNoOfQuestions] = useState(5);
  const [aiDifficultyMode, setAiDifficultyMode] = useState('mix');
  const [aiNoOfEasy, setAiNoOfEasy] = useState(2);
  const [aiNoOfMedium, setAiNoOfMedium] = useState(2);
  const [aiNoOfHard, setAiNoOfHard] = useState(1);

  // Submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form handlers
  const handleQuestionChange = useCallback((e) => {
    const { name, value } = e.target;
    setQuestionForm(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleOptionChange = useCallback((index, field, value) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => {
        if (i === index) {
          return { ...opt, [field]: value };
        } else if (field === 'is_correct' && value === true) {
          // Unselect other options when marking one as correct
          return { ...opt, is_correct: false };
        }
        return opt;
      })
    }));
  }, []);

  const handleAddQuestion = useCallback(async (e) => {
    e.preventDefault();

    if (isSubmitting) return;

    if (!questionForm.text.trim()) {
      console.log('Please enter question text');
      return;
    }

    if (questionType === 'MCQS') {
      const filledOptions = questionForm.options.filter(opt => opt.option_text.trim() !== '');
      if (filledOptions.length < 2) {
        console.log('Please provide at least 2 options for multiple choice questions');
        return;
      }
      const hasCorrectAnswer = questionForm.options.some(opt => opt.is_correct && opt.option_text.trim() !== '');
      if (!hasCorrectAnswer) {
        console.log('Please specify the correct answer');
        return;
      }
    }

    try {
      setIsSubmitting(true);

      const newQuestion = {
        id: Date.now(),
        text: questionForm.text,
        type: questionType,
        Type: questionType, // API expects capital T
        marks: parseInt(questionForm.marks) || 1,
        subject_id: subjectId,
        chapter_id: chapterId,
        topic_id: topicId || null,
        subtopic_id: subtopicId || null,
        Level: questionForm.Level,
        ...(questionType === 'MCQS' && {
          options: questionForm.options.filter(opt => opt.option_text.trim() !== ''),
          correct_answer: questionForm.options.find(opt => opt.is_correct)?.option_text || '',
          answer: questionForm.options.find(opt => opt.is_correct)?.option_text || '' // API expects answer field
        }),
        ...(questionType === 'DESCRIPTIVE' && {
          answer: questionForm.answer,
          desc_type: descType
        })
      };

      await onQuestionAdd(newQuestion);

      // Reset form
      setQuestionForm({
        text: '',
        options: [
          { option_text: '', is_correct: false },
          { option_text: '', is_correct: false },
          { option_text: '', is_correct: false },
          { option_text: '', is_correct: false }
        ],
        answer: '',
        marks: 1,
        Level: 'EASY'
      });

      console.log('Question added successfully!');
    } catch (error) {
      console.error('Failed to add question:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [questionForm, questionType, descType, subjectId, chapterId, topicId, subtopicId, onQuestionAdd, isSubmitting]);

  const handleAIGenerate = useCallback(async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      await onAIGenerate({
        questionType,
        aiNoOfQuestions,
        aiDifficultyMode,
        aiNoOfEasy,
        aiNoOfMedium,
        aiNoOfHard,
        subjectId,
        chapterId,
        topicId,
        subtopicId
      });
    } catch (error) {
      console.error('Failed to generate AI questions:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [questionType, aiNoOfQuestions, aiDifficultyMode, aiNoOfEasy, aiNoOfMedium, aiNoOfHard, subjectId, chapterId, topicId, subtopicId, onAIGenerate, isSubmitting]);

  return (
    <QuestionForm
      questionType={questionType}
      setQuestionType={setQuestionType}
      descType={descType}
      setDescType={setDescType}
      questionForm={questionForm}
      onQuestionChange={handleQuestionChange}
      onOptionChange={handleOptionChange}
      onQuestionAdd={handleAddQuestion}
      onAIGenerate={handleAIGenerate}
      subjects={subjects}
      subjectId={subjectId}
      setSubjectId={setSubjectId}
      chaptersBySubject={chaptersBySubject}
      topicsByChapter={topicsByChapter}
      subtopicsByTopic={subtopicsByTopic}
      chapterId={chapterId}
      topicId={topicId}
      subtopicId={subtopicId}
      setChapterId={setChapterId}
      setTopicId={setTopicId}
      setSubtopicId={setSubtopicId}
      chaptersLoading={chaptersLoading}
      topicsLoading={topicsLoading}
      subtopicsLoading={subtopicsLoading}
      themeClasses={themeClasses}
      isSubmitting={isSubmitting}
      userType={userType}
      gradeClasses={gradeClasses}
      // AI Generation props
      aiNoOfQuestions={aiNoOfQuestions}
      setAiNoOfQuestions={setAiNoOfQuestions}
      aiDifficultyMode={aiDifficultyMode}
      setAiDifficultyMode={setAiDifficultyMode}
      aiNoOfEasy={aiNoOfEasy}
      setAiNoOfEasy={setAiNoOfEasy}
      aiNoOfMedium={aiNoOfMedium}
      setAiNoOfMedium={setAiNoOfMedium}
      aiNoOfHard={aiNoOfHard}
      setAiNoOfHard={setAiNoOfHard}
    />
  );
};

export default QuestionFormManager;
