import React from 'react';
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fi<PERSON> } from 'react-icons/fi';
import MathText from '../ui/MathText';

const QuestionList = ({
  questions,
  onDeleteQuestion,
  onEditQuestion,
  onQuestionsChange,
  editingIndex,
  editingQuestion,
  onEditingQuestionChange,
  onEditingOptionChange,
  onSaveEdit,
  onCancelEdit,
  themeClasses
}) => {
  if (questions.length === 0) {
    return (
      <div className="text-center py-12 text-gray-500 dark:text-gray-400">
        <p className="text-lg">No questions added yet</p>
        <p className="text-sm">Add questions using the form above</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold mb-4">
        Questions ({questions.length})
      </h3>
      
      {questions.map((question, index) => (
        <div key={index} className={`border border-gray-200 dark:border-gray-700 rounded-lg p-6 ${themeClasses.bg}`}>
          {editingIndex === index ? (
            // Edit Mode
            <div className="space-y-4">
              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label}`}>Question Text</label>
                <textarea
                  value={editingQuestion.text}
                  onChange={(e) => onEditingQuestionChange('text', e.target.value)}
                  rows={3}
                  className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                />
              </div>

              {question.type === "MCQS" && (
                <div>
                  <label className={`block mb-2 font-medium ${themeClasses.label}`}>Options</label>
                  <div className="space-y-3">
                    {editingQuestion.options.map((opt, optIdx) => (
                      <div key={optIdx} className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <span className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-sm font-medium">
                          {String.fromCharCode(65 + optIdx)}
                        </span>
                        <input
                          type="text"
                          value={opt.option_text}
                          onChange={(e) => onEditingOptionChange(optIdx, "option_text", e.target.value)}
                          className={`flex-1 rounded-lg px-3 py-2 border focus:outline-none focus:ring-2 focus:ring-blue-500 ${themeClasses.input}`}
                        />
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name={`correct_option_edit_${index}`}
                            checked={opt.is_correct}
                            onChange={() => onEditingOptionChange(optIdx, "is_correct", true)}
                            className="text-blue-600"
                          />
                          <span className="text-sm text-gray-600 dark:text-gray-400">Correct</span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <label className={`block mb-2 font-medium ${themeClasses.label}`}>
                  {question.type === "MCQS" ? "Answer Explanation" : "Expected Answer"}
                </label>
                <textarea
                  value={editingQuestion.answer}
                  onChange={(e) => onEditingQuestionChange('answer', e.target.value)}
                  rows={2}
                  className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
                />
              </div>

              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <label className={`block mb-2 font-medium ${themeClasses.label}`}>Marks</label>
                  <input
                    type="number"
                    value={editingQuestion.marks}
                    onChange={(e) => onEditingQuestionChange('marks', parseInt(e.target.value))}
                    min={1}
                    max={100}
                    className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                  />
                </div>
                <div className="flex-1">
                  <label className={`block mb-2 font-medium ${themeClasses.label}`}>Difficulty Level</label>
                  <select
                    value={editingQuestion.Level}
                    onChange={(e) => onEditingQuestionChange('Level', e.target.value)}
                    className={`w-full rounded-lg px-3 py-2 border ${themeClasses.input}`}
                  >
                    <option value="EASY">Easy</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="HARD">Hard</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  onClick={onSaveEdit}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <FiCheck className="w-4 h-4" />
                  Save
                </button>
                <button
                  onClick={onCancelEdit}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <FiX className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            // View Mode
            <div>
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                      Q{index + 1}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      question.type === "MCQS"
                        ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                        : "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                    }`}>
                      {question.type === "MCQS"
                        ? "Multiple Choice"
                        : question.desc_type === "SHORT"
                        ? "Short Answer"
                        : "Long Answer"}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      question.Level === "EASY" 
                        ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                        : question.Level === "MEDIUM"
                        ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                        : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
                    }`}>
                      {question.Level}
                    </span>
                    <span className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded text-xs font-medium">
                      {question.marks} marks
                    </span>
                  </div>
                  <p className={`text-lg font-medium mb-4 question-text ${themeClasses.text}`}>
                    <MathText>{question.text}</MathText>
                  </p>
                </div>
                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => onEditQuestion(index)}
                    className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    title="Edit question"
                  >
                    <FiEdit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onDeleteQuestion(index)}
                    className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title="Delete question"
                  >
                    <FiTrash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {question.type === "MCQS" && question.options && (
                <div className="space-y-2 mb-4">
                  {question.options.map((opt, optIdx) => (
                    <div key={optIdx} className={`flex items-center gap-3 p-3 rounded-lg ${
                      opt.is_correct 
                        ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700"
                        : "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                    }`}>
                      <span className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                        opt.is_correct
                          ? "bg-green-600 text-white"
                          : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                      }`}>
                        {String.fromCharCode(65 + optIdx)}
                      </span>
                      <span className={`flex-1 option-text ${opt.is_correct ? "font-medium" : ""} ${themeClasses.text}`}>
                        <MathText>{opt.option_text}</MathText>
                      </span>
                      {opt.is_correct && (
                        <FiCheck className="w-4 h-4 text-green-600" />
                      )}
                    </div>
                  ))}
                </div>
              )}

              {question.answer && (
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <h4 className={`font-medium mb-2 ${themeClasses.text}`}>
                    {question.type === "MCQS" ? "Explanation:" : "Expected Answer:"}
                  </h4>
                  <p className={`text-sm ${themeClasses.text} opacity-80`}>
                    {question.answer}
                  </p>

                  {/* Display solution steps for AI-generated questions */}
                  {question.solution_steps && question.solution_steps.length > 0 && (
                    <div className="mt-4">
                      <h5 className={`font-medium mb-2 ${themeClasses.text}`}>
                        Solution Steps:
                      </h5>
                      <ol className="list-decimal list-inside space-y-1">
                        {question.solution_steps.map((step, stepIdx) => (
                          <li key={stepIdx} className={`text-sm ${themeClasses.text} opacity-80`}>
                            {step}
                          </li>
                        ))}
                      </ol>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default QuestionList;
