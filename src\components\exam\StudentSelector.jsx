import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchTeacherStudents } from '../../store/slices/ClassroomSlice';
import { FiUsers, FiUser, FiSearch, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';

const StudentSelector = ({
  assignmentType = 'classroom',
  onAssignmentTypeChange,
  selectedStudentIds = [],
  onSelectedStudentsChange,
  classId,
  onClassIdChange,
  classrooms = [],
  themeClasses = {
    bg: 'bg-white',
    text: 'text-gray-900',
    input: 'bg-gray-50 text-gray-900 border-gray-300',
    label: 'text-gray-700'
  },
  disabled = false
}) => {
  const dispatch = useDispatch();
  const { teacherStudents, loading: studentsLoading } = useSelector(state => state.classroom);
  const [searchTerm, setSearchTerm] = useState('');
  const [showStudentList, setShowStudentList] = useState(assignmentType === 'students');

  // Safe theme classes with defaults
  const safeThemeClasses = {
    bg: themeClasses?.bg || 'bg-white',
    text: themeClasses?.text || 'text-gray-900',
    input: themeClasses?.input || 'bg-gray-50 text-gray-900 border-gray-300',
    label: themeClasses?.label || 'text-gray-700'
  };

  // Fetch students when component mounts or classId changes
  useEffect(() => {
    if (classId && assignmentType === 'students') {
      dispatch(fetchTeacherStudents());
    }
  }, [dispatch, classId, assignmentType]);

  // Filter students based on search term
  const filteredStudents = useMemo(() => {
    if (!teacherStudents || !Array.isArray(teacherStudents)) return [];
    
    return teacherStudents.filter(student =>
      student.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [teacherStudents, searchTerm]);

  // Handle assignment type change
  const handleAssignmentTypeChange = (type) => {
    if (typeof onAssignmentTypeChange === 'function') {
      onAssignmentTypeChange(type);
    }
    if (type === 'classroom') {
      if (typeof onSelectedStudentsChange === 'function') {
        onSelectedStudentsChange([]);
      }
      setShowStudentList(false);
    } else {
      setShowStudentList(true);
    }
  };

  // Handle student selection
  const handleStudentToggle = (studentId) => {
    if (disabled) return;
    
    const newSelectedIds = selectedStudentIds.includes(studentId)
      ? selectedStudentIds.filter(id => id !== studentId)
      : [...selectedStudentIds, studentId];
    
    if (typeof onSelectedStudentsChange === 'function') {
      onSelectedStudentsChange(newSelectedIds);
    }
  };

  // Handle select all students
  const handleSelectAll = () => {
    if (disabled) return;
    
    const allStudentIds = filteredStudents.map(student => student.id);
    if (typeof onSelectedStudentsChange === 'function') {
      onSelectedStudentsChange(allStudentIds);
    }
  };

  // Handle clear all selections
  const handleClearAll = () => {
    if (disabled) return;
    
    if (typeof onSelectedStudentsChange === 'function') {
      onSelectedStudentsChange([]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Assignment Type Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <FiUsers className="w-5 h-5 text-purple-600" />
          Assignment Type
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Choose how to assign this exam to students
        </p>
      </div>
        
      <div className="space-y-3">
        {/* Assign to Whole Class Option */}
        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
          assignmentType === 'classroom' 
            ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-violet-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <input
            type="radio"
            name="assignmentType"
            value="classroom"
            checked={assignmentType === 'classroom'}
            onChange={() => handleAssignmentTypeChange('classroom')}
            disabled={disabled}
            className="w-4 h-4 text-violet-600 focus:ring-violet-500"
          />
          <div className="ml-3">
            <div className="flex items-center gap-2">
              <FiUsers className="w-4 h-4 text-violet-600" />
              <span className="font-medium text-gray-900 dark:text-gray-100">
                Assign to Entire Classroom
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              All students in the selected classroom will receive this exam
            </p>
          </div>
        </label>

        {/* Assign to Specific Students Option */}
        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
          assignmentType === 'students' 
            ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-green-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <input
            type="radio"
            name="assignmentType"
            value="students"
            checked={assignmentType === 'students'}
            onChange={() => handleAssignmentTypeChange('students')}
            disabled={disabled}
            className="w-4 h-4 text-green-600 focus:ring-green-500"
          />
          <div className="ml-3">
            <div className="flex items-center gap-2">
              <FiUser className="w-4 h-4 text-green-600" />
              <span className="font-medium text-gray-900 dark:text-gray-100">
                Assign to Specific Students
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Choose individual students from your classes to receive this exam
            </p>
          </div>
        </label>
      </div>

      {/* Classroom Selection */}
      {assignmentType === 'classroom' && (
        <div>
          <label className={`block mb-3 font-medium ${safeThemeClasses.label} flex items-center gap-2`}>
            <FiUsers className="w-4 h-4" />
            Select Classroom <span className="text-red-500">*</span>
          </label>
          <select
            value={classId || ''}
            onChange={(e) => onClassIdChange && onClassIdChange(e.target.value)}
            disabled={disabled}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${safeThemeClasses.input} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            required
          >
            <option value="">Select a classroom</option>
            {classrooms && classrooms.map(classroom => (
              <option key={classroom.id} value={classroom.id}>
                {classroom.name}
              </option>
            ))}
          </select>
          {(!classrooms || classrooms.length === 0) && (
            <p className="text-xs text-red-500 mt-1">No classrooms available</p>
          )}
        </div>
      )}

      {/* Student Selection */}
      {assignmentType === 'students' && showStudentList && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className={`font-medium ${safeThemeClasses.label} flex items-center gap-2`}>
              <FiUser className="w-4 h-4" />
              Select Students <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleSelectAll}
                disabled={disabled || filteredStudents.length === 0}
                className="text-xs px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Select All
              </button>
              <button
                type="button"
                onClick={handleClearAll}
                disabled={disabled || selectedStudentIds.length === 0}
                className="text-xs px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Search Input */}
          <div className="relative mb-3">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search students by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={disabled}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent ${safeThemeClasses.input} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            />
          </div>

          {/* Students List */}
          <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg">
            {studentsLoading ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                Loading students...
              </div>
            ) : filteredStudents.length > 0 ? (
              <div className="p-2 space-y-1">
                {filteredStudents.map(student => (
                  <label
                    key={student.id}
                    className={`flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedStudentIds.includes(student.id)}
                      onChange={() => handleStudentToggle(student.id)}
                      disabled={disabled}
                      className="w-4 h-4 text-violet-600 focus:ring-violet-500 border-gray-300 rounded"
                    />
                    <span className="ml-3 text-sm text-gray-900 dark:text-gray-100">
                      {student.username || student.name || student.email}
                    </span>
                    {selectedStudentIds.includes(student.id) && (
                      <FiCheck className="ml-auto w-4 h-4 text-green-500" />
                    )}
                  </label>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                {searchTerm ? 'No students found matching your search.' : 'No students available.'}
              </div>
            )}
          </div>

          {/* Selection Summary */}
          {selectedStudentIds.length > 0 && (
            <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
              <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                <FiCheck className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {selectedStudentIds.length} student{selectedStudentIds.length !== 1 ? 's' : ''} selected
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default StudentSelector;
