import React, { useCallback } from 'react';
import * as d3 from 'd3';
import BaseD3<PERSON><PERSON> from './BaseD3Chart';

/**
 * D3 Line Chart Component
 * Interactive line chart with hover effects, tooltips, and animations
 * Enhanced with modern styling and responsive design
 */
const D3LineChart = ({
  data = [],
  width = 600,
  height = 400,
  margin = { top: 30, right: 40, bottom: 60, left: 60 },
  xKey = 'x',
  yKey = 'y',
  lineColor,
  showDots = true,
  showArea = false,
  animate = true,
  curve = d3.curveMonotoneX,
  xAxisLabel = '',
  yAxisLabel = '',
  xTickFormat,
  yTickFormat,
  onPointHover,
  onPointClick,
  className = '',
  theme = 'light', // Add theme support
  gradient = true, // Add gradient support
  showPoints = true, // Rename showDots for consistency
  ...props
}) => {
  const renderChart = useCallback(({
    svg,
    g,
    data,
    colors,
    createScales,
    createAxes,
    createGrid,
    createTooltip,
    innerWidth,
    innerHeight
  }) => {
    // Prepare data
    const chartData = data.map(d => ({
      x: d[xKey],
      y: d[yKey],
      original: d
    }));

    // Create scales
    const { xScale, yScale } = createScales(chartData, {
      xType: typeof chartData[0]?.x === 'string' ? 'band' : 
             chartData[0]?.x instanceof Date ? 'time' : 'linear',
      yType: 'linear'
    });

    // Create grid
    createGrid(g, xScale, yScale);

    // Create axes
    createAxes(g, xScale, yScale, {
      xAxisLabel,
      yAxisLabel,
      xTickFormat,
      yTickFormat
    });

    // Create tooltip
    const tooltip = createTooltip();

    // Line generator
    const line = d3.line()
      .x(d => xScale(d.x))
      .y(d => yScale(d.y))
      .curve(curve);

    // Area generator (if showArea is true)
    let area;
    if (showArea) {
      area = d3.area()
        .x(d => xScale(d.x))
        .y0(yScale(0))
        .y1(d => yScale(d.y))
        .curve(curve);
    }

    // Add area if enabled
    if (showArea && area) {
      const areaPath = g.append('path')
        .datum(chartData)
        .attr('class', 'area')
        .attr('d', area)
        .style('fill', lineColor || colors.primary)
        .style('opacity', 0.3);

      if (animate) {
        areaPath
          .style('opacity', 0)
          .transition()
          .duration(1000)
          .style('opacity', 0.3);
      }
    }

    // Add line
    const linePath = g.append('path')
      .datum(chartData)
      .attr('class', 'line')
      .attr('d', line)
      .style('fill', 'none')
      .style('stroke', lineColor || colors.primary)
      .style('stroke-width', 2);

    if (animate) {
      const totalLength = linePath.node().getTotalLength();
      linePath
        .attr('stroke-dasharray', totalLength + ' ' + totalLength)
        .attr('stroke-dashoffset', totalLength)
        .transition()
        .duration(1500)
        .ease(d3.easeLinear)
        .attr('stroke-dashoffset', 0);
    }

    // Add dots if enabled
    if (showDots) {
      const dots = g.selectAll('.dot')
        .data(chartData)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => xScale(d.x))
        .attr('cy', d => yScale(d.y))
        .attr('r', 4)
        .style('fill', lineColor || colors.primary)
        .style('stroke', colors.background)
        .style('stroke-width', 2)
        .style('cursor', 'pointer');

      if (animate) {
        dots
          .attr('r', 0)
          .transition()
          .delay((d, i) => i * 100)
          .duration(500)
          .attr('r', 4);
      }

      // Add hover effects
      dots
        .on('mouseover', function(event, d) {
          d3.select(this)
            .transition()
            .duration(200)
            .attr('r', 6)
            .style('stroke-width', 3);

          tooltip
            .style('visibility', 'visible')
            .html(`
              <div>
                <strong>${xAxisLabel || 'X'}: ${d.x}</strong><br/>
                <strong>${yAxisLabel || 'Y'}: ${d.y}</strong>
              </div>
            `);

          if (onPointHover) {
            onPointHover(d.original, event);
          }
        })
        .on('mousemove', function(event) {
          tooltip
            .style('top', (event.pageY - 10) + 'px')
            .style('left', (event.pageX + 10) + 'px');
        })
        .on('mouseout', function() {
          d3.select(this)
            .transition()
            .duration(200)
            .attr('r', 4)
            .style('stroke-width', 2);

          tooltip.style('visibility', 'hidden');
        })
        .on('click', function(event, d) {
          if (onPointClick) {
            onPointClick(d.original, event);
          }
        });
    }

    // Add hover line (vertical line that follows mouse)
    const hoverLine = g.append('line')
      .attr('class', 'hover-line')
      .style('stroke', colors.textSecondary)
      .style('stroke-width', 1)
      .style('stroke-dasharray', '3,3')
      .style('opacity', 0);

    // Add invisible overlay for mouse tracking
    g.append('rect')
      .attr('width', innerWidth)
      .attr('height', innerHeight)
      .style('fill', 'none')
      .style('pointer-events', 'all')
      .on('mousemove', function(event) {
        const [mouseX] = d3.pointer(event);
        
        hoverLine
          .attr('x1', mouseX)
          .attr('x2', mouseX)
          .attr('y1', 0)
          .attr('y2', innerHeight)
          .style('opacity', 1);
      })
      .on('mouseout', function() {
        hoverLine.style('opacity', 0);
      });

  }, [
    xKey, yKey, lineColor, showDots, showArea, animate, curve,
    xAxisLabel, yAxisLabel, xTickFormat, yTickFormat,
    onPointHover, onPointClick
  ]);

  return (
    <BaseD3Chart
      data={data}
      width={width}
      height={height}
      margin={margin}
      onRender={renderChart}
      className={`d3-line-chart ${className}`}
      {...props}
    />
  );
};

export default D3LineChart;
