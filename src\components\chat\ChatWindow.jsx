import React, { useEffect, useRef, useState } from 'react';
import { FiArrowLeft, FiMoreVertical, FiUser } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';
import MessageBubble, { DateSeparator, TypingIndicator } from './MessageBubble';
import ChatInput from './ChatInput';

/**
 * ChatWindow Component
 * Main chat interface showing messages and input
 */
const ChatWindow = ({
  conversation,
  messages = [],
  currentUserId,
  onSendMessage,
  onDeleteMessage,
  onMarkAsRead,
  onBack,
  loading = false,
  sendingMessage = false,
  typingUsers = {},
  onTyping,
  className = ''
}) => {
  const { currentTheme } = useThemeProvider();
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle scroll to show/hide scroll-to-bottom button
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
      setShowScrollButton(!isNearBottom);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  // Mark conversation as read when messages are viewed
  useEffect(() => {
    if (conversation && messages.length > 0 && onMarkAsRead) {
      const hasUnreadMessages = messages.some(msg => 
        msg.sender_id !== currentUserId && !msg.read_at
      );
      
      if (hasUnreadMessages) {
        const timer = setTimeout(() => {
          onMarkAsRead(conversation.other_user.id);
        }, 1000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [conversation, messages, currentUserId, onMarkAsRead]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const groupMessagesByDate = (messages) => {
    const groups = [];
    let currentDate = null;
    let currentGroup = [];

    messages.forEach((message) => {
      const messageDate = new Date(message.sent_at).toDateString();
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: currentDate, messages: currentGroup });
        }
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate, messages: currentGroup });
    }

    return groups;
  };

  if (!conversation) {
    return (
      <div className={`flex-1 flex items-center justify-center ${className}`}>
        <div className="text-center">
          <FiUser className={`w-16 h-16 mx-auto mb-4 ${
            currentTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
          }`} />
          <h3 className={`text-lg font-medium mb-2 ${
            currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
          }`}>
            Select a conversation
          </h3>
          <p className={`text-sm ${
            currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-500'
          }`}>
            Choose a conversation from the list to start messaging
          </p>
        </div>
      </div>
    );
  }

  const { other_user } = conversation;

  // Sort messages by timestamp to ensure chronological order
  const sortedMessages = [...messages].sort((a, b) =>
    new Date(a.sent_at) - new Date(b.sent_at)
  );

  const messageGroups = groupMessagesByDate(sortedMessages);
  const isTyping = typingUsers[other_user.id];

  return (
    <div className={`flex-1 flex flex-col ${className}`}>
      {/* Chat header */}
      <div className={`
        flex items-center justify-between p-4 border-b
        ${currentTheme === 'dark' 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
        }
      `}>
        <div className="flex items-center space-x-3">
          {/* Back button (mobile) */}
          <button
            onClick={onBack}
            className={`
              lg:hidden p-2 rounded-lg transition-colors
              ${currentTheme === 'dark'
                ? 'hover:bg-gray-700 text-gray-400'
                : 'hover:bg-gray-100 text-gray-600'
              }
            `}
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>

          {/* User info */}
          <div className="flex items-center space-x-3">
            {/* Avatar */}
            <div className="relative">
              {other_user.profile_picture ? (
                <img
                  src={other_user.profile_picture}
                  alt={other_user.username}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center
                  ${currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'}
                `}>
                  <FiUser className="w-5 h-5 text-gray-500" />
                </div>
              )}
              
              {/* Online indicator */}
              {other_user.is_online && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
              )}
            </div>

            {/* Name and status */}
            <div>
              <h3 className={`font-medium ${
                currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-900'
              }`}>
                {other_user.username}
              </h3>
              <p className={`text-sm ${
                currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {other_user.is_online 
                  ? 'Online' 
                  : other_user.last_seen 
                    ? `Last seen ${new Date(other_user.last_seen).toLocaleDateString()}`
                    : 'Offline'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <button
            className={`
              p-2 rounded-lg transition-colors
              ${currentTheme === 'dark'
                ? 'hover:bg-gray-700 text-gray-400'
                : 'hover:bg-gray-100 text-gray-600'
              }
            `}
            title="More options"
          >
            <FiMoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Messages area */}
      <div 
        ref={messagesContainerRef}
        className={`
          flex-1 overflow-y-auto p-4 space-y-1
          ${currentTheme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}
        `}
      >
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          </div>
        ) : messageGroups.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <FiUser className={`w-12 h-12 mb-4 ${
              currentTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <h3 className={`text-lg font-medium mb-2 ${
              currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            }`}>
              No messages yet
            </h3>
            <p className={`text-sm text-center ${
              currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-500'
            }`}>
              Start the conversation by sending a message below
            </p>
          </div>
        ) : (
          <>
            {messageGroups.map((group, groupIndex) => (
              <div key={groupIndex}>
                <DateSeparator date={group.date} />
                {group.messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    isOwn={message.sender_id === currentUserId}
                    onDelete={onDeleteMessage}
                  />
                ))}
              </div>
            ))}
            
            {/* Typing indicator */}
            {isTyping && (
              <TypingIndicator username={other_user.username} />
            )}
          </>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      {showScrollButton && (
        <button
          onClick={scrollToBottom}
          className={`
            absolute bottom-20 right-6 p-2 rounded-full shadow-lg transition-all duration-200
            ${currentTheme === 'dark'
              ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              : 'bg-white hover:bg-gray-50 text-gray-600'
            }
          `}
        >
          ↓
        </button>
      )}

      {/* Chat input */}
      <div className={`
        p-4 border-t
        ${currentTheme === 'dark' 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
        }
      `}>
        <ChatInput
          onSendMessage={(message) => onSendMessage(other_user.id, message)}
          disabled={sendingMessage}
          placeholder={`Message ${other_user.username}...`}
          onTyping={onTyping}
        />
      </div>
    </div>
  );
};

export default ChatWindow;
