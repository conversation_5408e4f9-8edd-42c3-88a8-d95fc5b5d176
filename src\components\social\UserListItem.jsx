import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiUser, FiMapPin } from 'react-icons/fi';
import FollowButton from './FollowButton';
import { MessageButton } from '../chat';

/**
 * SocialUserListItem Component
 * Displays a user in a list with follow functionality
 */
const SocialUserListItem = ({
  user,
  showFollowButton = true,
  showMessageButton = true,
  onFollowChange = null,
  className = ''
}) => {
  const navigate = useNavigate();
  const currentUserId = localStorage.getItem('userId');
  const isCurrentUser = user.id === currentUserId;

  const handleUserClick = () => {
    navigate(`/profile/${user.id}`);
  };

  const handleFollowChange = (isFollowing, userId) => {
    onFollowChange?.(isFollowing, userId, user);
  };

  const getUserTypeColor = (userType) => {
    switch (userType?.toLowerCase()) {
      case 'student':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'teacher':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'institute':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'mentor':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <div className={`flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow ${className}`}>
      {/* User Info */}
      <div className="flex items-center gap-4 flex-1 min-w-0">
        {/* Avatar */}
        <button
          onClick={handleUserClick}
          className="flex-shrink-0 w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold text-lg hover:shadow-lg transition-shadow"
        >
          {user.profile_picture ? (
            <img
              src={user.profile_picture}
              alt={user.username}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <FiUser className="w-6 h-6" />
          )}
        </button>

        {/* User Details */}
        <div className="flex-1 min-w-0">
          <button
            onClick={handleUserClick}
            className="block text-left w-full"
          >
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                {user.username || 'Unknown User'}
              </h3>
              {user.user_type && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUserTypeColor(user.user_type)}`}>
                  {user.user_type}
                </span>
              )}
            </div>
            
            {user.email && (
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {user.email}
              </p>
            )}
            
            {user.country && (
              <div className="flex items-center gap-1 mt-1">
                <FiMapPin className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {user.country}
                </span>
              </div>
            )}
          </button>
        </div>
      </div>

      {/* Action Buttons */}
      {!isCurrentUser && (showFollowButton || showMessageButton) && (
        <div className="flex-shrink-0 ml-4 flex items-center gap-2">
          {showMessageButton && (
            <MessageButton
              userId={user.id}
              username={user.username}
              size="small"
              variant="outline"
            />
          )}
          {showFollowButton && (
            <FollowButton
              userId={user.id}
              onFollowChange={handleFollowChange}
              size="small"
              variant="outline"
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SocialUserListItem;
