import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Competition Service
 * Handles all competition-related API operations based on OpenAPI specification
 */

const BASE_URL = `${API_BASE_URL}/api/competitions`;
const getAuthToken = () => localStorage.getItem("token");

const getAuthHeaders = () => ({
  Authorization: `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Competition Management APIs
 */

// Get Available Exams for Competition Creation
export const getAvailableExamsForCompetition = async () => {
  try {
    const response = await axios.get(`${BASE_URL}/available-exams`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching available exams:', error);
    // Return mock data for development
    return {
      exams: [
        {
          id: 'exam-1',
          title: 'JavaScript Fundamentals',
          description: 'Basic JavaScript concepts and syntax',
          questions_count: 20,
          duration_minutes: 60,
          difficulty_level: 'beginner',
          created_by: '<PERSON>',
          created_at: '2024-01-15T10:00:00Z',
          usage_count: 3,
          last_used: '2024-11-20T14:30:00Z',
          tags: ['javascript', 'programming', 'fundamentals']
        },
        {
          id: 'exam-2',
          title: 'Advanced React Patterns',
          description: 'Complex React patterns and best practices',
          questions_count: 15,
          duration_minutes: 90,
          difficulty_level: 'advanced',
          created_by: 'Sarah Teacher',
          created_at: '2024-02-10T09:00:00Z',
          usage_count: 1,
          last_used: '2024-10-15T11:00:00Z',
          tags: ['react', 'javascript', 'advanced']
        },
        {
          id: 'exam-3',
          title: 'Data Structures & Algorithms',
          description: 'Comprehensive DSA assessment',
          questions_count: 25,
          duration_minutes: 120,
          difficulty_level: 'intermediate',
          created_by: 'Mike Mentor',
          created_at: '2024-03-05T16:00:00Z',
          usage_count: 5,
          last_used: '2024-12-01T10:00:00Z',
          tags: ['algorithms', 'data-structures', 'computer-science']
        }
      ]
    };
  }
};

// Create Competition with Exam Assignment
export const createCompetition = async (competitionData) => {
  try {
    const response = await axios.post(`${BASE_URL}/`, competitionData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error creating competition:', error);
    throw error;
  }
};

// Get Competition by ID
export const getCompetition = async (competitionId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching competition:', error);
    throw error;
  }
};

// Update Competition
export const updateCompetition = async (competitionId, updateData) => {
  try {
    const response = await axios.put(`${BASE_URL}/${competitionId}`, updateData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating competition:', error);
    throw error;
  }
};

// Delete Competition
export const deleteCompetition = async (competitionId) => {
  try {
    const response = await axios.delete(`${BASE_URL}/${competitionId}`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting competition:', error);
    throw error;
  }
};

// Get Institute Competitions
export const getInstituteCompetitions = async (instituteId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/institute/${instituteId}`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching institute competitions:', error);
    throw error;
  }
};

// Get Competition Statistics
export const getCompetitionStatistics = async (competitionId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/statistics`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching competition statistics:', error);
    throw error;
  }
};

/**
 * Mentor Management APIs
 */

// Assign Mentor to Competition
export const assignMentor = async (competitionId, mentorData) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/mentors`, mentorData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error assigning mentor:', error);
    throw error;
  }
};

// Get Competition Mentors
export const getCompetitionMentors = async (competitionId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/mentors`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching competition mentors:', error);
    throw error;
  }
};

// Auto Assign Mentors
export const autoAssignMentors = async (competitionId, assignmentData) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/mentors/auto-assign`, assignmentData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error auto-assigning mentors:', error);
    throw error;
  }
};

// Update Mentor Assignment
export const updateMentorAssignment = async (assignmentId, updateData) => {
  try {
    const response = await axios.put(`${BASE_URL}/mentors/${assignmentId}`, updateData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating mentor assignment:', error);
    throw error;
  }
};

// Get Mentor Workload
export const getMentorWorkload = async (mentorId) => {
  try {
    const response = await axios.get(`${BASE_URL}/mentors/${mentorId}/workload`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching mentor workload:', error);
    throw error;
  }
};

// Update Mentor Workload
export const updateMentorWorkload = async (assignmentId, workloadData) => {
  try {
    const response = await axios.put(`${BASE_URL}/mentors/assignments/${assignmentId}/workload`, workloadData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating mentor workload:', error);
    throw error;
  }
};

/**
 * Student Participation APIs
 */

// Register for Competition
export const registerForCompetition = async (competitionId, registrationData) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/register`, registrationData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error registering for competition:', error);
    throw error;
  }
};

// Get My Competition Submission
export const getMyCompetitionSubmission = async (competitionId) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/my-submission`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching my submission:', error);
    throw error;
  }
};

// Get Competition Leaderboard
export const getCompetitionLeaderboard = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/leaderboard`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    throw error;
  }
};

/**
 * Mentor Evaluation APIs
 */

// Get My Mentor Assignments
export const getMyMentorAssignments = async (params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/mentors/my-assignments`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching mentor assignments:', error);
    throw error;
  }
};

// Get Submissions to Evaluate
export const getSubmissionsToEvaluate = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/submissions-to-evaluate`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching submissions to evaluate:', error);
    throw error;
  }
};

// Get Submission Details
export const getSubmissionDetails = async (attemptId) => {
  try {
    const response = await axios.get(`${BASE_URL}/submissions/${attemptId}/details`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching submission details:', error);
    throw error;
  }
};

// Submit Mentor Evaluation
export const submitMentorEvaluation = async (attemptId, evaluationData) => {
  try {
    const response = await axios.post(`${BASE_URL}/submissions/${attemptId}/evaluate`, evaluationData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error submitting evaluation:', error);
    throw error;
  }
};

/**
 * Results Management APIs
 */

// Calculate Competition Results
export const calculateCompetitionResults = async (competitionId, calculationData = {}) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/calculate-results`, calculationData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error calculating competition results:', error);
    throw error;
  }
};

// Publish Competition Results
export const publishCompetitionResults = async (competitionId, publishData = {}) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/publish-results`, publishData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error publishing competition results:', error);
    throw error;
  }
};

// Get Competition Results
export const getCompetitionResults = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/results`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching competition results:', error);
    throw error;
  }
};

/**
 * Analytics APIs
 */

// Get Competition Analytics
export const getCompetitionAnalytics = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/analytics`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching competition analytics:', error);
    throw error;
  }
};

/**
 * Security and Monitoring APIs
 */

// Update Competition Security Settings
export const updateCompetitionSecurity = async (competitionId, securityData) => {
  try {
    const response = await axios.put(`${BASE_URL}/${competitionId}/security`, securityData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error updating competition security:', error);
    throw error;
  }
};

// Get Competition Security Violations
export const getCompetitionViolations = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/security/violations`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching security violations:', error);
    throw error;
  }
};

// Monitor Competition Session
export const monitorCompetitionSession = async (competitionId, monitoringData) => {
  try {
    const response = await axios.post(`${BASE_URL}/${competitionId}/monitor`, monitoringData, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error monitoring competition session:', error);
    throw error;
  }
};

// Get Competition Monitoring Dashboard
export const getCompetitionMonitoringDashboard = async (competitionId, params = {}) => {
  try {
    const response = await axios.get(`${BASE_URL}/${competitionId}/monitoring/dashboard`, {
      headers: getAuthHeaders(),
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching monitoring dashboard:', error);
    throw error;
  }
};

/**
 * Mentor Evaluation APIs (New from API Documentation)
 */

// Get Mentor Dashboard
export const getMentorDashboard = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/mentor/evaluation/dashboard`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching mentor dashboard:', error);
    throw error;
  }
};

// Get Assigned Competitions
export const getAssignedCompetitions = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/mentor/evaluation/competitions`, {
      headers: getAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching assigned competitions:', error);
    throw error;
  }
};

// Get Competition Submissions
export const getCompetitionSubmissions = async (competitionId, params = {}) => {
  try {
    const queryParams = new URLSearchParams();

    // Add query parameters
    if (params.skip !== undefined) queryParams.append('skip', params.skip);
    if (params.limit !== undefined) queryParams.append('limit', params.limit);
    if (params.status) queryParams.append('status', params.status);

    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/submissions?${queryParams}`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competition submissions:', error);
    throw error;
  }
};

// Get Submission Details
export const getMentorSubmissionDetails = async (attemptId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/${attemptId}`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching submission details:', error);
    throw error;
  }
};

// Mark Submission
export const markSubmission = async (markingData) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/mark`,
      markingData,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error marking submission:', error);
    throw error;
  }
};

// Get Submission Evaluation Status
export const getSubmissionEvaluationStatus = async (attemptId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/submissions/${attemptId}/status`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching submission status:', error);
    throw error;
  }
};

// Get Competition Evaluation Progress
export const getCompetitionEvaluationProgress = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/progress`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching evaluation progress:', error);
    throw error;
  }
};

// Get Competition Rankings
export const getCompetitionRankings = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/rankings`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competition rankings:', error);
    throw error;
  }
};

// Generate Winner Certificate
export const generateWinnerCertificate = async (competitionId, certificateData) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/certificates/generate`,
      certificateData,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error generating certificate:', error);
    throw error;
  }
};

// Get Competition Certificates List
export const getCompetitionCertificates = async (competitionId) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/mentor/evaluation/competitions/${competitionId}/certificates`,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching certificates:', error);
    throw error;
  }
};

export default {
  // Competition Management
  createCompetition,
  getCompetition,
  updateCompetition,
  deleteCompetition,
  getInstituteCompetitions,
  getCompetitionStatistics,

  // Mentor Management
  assignMentor,
  getCompetitionMentors,
  autoAssignMentors,
  updateMentorAssignment,
  getMentorWorkload,
  updateMentorWorkload,

  // Student Participation
  registerForCompetition,
  getMyCompetitionSubmission,
  getCompetitionLeaderboard,

  // Mentor Evaluation (Legacy)
  getMyMentorAssignments,
  getSubmissionsToEvaluate,
  getSubmissionDetails,
  submitMentorEvaluation,

  // Mentor Evaluation (New API)
  getMentorDashboard,
  getAssignedCompetitions,
  getCompetitionSubmissions,
  getMentorSubmissionDetails,
  markSubmission,
  getSubmissionEvaluationStatus,
  getCompetitionEvaluationProgress,
  getCompetitionRankings,
  generateWinnerCertificate,
  getCompetitionCertificates,

  // Results Management
  calculateCompetitionResults,
  publishCompetitionResults,
  getCompetitionResults,

  // Analytics
  getCompetitionAnalytics,

  // Security and Monitoring
  updateCompetitionSecurity,
  getCompetitionViolations,
  monitorCompetitionSession,
  getCompetitionMonitoringDashboard
};
