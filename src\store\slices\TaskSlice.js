import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

// Helper function to get auth token
const getAuthToken = () => localStorage.getItem("token") || sessionStorage.getItem("token");

// Helper function to get auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

// Map API status values to frontend submission_status values
const mapApiStatusToSubmissionStatus = (apiStatus, isGraded = false) => {
  // If the task is graded, always return 'graded' status
  if (isGraded) {
    return 'graded';
  }

  const statusMap = {
    'pending': 'not_submitted',
    'completed': 'submitted',
    'submitted': 'submitted',
    'graded': 'graded',
    'in_progress': 'in_progress',
    'draft': 'in_progress'
  };
  return statusMap[apiStatus] || 'not_submitted';
};

// Async thunks for task operations
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/`, {
        params,
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tasks');
    }
  }
);

export const fetchTasksByStudent = createAsyncThunk(
  'tasks/fetchTasksByStudent',
  async (params = {}, { rejectWithValue }) => {
    try {
      // Use the new student-specific tasks endpoint
      const response = await axios.get(`${API_BASE_URL}/api/tasks/my/tasks`, {
        params,
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student tasks');
    }
  }
);

export const fetchAllTasksWithFilters = createAsyncThunk(
  'tasks/fetchAllTasksWithFilters',
  async (filters = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/all/`, {
        params: filters,
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tasks with filters');
    }
  }
);

export const fetchTasksMinimal = createAsyncThunk(
  'tasks/fetchTasksMinimal',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/minimal/`, {
        params,
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch minimal tasks');
    }
  }
);

export const fetchTaskById = createAsyncThunk(
  'tasks/fetchTaskById',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task');
    }
  }
);

export const fetchTaskForEdit = createAsyncThunk(
  'tasks/fetchTaskForEdit',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/edit/`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task for edit');
    }
  }
);

export const fetchStudentTaskById = createAsyncThunk(
  'tasks/fetchStudentTaskById',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/my/${taskId}`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student task');
    }
  }
);

export const fetchStudentTaskWithData = createAsyncThunk(
  'tasks/fetchStudentTaskWithData',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/my/${taskId}/with-data`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student task with data');
    }
  }
);

export const fetchTaskForTeacher = createAsyncThunk(
  'tasks/fetchTaskForTeacher',
  async (taskId, { rejectWithValue }) => {
    try {
      // Use the regular task endpoint for teachers
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task for teacher');
    }
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task');
    }
  }
);

export const createTaskForStudent = createAsyncThunk(
  'tasks/createTaskForStudent',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-student`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for student');
    }
  }
);

export const createTaskForClassroom = createAsyncThunk(
  'tasks/createTaskForClassroom',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-classroom`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for classroom');
    }
  }
);

export const createTaskForMultipleStudents = createAsyncThunk(
  'tasks/createTaskForMultipleStudents',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-multiple-students`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for multiple students');
    }
  }
);

// Task creation with file attachments
export const createTaskForStudentWithFiles = createAsyncThunk(
  'tasks/createTaskForStudentWithFiles',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-student-with-files`, formData, {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for student with files');
    }
  }
);

export const createTaskForClassroomWithFiles = createAsyncThunk(
  'tasks/createTaskForClassroomWithFiles',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-classroom-with-files`, formData, {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for classroom with files');
    }
  }
);

export const createTaskForMultipleStudentsWithFiles = createAsyncThunk(
  'tasks/createTaskForMultipleStudentsWithFiles',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/for-multiple-students-with-files`, formData, {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task for multiple students with files');
    }
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ taskId, taskData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}/`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task');
    }
  }
);

export const updateTaskForTeacher = createAsyncThunk(
  'tasks/updateTaskForTeacher',
  async ({ taskId, taskData }, { rejectWithValue }) => {
    try {
      // Use the regular task update endpoint for teachers
      const response = await axios.put(`${API_BASE_URL}/api/tasks/${taskId}`, taskData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task for teacher');
    }
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (taskId, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/tasks/${taskId}`, {
        headers: getAuthHeaders()
      });
      return taskId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete task');
    }
  }
);

// Task submission operations
export const submitTask = createAsyncThunk(
  'tasks/submitTask',
  async ({ taskId, submissionData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/submit`, submissionData, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit task');
    }
  }
);

export const submitTaskWithFiles = createAsyncThunk(
  'tasks/submitTaskWithFiles',
  async ({ taskId, formData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/submit-with-files`, formData, {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit task with files');
    }
  }
);

export const fetchStudentTaskSubmission = createAsyncThunk(
  'tasks/fetchStudentTaskSubmission',
  async (taskId, { rejectWithValue }) => {
    try {
      // Use the new student-specific submission endpoint
      const response = await axios.get(`${API_BASE_URL}/api/tasks/my/${taskId}/submission`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch submission');
    }
  }
);

export const fetchTaskSubmissions = createAsyncThunk(
  'tasks/fetchTaskSubmissions',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/submissions`, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch submissions');
    }
  }
);

export const updateTaskStatus = createAsyncThunk(
  'tasks/updateTaskStatus',
  async ({ taskId, status }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/api/tasks/${taskId}/status/`, { status }, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task status');
    }
  }
);

// Grading operations
export const gradeTaskSubmission = createAsyncThunk(
  'tasks/gradeTaskSubmission',
  async ({ task_id, student_id, grading_data }, { rejectWithValue }) => {
    try {
      // Use task_id and student_id to construct the grading endpoint
      const response = await axios.post(`${API_BASE_URL}/api/tasks/${task_id}/submissions/${student_id}/grade/`, grading_data, {
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to grade submission');
    }
  }
);

// Attachment operations
export const uploadTaskAttachment = createAsyncThunk(
  'tasks/uploadTaskAttachment',
  async ({ taskId, file, attachmentType = 'task' }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('attachment_type', attachmentType);

      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/attachments/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload attachment');
    }
  }
);

export const uploadTeacherTaskAttachment = createAsyncThunk(
  'tasks/uploadTeacherTaskAttachment',
  async ({ taskId, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/teacher-attachments/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload teacher attachment');
    }
  }
);

export const fetchTaskAttachments = createAsyncThunk(
  'tasks/fetchTaskAttachments',
  async ({ taskId, attachmentType }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}/attachments/`, {
        params: { attachment_type: attachmentType },
        headers: getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch attachments');
    }
  }
);

export const deleteTaskAttachment = createAsyncThunk(
  'tasks/deleteTaskAttachment',
  async (attachmentId, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/api/tasks/attachments/${attachmentId}/`, {
        headers: getAuthHeaders()
      });
      return attachmentId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete attachment');
    }
  }
);

const initialState = {
  tasks: [],
  currentTask: null,
  submissions: [],
  currentSubmission: null,
  attachments: [],
  tasksLoading: false,
  tasksError: null,
  submissionLoading: false,
  submissionError: null,
  submissionStats: {
    total: 0,
    submitted: 0,
    pending: 0,
    graded: 0,
    averageScore: 0
  },
  gradingState: {
    loading: false,
    error: null,
    success: false
  },
  attachmentLoading: false,
  attachmentError: null,
  createLoading: false,
  createError: null,
  updateLoading: false,
  updateError: null
};

const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearTaskState: (state) => {
      state.tasksError = null;
      state.createError = null;
      state.updateError = null;
    },
    clearSubmissionState: (state) => {
      state.submissionError = null;
      state.currentSubmission = null;
    },
    clearGradingState: (state) => {
      state.gradingState = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearAttachmentState: (state) => {
      state.attachmentError = null;
    },
    setCurrentTask: (state, action) => {
      state.currentTask = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch tasks
      .addCase(fetchTasks.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.tasksLoading = false;
        // Handle both array response and object response with tasks property
        if (Array.isArray(action.payload)) {
          state.tasks = action.payload;
        } else if (action.payload && Array.isArray(action.payload.tasks)) {
          state.tasks = action.payload.tasks;
        } else {
          state.tasks = [];
        }
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch tasks by student
      .addCase(fetchTasksByStudent.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasksByStudent.fulfilled, (state, action) => {
        state.tasksLoading = false;
        // Handle both array response and object response with tasks property
        let tasks = [];
        if (Array.isArray(action.payload)) {
          tasks = action.payload;
        } else if (action.payload && Array.isArray(action.payload.tasks)) {
          tasks = action.payload.tasks;
        }

        // Map API status to frontend submission_status
        state.tasks = tasks.map(task => ({
          ...task,
          submission_status: mapApiStatusToSubmissionStatus(task.status, task.graded)
        }));
      })
      .addCase(fetchTasksByStudent.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch all tasks with filters
      .addCase(fetchAllTasksWithFilters.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchAllTasksWithFilters.fulfilled, (state, action) => {
        state.tasksLoading = false;
        // Handle both array response and object response with tasks property
        if (Array.isArray(action.payload)) {
          state.tasks = action.payload;
        } else if (action.payload && Array.isArray(action.payload.tasks)) {
          state.tasks = action.payload.tasks;
        } else {
          state.tasks = [];
        }
      })
      .addCase(fetchAllTasksWithFilters.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch tasks minimal
      .addCase(fetchTasksMinimal.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTasksMinimal.fulfilled, (state, action) => {
        state.tasksLoading = false;
        // Handle both array response and object response with tasks property
        let tasks = [];
        if (Array.isArray(action.payload)) {
          tasks = action.payload;
        } else if (action.payload && Array.isArray(action.payload.tasks)) {
          tasks = action.payload.tasks;
        }

        // Map API status to frontend submission_status for teacher tasks too
        state.tasks = tasks.map(task => ({
          ...task,
          submission_status: mapApiStatusToSubmissionStatus(task.status, task.graded)
        }));
      })
      .addCase(fetchTasksMinimal.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task by ID
      .addCase(fetchTaskById.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskById.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskById.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task for edit
      .addCase(fetchTaskForEdit.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskForEdit.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskForEdit.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch student task by ID
      .addCase(fetchStudentTaskById.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchStudentTaskById.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchStudentTaskById.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch student task with data
      .addCase(fetchStudentTaskWithData.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchStudentTaskWithData.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchStudentTaskWithData.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Fetch task for teacher
      .addCase(fetchTaskForTeacher.pending, (state) => {
        state.tasksLoading = true;
        state.tasksError = null;
      })
      .addCase(fetchTaskForTeacher.fulfilled, (state, action) => {
        state.tasksLoading = false;
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskForTeacher.rejected, (state, action) => {
        state.tasksLoading = false;
        state.tasksError = action.payload;
      })

      // Create task
      .addCase(createTask.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.createLoading = false;
        state.tasks.push(action.payload);
      })
      .addCase(createTask.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })
      
      // Create task for student
      .addCase(createTaskForStudent.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForStudent.fulfilled, (state, action) => {
        state.createLoading = false;
        state.tasks.push(action.payload);
      })
      .addCase(createTaskForStudent.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for classroom
      .addCase(createTaskForClassroom.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForClassroom.fulfilled, (state, action) => {
        state.createLoading = false;
        if (Array.isArray(action.payload)) {
          state.tasks.push(...action.payload);
        } else {
          state.tasks.push(action.payload);
        }
      })
      .addCase(createTaskForClassroom.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for multiple students
      .addCase(createTaskForMultipleStudents.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForMultipleStudents.fulfilled, (state, action) => {
        state.createLoading = false;
        if (Array.isArray(action.payload)) {
          state.tasks.push(...action.payload);
        } else {
          state.tasks.push(action.payload);
        }
      })
      .addCase(createTaskForMultipleStudents.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for student with files
      .addCase(createTaskForStudentWithFiles.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForStudentWithFiles.fulfilled, (state, action) => {
        state.createLoading = false;
        // The response contains { task, uploaded_files }
        if (action.payload.task) {
          state.tasks.push(action.payload.task);
        }
      })
      .addCase(createTaskForStudentWithFiles.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for classroom with files
      .addCase(createTaskForClassroomWithFiles.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForClassroomWithFiles.fulfilled, (state, action) => {
        state.createLoading = false;
        // The response contains { task, uploaded_files }
        if (action.payload.task) {
          state.tasks.push(action.payload.task);
        }
      })
      .addCase(createTaskForClassroomWithFiles.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Create task for multiple students with files
      .addCase(createTaskForMultipleStudentsWithFiles.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
      })
      .addCase(createTaskForMultipleStudentsWithFiles.fulfilled, (state, action) => {
        state.createLoading = false;
        // The response contains { task, uploaded_files }
        if (action.payload.task) {
          state.tasks.push(action.payload.task);
        }
      })
      .addCase(createTaskForMultipleStudentsWithFiles.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Update task
      .addCase(updateTask.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
      })
      .addCase(updateTask.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.tasks.findIndex(task => task.id === action.payload.id);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
        if (state.currentTask && state.currentTask.id === action.payload.id) {
          state.currentTask = action.payload;
        }
      })
      .addCase(updateTask.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Update task for teacher
      .addCase(updateTaskForTeacher.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
      })
      .addCase(updateTaskForTeacher.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.tasks.findIndex(task => task.id === action.payload.id);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
        if (state.currentTask && state.currentTask.id === action.payload.id) {
          state.currentTask = action.payload;
        }
      })
      .addCase(updateTaskForTeacher.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Submit task
      .addCase(submitTask.pending, (state) => {
        state.submissionLoading = true;
        state.submissionError = null;
      })
      .addCase(submitTask.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.currentSubmission = action.payload;
      })
      .addCase(submitTask.rejected, (state, action) => {
        state.submissionLoading = false;
        state.submissionError = action.payload;
      })

      // Submit task with files
      .addCase(submitTaskWithFiles.pending, (state) => {
        state.submissionLoading = true;
        state.submissionError = null;
      })
      .addCase(submitTaskWithFiles.fulfilled, (state, action) => {
        state.submissionLoading = false;
        // Handle both submission object and response with submission property
        if (action.payload && action.payload.submission) {
          state.currentSubmission = action.payload.submission;
        } else {
          state.currentSubmission = action.payload;
        }
      })
      .addCase(submitTaskWithFiles.rejected, (state, action) => {
        state.submissionLoading = false;
        state.submissionError = action.payload;
      })

      // Fetch submissions
      .addCase(fetchTaskSubmissions.pending, (state) => {
        state.submissionLoading = true;
        state.submissionError = null;
      })
      .addCase(fetchTaskSubmissions.fulfilled, (state, action) => {
        state.submissionLoading = false;
        // Handle both array response and object response with submissions property
        if (Array.isArray(action.payload)) {
          state.submissions = action.payload;
        } else if (action.payload && Array.isArray(action.payload.submissions)) {
          state.submissions = action.payload.submissions;
        } else {
          state.submissions = [];
        }
      })
      .addCase(fetchTaskSubmissions.rejected, (state, action) => {
        state.submissionLoading = false;
        state.submissionError = action.payload;
      })
      
      // Grade submission
      .addCase(gradeTaskSubmission.pending, (state) => {
        state.gradingState.loading = true;
        state.gradingState.error = null;
        state.gradingState.success = false;
      })
      .addCase(gradeTaskSubmission.fulfilled, (state, action) => {
        state.gradingState.loading = false;
        state.gradingState.success = true;
        // Update the submission in the submissions array
        if (Array.isArray(state.submissions)) {
          const index = state.submissions.findIndex(sub => sub.id === action.payload.id);
          if (index !== -1) {
            state.submissions[index] = action.payload;
          }
        }
      })
      .addCase(gradeTaskSubmission.rejected, (state, action) => {
        state.gradingState.loading = false;
        state.gradingState.error = action.payload;
      })
      
      // Upload attachment
      .addCase(uploadTaskAttachment.pending, (state) => {
        state.attachmentLoading = true;
        state.attachmentError = null;
      })
      .addCase(uploadTaskAttachment.fulfilled, (state, action) => {
        state.attachmentLoading = false;
        state.attachments.push(action.payload);
      })
      .addCase(uploadTaskAttachment.rejected, (state, action) => {
        state.attachmentLoading = false;
        state.attachmentError = action.payload;
      })
      
      // Fetch attachments
      .addCase(fetchTaskAttachments.fulfilled, (state, action) => {
        state.attachments = action.payload;
      });
  }
});

// Export actions
export const {
  clearTaskState,
  clearSubmissionState,
  clearGradingState,
  clearAttachmentState,
  setCurrentTask
} = taskSlice.actions;

// Export selectors
export const selectTasks = (state) => state.tasks.tasks;
export const selectCurrentTask = (state) => state.tasks.currentTask;
export const selectTasksLoading = (state) => state.tasks.tasksLoading;
export const selectTasksError = (state) => state.tasks.tasksError;
export const selectSubmissions = (state) => {
  const submissions = state.tasks.submissions;
  return Array.isArray(submissions) ? submissions : [];
};
export const selectCurrentSubmission = (state) => state.tasks.currentSubmission;
export const selectSubmissionLoading = (state) => state.tasks.submissionLoading;
export const selectSubmissionError = (state) => state.tasks.submissionError;
export const selectSubmissionStats = (state) => {
  const submissions = state.tasks.submissions;
  // Ensure submissions is always an array
  if (!submissions || !Array.isArray(submissions) || submissions.length === 0) {
    return {
      total: 0,
      submitted: 0,
      pending: 0,
      graded: 0,
      averageScore: 0
    };
  }

  const total = submissions.length;
  const submitted = submissions.filter(sub => sub.status === 'submitted').length;
  const pending = submissions.filter(sub => sub.status === 'pending').length;
  const graded = submissions.filter(sub => sub.grade !== null).length;
  const averageScore = graded > 0
    ? submissions
        .filter(sub => sub.grade !== null)
        .reduce((sum, sub) => sum + sub.grade, 0) / graded
    : 0;

  return {
    total,
    submitted,
    pending,
    graded,
    averageScore: Math.round(averageScore * 100) / 100
  };
};
export const selectGradingState = (state) => state.tasks.gradingState;
export const selectAttachments = (state) => state.tasks.attachments;
export const selectAttachmentLoading = (state) => state.tasks.attachmentLoading;
export const selectAttachmentError = (state) => state.tasks.attachmentError;
export const selectCreateLoading = (state) => state.tasks.createLoading;
export const selectCreateError = (state) => state.tasks.createError;

export default taskSlice.reducer;
