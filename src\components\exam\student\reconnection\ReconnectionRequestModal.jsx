/**
 * Reconnection Request Modal
 * Allows students to request reconnection when disconnected from exam session
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../../../providers/ThemeContext';
import { useNotification } from '../../../../contexts/NotificationContext';
import {
  requestReconnection,
  checkReconnectionStatus,
  startStatusPolling,
  stopStatusPolling,
  selectReconnectionState,
  selectCurrentRequest,
  selectRequestStatus
} from '../../../../store/slices/exam/examReconnectionSlice';
import {
  FiWifi,
  FiWifiOff,
  FiClock,
  FiCheckCircle,
  FiXCircle,
  FiLoader,
  FiRefreshCw,
  FiAlertTriangle
} from 'react-icons/fi';

const ReconnectionRequestModal = ({ 
  isOpen, 
  onClose, 
  sessionId, 
  examTitle,
  onReconnected 
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { showSuccess, showError } = useNotification();
  
  const reconnectionState = useSelector(selectReconnectionState);
  const currentRequest = useSelector(selectCurrentRequest);
  const requestStatus = useSelector(selectRequestStatus);
  
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusCheckInterval, setStatusCheckInterval] = useState(null);

  // Theme-based styling
  const isDark = currentTheme === 'dark';
  const bgColor = isDark ? 'bg-gray-800' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-gray-900';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';

  // Handle reconnection request submission
  const handleSubmitRequest = async () => {
    if (!reason.trim()) {
      showError('Please provide a reason for reconnection');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await dispatch(requestReconnection({
        sessionId,
        reason: reason.trim()
      })).unwrap();

      showSuccess('Reconnection request submitted successfully');
      
      // Start polling for status updates
      dispatch(startStatusPolling({ interval: 5000 }));
      startStatusPolling(result.request_id);
      
    } catch (error) {
      showError(error.message || 'Failed to submit reconnection request');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Start status polling
  const startStatusPolling = (requestId) => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
    }

    const interval = setInterval(async () => {
      try {
        await dispatch(checkReconnectionStatus({ requestId })).unwrap();
      } catch (error) {
        console.error('Status check failed:', error);
      }
    }, 5000);

    setStatusCheckInterval(interval);
  };

  // Handle status changes
  useEffect(() => {
    if (requestStatus === 'approved') {
      showSuccess('Reconnection approved! Resuming exam session...');
      if (onReconnected) {
        onReconnected();
      }
      handleClose();
    } else if (requestStatus === 'denied') {
      showError('Reconnection request was denied');
    }
  }, [requestStatus, onReconnected, showSuccess, showError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
      dispatch(stopStatusPolling());
    };
  }, [statusCheckInterval, dispatch]);

  const handleClose = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
    }
    dispatch(stopStatusPolling());
    setReason('');
    setIsSubmitting(false);
    onClose();
  };

  const getStatusIcon = () => {
    switch (requestStatus) {
      case 'pending':
        return <FiClock className="w-6 h-6 text-yellow-500 animate-pulse" />;
      case 'approved':
        return <FiCheckCircle className="w-6 h-6 text-green-500" />;
      case 'denied':
        return <FiXCircle className="w-6 h-6 text-red-500" />;
      default:
        return <FiWifiOff className="w-6 h-6 text-red-500" />;
    }
  };

  const getStatusMessage = () => {
    switch (requestStatus) {
      case 'pending':
        return 'Your reconnection request is being reviewed by the teacher...';
      case 'approved':
        return 'Reconnection approved! Resuming your exam session...';
      case 'denied':
        return 'Your reconnection request was denied. Please contact your teacher.';
      default:
        return 'You have been disconnected from the exam session.';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${bgColor} rounded-xl shadow-2xl max-w-md w-full border ${borderColor}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <h3 className={`text-lg font-semibold ${textColor}`}>
                Connection Lost
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {examTitle}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Status Message */}
          <div className="mb-6">
            <p className={`text-sm ${textColor} mb-4`}>
              {getStatusMessage()}
            </p>

            {/* Request Form */}
            {!currentRequest && requestStatus === 'idle' && (
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium ${textColor} mb-2`}>
                    Reason for disconnection
                  </label>
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="Please describe what happened (e.g., internet connection lost, browser crashed, etc.)"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      isDark 
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    rows={3}
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {reason.length}/500 characters
                  </p>
                </div>
              </div>
            )}

            {/* Status Display */}
            {currentRequest && (
              <div className={`p-4 rounded-lg border ${borderColor} bg-gray-50 dark:bg-gray-700`}>
                <div className="flex items-center gap-2 mb-2">
                  {requestStatus === 'pending' && (
                    <FiLoader className="w-4 h-4 text-blue-500 animate-spin" />
                  )}
                  <span className={`text-sm font-medium ${textColor}`}>
                    Request ID: {currentRequest.request_id}
                  </span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Submitted: {new Date(currentRequest.timestamp || Date.now()).toLocaleTimeString()}
                </p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            {!currentRequest && requestStatus === 'idle' && (
              <>
                <button
                  onClick={handleSubmitRequest}
                  disabled={isSubmitting || !reason.trim()}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <FiLoader className="w-4 h-4 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <FiWifi className="w-4 h-4" />
                      Request Reconnection
                    </>
                  )}
                </button>
                <button
                  onClick={handleClose}
                  className={`px-4 py-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 ${borderColor} ${textColor}`}
                >
                  Cancel
                </button>
              </>
            )}

            {requestStatus === 'pending' && (
              <button
                onClick={() => dispatch(checkReconnectionStatus({ requestId: currentRequest.request_id }))}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2"
              >
                <FiRefreshCw className="w-4 h-4" />
                Check Status
              </button>
            )}

            {(requestStatus === 'denied' || requestStatus === 'approved') && (
              <button
                onClick={handleClose}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Close
              </button>
            )}
          </div>

          {/* Warning */}
          {requestStatus === 'denied' && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-start gap-2">
                <FiAlertTriangle className="w-4 h-4 text-red-500 mt-0.5" />
                <div>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    Your reconnection request was denied. Please contact your teacher for assistance.
                  </p>
                  {reconnectionState.reconnectionReason && (
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      Reason: {reconnectionState.reconnectionReason}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReconnectionRequestModal;
