import React, { useState } from 'react';
import {
  FiSearch,
  FiCheckCircle,
  FiXCircle,
  FiAlertTriangle,
  FiAward,
  FiCalendar,
  FiUser,
  FiFileText,
  FiShield
} from 'react-icons/fi';
import { verifyCertificate } from '../../services/mentorEvaluationService';
import { LoadingSpinner } from '../ui';

const CertificateVerification = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [verificationResult, setVerificationResult] = useState(null);
  const [error, setError] = useState(null);

  const handleVerify = async () => {
    if (!verificationCode.trim()) {
      setError('Please enter a verification code');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setVerificationResult(null);

      const result = await verifyCertificate(verificationCode.trim());
      setVerificationResult(result);
    } catch (err) {
      console.error('Certificate verification error:', err);
      if (err.response?.status === 404) {
        setError('Certificate not found. Please check the verification code and try again.');
      } else {
        setError('Failed to verify certificate. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleVerify();
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getPositionText = (position) => {
    switch (position) {
      case 1: return '1st';
      case 2: return '2nd';
      case 3: return '3rd';
      default: return `${position}th`;
    }
  };

  const getPositionEmoji = (position) => {
    switch (position) {
      case 1: return '🏆';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return '🏅';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <FiShield className="h-12 w-12 text-blue-600 mr-3" />
          <h1 className="text-3xl font-bold text-gray-900">Certificate Verification</h1>
        </div>
        <p className="text-lg text-gray-600">
          Verify the authenticity of educational certificates using their verification codes
        </p>
      </div>

      {/* Verification Input */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700 mb-2">
              Verification Code
            </label>
            <input
              id="verification-code"
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter certificate verification code (e.g., CERT-DD2B355C-3BE0F4E1-1)"
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-1">
              The verification code can be found on the certificate document
            </p>
          </div>
          <div className="flex items-end">
            <button
              onClick={handleVerify}
              disabled={loading || !verificationCode.trim()}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <FiSearch className="h-5 w-5 mr-2" />
              )}
              {loading ? 'Verifying...' : 'Verify'}
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiXCircle className="h-5 w-5 text-red-600 mr-2" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Verification Result */}
      {verificationResult && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Success Header */}
          <div className="bg-green-50 border-b border-green-200 p-6">
            <div className="flex items-center">
              <FiCheckCircle className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <h2 className="text-2xl font-bold text-green-800">Certificate Verified</h2>
                <p className="text-green-700">This certificate is authentic and valid</p>
              </div>
            </div>
          </div>

          {/* Certificate Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Certificate Info */}
              <div className="space-y-4">
                <div className="flex items-center text-center">
                  <div className="text-4xl mr-3">
                    {getPositionEmoji(verificationResult.rank_position || verificationResult.position)}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {verificationResult.participant_name || verificationResult.student_name || 'Certificate Holder'}
                    </h3>
                    <p className="text-gray-600">
                      {getPositionText(verificationResult.rank_position || verificationResult.position)} Place Winner
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Competition Details</h4>
                  <p className="text-gray-700">
                    {verificationResult.competition_title || verificationResult.competition_name || 'Competition'}
                  </p>
                  {verificationResult.competition_description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {verificationResult.competition_description}
                    </p>
                  )}
                </div>

                {verificationResult.custom_message && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">Achievement Message</h4>
                    <p className="text-blue-800 italic">"{verificationResult.custom_message}"</p>
                  </div>
                )}
              </div>

              {/* Right Column - Verification Details */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center mb-1">
                      <FiAward className="h-4 w-4 text-gray-600 mr-1" />
                      <span className="text-sm font-medium text-gray-700">Final Score</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">
                      {verificationResult.final_score || 'N/A'}
                    </p>
                    {verificationResult.score_percentage && (
                      <p className="text-sm text-gray-600">
                        {verificationResult.score_percentage}%
                      </p>
                    )}
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center mb-1">
                      <FiCalendar className="h-4 w-4 text-gray-600 mr-1" />
                      <span className="text-sm font-medium text-gray-700">Issue Date</span>
                    </div>
                    <p className="text-lg font-bold text-gray-900">
                      {formatDate(verificationResult.issued_date || verificationResult.issued_at)}
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-3">Verification Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <FiFileText className="h-4 w-4 text-gray-600 mr-2" />
                      <span className="text-gray-700">Certificate ID:</span>
                      <span className="ml-2 font-mono text-gray-900">
                        {verificationResult.certificate_id}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <FiShield className="h-4 w-4 text-gray-600 mr-2" />
                      <span className="text-gray-700">Verification Code:</span>
                      <span className="ml-2 font-mono text-gray-900">
                        {verificationResult.verification_code}
                      </span>
                    </div>
                    {verificationResult.evaluator_name && (
                      <div className="flex items-center">
                        <FiUser className="h-4 w-4 text-gray-600 mr-2" />
                        <span className="text-gray-700">Evaluated by:</span>
                        <span className="ml-2 text-gray-900">
                          {verificationResult.evaluator_name}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <FiCheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-gray-700">Status:</span>
                      <span className="ml-2 text-green-600 font-medium">
                        {verificationResult.status || 'Verified'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 border-t border-gray-200 p-4">
            <div className="flex items-center justify-center text-sm text-gray-600">
              <FiShield className="h-4 w-4 mr-1" />
              This certificate has been verified as authentic and issued by the educational institution.
            </div>
          </div>
        </div>
      )}

      {/* Information Section */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">How Certificate Verification Works</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
          <div className="flex items-start">
            <FiSearch className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
            <div>
              <p className="font-medium">1. Enter Code</p>
              <p>Input the verification code found on your certificate</p>
            </div>
          </div>
          <div className="flex items-start">
            <FiShield className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
            <div>
              <p className="font-medium">2. Verify Authenticity</p>
              <p>Our system checks the certificate against our secure database</p>
            </div>
          </div>
          <div className="flex items-start">
            <FiCheckCircle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
            <div>
              <p className="font-medium">3. View Details</p>
              <p>See complete certificate information and verification status</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateVerification;
