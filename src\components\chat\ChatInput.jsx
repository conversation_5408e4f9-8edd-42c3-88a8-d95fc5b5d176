import React, { useState, useRef, useEffect } from 'react';
import { FiSend, FiPaperclip, FiSmile } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';

/**
 * ChatInput Component
 * Input field for typing and sending messages
 */
const ChatInput = ({
  onSendMessage,
  disabled = false,
  placeholder = "Type a message...",
  maxLength = 1000,
  onTyping,
  className = ''
}) => {
  const { currentTheme } = useThemeProvider();
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Handle typing indicator
  useEffect(() => {
    if (onTyping) {
      if (message.trim() && !isTyping) {
        setIsTyping(true);
        onTyping(true);
      }

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set new timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        if (isTyping) {
          setIsTyping(false);
          onTyping(false);
        }
      }, 1000);
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, isTyping, onTyping]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const trimmedMessage = message.trim();
    if (!trimmedMessage || disabled) return;

    onSendMessage(trimmedMessage);
    setMessage('');
    
    // Stop typing indicator
    if (isTyping && onTyping) {
      setIsTyping(false);
      onTyping(false);
    }

    // Focus back to input
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
    }
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <div className={`${className}`}>
      <form onSubmit={handleSubmit} className="flex items-end space-x-2">
        {/* Message input */}
        <div className={`
          flex-1 relative border rounded-lg overflow-hidden
          ${currentTheme === 'dark' 
            ? 'border-gray-600 bg-gray-700' 
            : 'border-gray-300 bg-white'
          }
          ${disabled ? 'opacity-50' : ''}
        `}>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={`
              w-full px-3 py-2 pr-12 resize-none border-none outline-none bg-transparent
              ${currentTheme === 'dark' 
                ? 'text-gray-100 placeholder-gray-400' 
                : 'text-gray-900 placeholder-gray-500'
              }
              ${disabled ? 'cursor-not-allowed' : ''}
            `}
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />

          {/* Character count */}
          {message.length > maxLength * 0.8 && (
            <div className={`
              absolute bottom-1 right-12 text-xs
              ${message.length >= maxLength 
                ? 'text-red-500' 
                : currentTheme === 'dark' 
                  ? 'text-gray-400' 
                  : 'text-gray-500'
              }
            `}>
              {message.length}/{maxLength}
            </div>
          )}

          {/* Attachment button (placeholder for future feature) */}
          <button
            type="button"
            disabled={disabled}
            className={`
              absolute bottom-2 right-2 p-1 rounded-md transition-colors
              ${disabled 
                ? 'cursor-not-allowed opacity-50' 
                : currentTheme === 'dark'
                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-600'
                  : 'text-gray-500 hover:text-gray-600 hover:bg-gray-100'
              }
            `}
            title="Attach file (coming soon)"
          >
            <FiPaperclip className="w-4 h-4" />
          </button>
        </div>

        {/* Send button */}
        <button
          type="submit"
          disabled={!canSend}
          className={`
            p-2 rounded-lg transition-all duration-200 flex-shrink-0
            ${canSend
              ? 'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg'
              : currentTheme === 'dark'
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }
          `}
          title={canSend ? 'Send message' : 'Type a message to send'}
        >
          <FiSend className="w-4 h-4" />
        </button>
      </form>

      {/* Typing indicator */}
      {isTyping && (
        <div className={`mt-1 text-xs ${
          currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
        }`}>
          Typing...
        </div>
      )}

      {/* Help text */}
      <div className={`mt-1 text-xs ${
        currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-400'
      }`}>
        Press Enter to send, Shift+Enter for new line
      </div>
    </div>
  );
};

export default ChatInput;
