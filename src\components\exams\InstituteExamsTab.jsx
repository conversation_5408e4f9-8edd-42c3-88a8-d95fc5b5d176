import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  FiPlus,
  FiSearch,
  FiFilter,
  FiBookOpen,
  FiGrid,
  FiList,
  FiInfo,
  FiDownload,
  FiRefreshCw
} from 'react-icons/fi';
import {
  getInstituteReferenceExams,
  getComprehensiveInstituteExam,
  deleteExam
} from '../../store/slices/ExamSlice';
import { LoadingSpinner, ErrorMessage } from '../ui';
import ExamCard from './ExamCard';

const InstituteExamsTab = ({ instituteId, onExamSelect = null, selectionMode = false }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state - using institute reference exams
  const { instituteReferenceExams, loading, error } = useSelector((state) => state.exams);
  const exams = instituteReferenceExams;

  // Local state
  const [filteredExams, setFilteredExams] = useState([]);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    difficulty: 'all',
    status: 'all'
  });

  useEffect(() => {
    loadExams();
  }, [dispatch]);

  useEffect(() => {
    applyFilters();
  }, [exams, filters]);

  const loadExams = () => {
    dispatch(getInstituteReferenceExams());
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(getInstituteReferenceExams()).unwrap();
    } catch (error) {
      console.error('Failed to refresh reference exams:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const applyFilters = () => {
    const examsList = Array.isArray(exams) ? exams : [];
    let filtered = [...examsList];

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(exam =>
        (exam.title || '').toLowerCase().includes(searchTerm) ||
        (exam.description || '').toLowerCase().includes(searchTerm) ||
        (exam.created_by || '').toLowerCase().includes(searchTerm) ||
        exam.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    // Difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(exam => exam.difficulty_level === filters.difficulty);
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(exam => exam.status === filters.status);
    }

    setFilteredExams(filtered);
  };

  const handleCreateExam = () => {
    navigate('/institute/create-exam');
  };

  const handleEditExam = (exam) => {
    // Check for different possible ID fields
    const examId = exam.id || exam._id || exam.exam_id;
    // Pass the exam data through navigation state for pre-filling the form
    navigate(`/institute/exam/${examId}/edit`, {
      state: { examData: exam, isEditing: true }
    });
  };

  const handleViewExam = (exam) => {
    // Check for different possible ID fields
    const examId = exam.id || exam._id || exam.exam_id;
    // Pass the exam data through navigation state
    navigate(`/institute/exam/${examId}`, {
      state: { examData: exam }
    });
  };

  const handleDeleteExam = async (exam) => {
    if (!window.confirm(`Are you sure you want to delete "${exam.title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const examId = exam.id || exam._id || exam.exam_id;
      await dispatch(deleteExam(examId)).unwrap();
      // Refresh the institute exams list
      await loadExams();
    } catch (err) {
      console.error('Error deleting exam:', err);
    }
  };

  const handleCopyExam = async (exam) => {
    // Copy functionality not implemented yet
    console.log('Copy functionality will be available in a future update.');
  };

  const handleViewUsage = async (exam) => {
    // Usage info functionality not implemented yet
    console.log('Usage information will be available in a future update.');
  };



  const handleExamFormSuccess = () => {
    setShowExamForm(false);
    setEditingExam(null);
    loadExams();
  };

  const handleExamSelection = (exam) => {
    if (selectionMode && onExamSelect) {
      onExamSelect(exam);
    }
  };

  const exportExams = () => {
    if (!filteredExams || filteredExams.length === 0) {
      console.log('No exams to export');
      return;
    }

    try {
      const csvContent = [
        ['ID', 'Title', 'Description', 'Questions', 'Duration (min)', 'Difficulty', 'Status', 'Usage Count', 'Created Date'].join(','),
        ...filteredExams.map(exam => [
          exam.id || '',
          `"${exam.title || ''}"`,
          `"${exam.description || ''}"`,
          exam.questions_count || 0,
          exam.duration_minutes || 0,
          exam.difficulty_level || '',
          exam.status || '',
          exam.usage_count || 0,
          exam.created_at ? new Date(exam.created_at).toLocaleDateString() : ''
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `reference-exams-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting exams:', error);
      console.log('Failed to export exams');
    }
  };

  const StatCard = ({ icon: Icon, title, value, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-2 rounded-lg bg-${color}-100`}>
          <Icon className={`h-5 w-5 text-${color}-600`} />
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-lg font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <LoadingSpinner size="lg" />
        <p className="text-gray-600 mt-4">Loading institute exams...</p>
      </div>
    );
  }

  if (!instituteId) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Loading institute information...</p>
        <p className="text-xs text-gray-500 mt-2">Waiting for user authentication</p>
      </div>
    );
  }

  // Calculate statistics
  const examsList = Array.isArray(exams) ? exams : [];
  const totalExams = examsList.length;
  const activeExams = examsList.filter(e => e.status === 'active').length;
  const draftExams = examsList.filter(e => e.status === 'draft').length;
  const totalUsage = examsList.reduce((sum, e) => sum + (e.usage_count || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Reference Exams</h2>
          <p className="text-gray-600 mt-1">Create and manage reference exam templates for competitions. Students access these through competition registrations.</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Export Button */}
          <button
            onClick={exportExams}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <FiDownload className="h-4 w-4 mr-2" />
            Export
          </button>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>

          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
            >
              <FiGrid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
            >
              <FiList className="h-4 w-4" />
            </button>
          </div>

          <button
            onClick={handleCreateExam}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Exam
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard
          icon={FiBookOpen}
          title="Total Exams"
          value={totalExams}
          color="blue"
        />
        <StatCard
          icon={FiBookOpen}
          title="Active Exams"
          value={activeExams}
          color="green"
        />
        <StatCard
          icon={FiBookOpen}
          title="Draft Exams"
          value={draftExams}
          color="yellow"
        />
        <StatCard
          icon={FiBookOpen}
          title="Total Usage"
          value={totalUsage}
          color="purple"
        />
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage message={typeof error === 'string' ? error : 'Failed to load exams'} />
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                placeholder="Search exams..."
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <select
            value={filters.difficulty}
            onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Exams List */}
      {filteredExams.length > 0 ? (
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" 
          : "space-y-4"
        }>
          {filteredExams.map((exam) => (
            <div
              key={exam.id}
              onClick={() => handleExamSelection(exam)}
              className={selectionMode ? 'cursor-pointer' : ''}
            >
              <ExamCard
                exam={exam}
                onEdit={handleEditExam}
                onDelete={handleDeleteExam}
                onCopy={handleCopyExam}
                onView={handleViewExam}
                onViewUsage={handleViewUsage}
                variant={viewMode === 'list' ? 'compact' : 'default'}
                showActions={!selectionMode}
                userType="institute"
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiBookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {filters.search || filters.difficulty !== 'all' || filters.status !== 'all'
              ? 'No Exams Found'
              : 'No Exams Created Yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {filters.search || filters.difficulty !== 'all' || filters.status !== 'all'
              ? 'No exams match your current filters. Try adjusting your search criteria.'
              : 'Get started by creating your first exam. Build custom assessments for competitions and events.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={handleCreateExam}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiPlus className="h-5 w-5 mr-2" />
              {filters.search || filters.difficulty !== 'all' || filters.status !== 'all'
                ? 'Create New Exam'
                : 'Create Your First Exam'}
            </button>
            {(filters.search || filters.difficulty !== 'all' || filters.status !== 'all') && (
              <button
                onClick={() => setFilters({ search: '', difficulty: 'all', status: 'all' })}
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      )}

    </div>
  );
};

export default InstituteExamsTab;
