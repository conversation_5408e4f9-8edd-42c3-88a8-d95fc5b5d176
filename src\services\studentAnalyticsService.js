/**
 * Student Analytics Service
 * 
 * Handles all student analytics API endpoints including:
 * - Subject analytics
 * - Class/Grade analytics  
 * - Classroom analytics
 * - Competition analytics
 * - Comprehensive analytics
 */

import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';
import logger from '../utils/helpers/logger';
import { getAuthToken, getAuthHeader } from '../utils/helpers/authHelpers';

class StudentAnalyticsService {
  constructor() {
    this.baseUrl = API_BASE_URL;
    this.apiPrefix = '/api/student/analytics';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get cached data or fetch new data
   */
  getCachedData(key, fetchFunction) {
    const cached = this.cache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < this.cacheTimeout) {
      logger.info(`Returning cached data for key: ${key}`);
      return Promise.resolve(cached.data);
    }

    return fetchFunction().then(data => {
      this.cache.set(key, {
        data,
        timestamp: now
      });
      return data;
    });
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = getAuthToken();

    // Check if we have a valid token
    if (!token) {
      logger.warn('No authentication token found for analytics API call');
      throw new Error('Authentication required. Please log in again.');
    }

    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Clear cache for specific key or all cache
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Handle API errors consistently
   */
  handleApiError(error) {
    logger.error('Analytics API Error:', error);

    if (error.response) {
      const { status, data } = error.response;

      // Handle authentication errors specifically
      if (status === 401) {
        logger.error('Authentication failed for analytics API. Token may be expired or invalid.');
        // Clear potentially invalid token
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        throw new Error('Authentication failed. Please log in again.');
      }

      throw new Error(data?.message || `HTTP ${status}: ${error.message}`);
    } else if (error.request) {
      throw new Error('Network error: Unable to reach analytics service');
    } else {
      throw new Error(error.message || 'Unknown error occurred');
    }
  }

  /**
   * Build query parameters
   */
  buildQueryParams(params) {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(item => queryParams.append(key, item));
        } else {
          queryParams.append(key, value);
        }
      }
    });

    return queryParams.toString();
  }

  /**
   * Get Subject Analytics
   * GET /api/student/analytics/subject
   */
  async getSubjectAnalytics(params = {}) {
    const {
      start_date,
      end_date,
      period_type = 'monthly',
      subject_ids = null,
      include_trends = true,
      include_comparisons = true,
      include_recommendations = true,
      include_chapter_breakdown = true
    } = params;

    const queryParams = this.buildQueryParams({
      start_date,
      end_date,
      period_type,
      subject_ids,
      include_trends,
      include_comparisons,
      include_recommendations,
      include_chapter_breakdown
    });

    const cacheKey = `subject_analytics_${queryParams}`;

    return this.getCachedData(cacheKey, async () => {
      try {
        logger.info('Fetching subject analytics with params:', params);

        const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/subject?${queryParams}`, {
          headers: this.getAuthHeaders()
        });
        
        logger.info('Subject analytics fetched successfully');
        return response.data;

      } catch (error) {
        logger.error('Failed to fetch subject analytics:', error);

        if (error.response?.status === 404) {
          // Backend not implemented, return mock data
          return this.getMockSubjectAnalytics(params);
        }

        throw this.handleApiError(error);
      }
    });
  }

  /**
   * Get Class/Grade Analytics
   * GET /api/student/analytics/class-grade
   */
  async getClassGradeAnalytics(params = {}) {
    const {
      start_date,
      end_date,
      period_type = 'monthly',
      include_trends = true,
      include_comparisons = true,
      include_recommendations = true,
      include_peer_comparison = true,
      include_rank_history = true
    } = params;

    const queryParams = this.buildQueryParams({
      start_date,
      end_date,
      period_type,
      include_trends,
      include_comparisons,
      include_recommendations,
      include_peer_comparison,
      include_rank_history
    });

    const cacheKey = `class_grade_analytics_${queryParams}`;

    return this.getCachedData(cacheKey, async () => {
      try {
        logger.info('Fetching class/grade analytics with params:', params);

        const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/class-grade?${queryParams}`, {
          headers: this.getAuthHeaders()
        });
        
        logger.info('Class/grade analytics fetched successfully');
        return response.data;

      } catch (error) {
        logger.error('Failed to fetch class/grade analytics:', error);

        if (error.response?.status === 404) {
          // Backend not implemented, return mock data
          return this.getMockClassGradeAnalytics(params);
        }

        throw this.handleApiError(error);
      }
    });
  }

  /**
   * Get Classroom Analytics
   * GET /api/student/analytics/classroom
   */
  async getClassroomAnalytics(params = {}) {
    const {
      start_date,
      end_date,
      period_type = 'monthly',
      classroom_ids = null,
      include_trends = true,
      include_comparisons = true,
      include_recommendations = true,
      include_engagement_details = true
    } = params;

    const queryParams = this.buildQueryParams({
      start_date,
      end_date,
      period_type,
      classroom_ids,
      include_trends,
      include_comparisons,
      include_recommendations,
      include_engagement_details
    });

    const cacheKey = `classroom_analytics_${queryParams}`;

    return this.getCachedData(cacheKey, async () => {
      try {
        logger.info('Fetching classroom analytics with params:', params);

        const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/classroom?${queryParams}`, {
          headers: this.getAuthHeaders()
        });
        
        logger.info('Classroom analytics fetched successfully');
        return response.data;

      } catch (error) {
        logger.error('Failed to fetch classroom analytics:', error);

        if (error.response?.status === 404) {
          // Backend not implemented, return mock data
          return this.getMockClassroomAnalytics(params);
        }

        throw this.handleApiError(error);
      }
    });
  }

  /**
   * Get Competition Analytics
   * GET /api/student/analytics/competition
   */
  async getCompetitionAnalytics(params = {}) {
    const {
      start_date,
      end_date,
      period_type = 'monthly',
      competition_types = null,
      include_trends = true,
      include_comparisons = true,
      include_recommendations = true,
      include_upcoming = true
    } = params;

    const queryParams = this.buildQueryParams({
      start_date,
      end_date,
      period_type,
      competition_types,
      include_trends,
      include_comparisons,
      include_recommendations,
      include_upcoming
    });

    const cacheKey = `competition_analytics_${queryParams}`;

    return this.getCachedData(cacheKey, async () => {
      try {
        logger.info('Fetching competition analytics with params:', params);

        const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/competition?${queryParams}`, {
          headers: this.getAuthHeaders()
        });
        
        logger.info('Competition analytics fetched successfully');
        return response.data;

      } catch (error) {
        logger.error('Failed to fetch competition analytics:', error);

        if (error.response?.status === 404) {
          // Backend not implemented, return mock data
          return this.getMockCompetitionAnalytics(params);
        }

        throw this.handleApiError(error);
      }
    });
  }

  /**
   * Get Comprehensive Analytics
   * GET /api/student/analytics/comprehensive
   */
  async getComprehensiveAnalytics(params = {}) {
    const {
      start_date,
      end_date,
      period_type = 'monthly',
      include_trends = true,
      include_comparisons = true,
      include_recommendations = true
    } = params;

    const queryParams = this.buildQueryParams({
      start_date,
      end_date,
      period_type,
      include_trends,
      include_comparisons,
      include_recommendations
    });

    const cacheKey = `comprehensive_analytics_${queryParams}`;

    return this.getCachedData(cacheKey, async () => {
      try {
        logger.info('Fetching comprehensive analytics with params:', params);

        const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/comprehensive?${queryParams}`, {
          headers: this.getAuthHeaders()
        });
        
        logger.info('Comprehensive analytics fetched successfully');
        return response.data;

      } catch (error) {
        logger.error('Failed to fetch comprehensive analytics:', error);

        if (error.response?.status === 404) {
          // Backend not implemented, return mock data
          return this.getMockComprehensiveAnalytics(params);
        }

        throw this.handleApiError(error);
      }
    });
  }

  /**
   * Health Check
   * GET /api/student/analytics/health
   */
  async healthCheck() {
    try {
      const response = await axios.get(`${this.baseUrl}${this.apiPrefix}/health`, {
        headers: this.getAuthHeaders()
      });
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  // Mock data methods for development/testing
  getMockSubjectAnalytics(params) {
    const now = new Date();
    const startDate = params.start_date || new Date(now.getFullYear(), now.getMonth() - 3, 1);
    const endDate = params.end_date || now;

    return {
      student_id: "mock-student-id",
      time_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        period_type: params.period_type || "monthly"
      },
      subjects: [
        {
          subject_id: "math-101",
          subject_name: "Mathematics",
          total_exams: 8,
          total_assignments: 15,
          average_score: 87.5,
          highest_score: 95,
          lowest_score: 78,
          median_score: 88,
          total_marks_obtained: 1400,
          total_marks_possible: 1600,
          success_rate: 87.5,
          class_average: 82.3,
          performance_vs_class: 5.2,
          ranking: {
            current_rank: 3,
            total_students: 28,
            percentile: 89.3,
            rank_change: 1,
            rank_trend: "improving"
          },
          performance_trend: [
            { period: "Jan 2025", value: 85, change_percentage: 0, trend_direction: "stable" },
            { period: "Feb 2025", value: 87, change_percentage: 2.4, trend_direction: "up" },
            { period: "Mar 2025", value: 89, change_percentage: 2.3, trend_direction: "up" }
          ],
          improvement_rate: 4.7,
          chapter_performance: {
            "Algebra": 92,
            "Geometry": 85,
            "Calculus": 88,
            "Statistics": 86
          },
          strongest_chapters: ["Algebra", "Calculus"],
          weakest_chapters: ["Geometry"],
          study_time_hours: 45,
          assignment_completion_rate: 93.3,
          on_time_submission_rate: 86.7,
          improvement_areas: ["Geometry concepts", "Time management"],
          recommended_actions: ["Practice more geometry problems", "Create study schedule"]
        },
        {
          subject_id: "science-101",
          subject_name: "Science",
          total_exams: 6,
          total_assignments: 12,
          average_score: 91.2,
          highest_score: 98,
          lowest_score: 84,
          median_score: 92,
          total_marks_obtained: 1095,
          total_marks_possible: 1200,
          success_rate: 91.2,
          class_average: 85.7,
          performance_vs_class: 5.5,
          ranking: {
            current_rank: 2,
            total_students: 28,
            percentile: 92.9,
            rank_change: 0,
            rank_trend: "stable"
          },
          performance_trend: [
            { period: "Jan 2025", value: 89, change_percentage: 0, trend_direction: "stable" },
            { period: "Feb 2025", value: 91, change_percentage: 2.2, trend_direction: "up" },
            { period: "Mar 2025", value: 93, change_percentage: 2.2, trend_direction: "up" }
          ],
          improvement_rate: 4.5,
          chapter_performance: {
            "Physics": 94,
            "Chemistry": 89,
            "Biology": 91
          },
          strongest_chapters: ["Physics", "Biology"],
          weakest_chapters: ["Chemistry"],
          study_time_hours: 38,
          assignment_completion_rate: 100,
          on_time_submission_rate: 91.7,
          improvement_areas: ["Chemistry equations"],
          recommended_actions: ["Review chemical formulas", "Practice lab experiments"]
        }
      ],
      overall_gpa: 3.8,
      strongest_subject: "Science",
      weakest_subject: "Mathematics",
      total_study_hours: 83,
      last_updated: now.toISOString()
    };
  }

  getMockClassGradeAnalytics(params) {
    const now = new Date();
    const startDate = params.start_date || new Date(now.getFullYear(), now.getMonth() - 3, 1);
    const endDate = params.end_date || now;

    return {
      student_id: "mock-student-id",
      time_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        period_type: params.period_type || "monthly"
      },
      class_performance: [
        {
          class_id: "class-10a",
          class_name: "Class 10-A",
          grade_level: "10th Grade",
          student_rank: 5,
          total_students: 32,
          percentile: 84.4,
          student_average: 88.5,
          class_average: 82.1,
          top_performer_score: 95.2,
          performance_gap: 6.7,
          subject_rankings: {
            "Mathematics": {
              current_rank: 3,
              total_students: 32,
              percentile: 90.6,
              rank_change: 1,
              rank_trend: "improving"
            },
            "Science": {
              current_rank: 2,
              total_students: 32,
              percentile: 93.8,
              rank_change: 0,
              rank_trend: "stable"
            }
          },
          students_above: 4,
          students_below: 27,
          similar_performers: [
            { student_name: "John D.", average: 87.8 },
            { student_name: "Sarah M.", average: 89.2 }
          ],
          rank_history: [
            { period: "Jan", rank: 6 },
            { period: "Feb", rank: 5 },
            { period: "Mar", rank: 5 }
          ],
          rank_trend: "improving"
        }
      ],
      grade_analytics: {
        grade_level: "10th Grade",
        total_students_in_grade: 128,
        student_rank_in_grade: 18,
        grade_percentile: 85.9,
        student_gpa: 3.8,
        grade_average_gpa: 3.4,
        top_gpa_in_grade: 4.0,
        grade_subject_averages: {
          "Mathematics": 82.3,
          "Science": 85.7,
          "English": 79.8,
          "History": 81.2
        },
        student_vs_grade_subjects: {
          "Mathematics": 5.2,
          "Science": 5.5,
          "English": 8.7,
          "History": 4.3
        },
        top_performers: [
          { student_name: "Alex K.", gpa: 4.0 },
          { student_name: "Emma L.", gpa: 3.95 }
        ],
        achievement_level: "High Achiever"
      },
      overall_academic_standing: "Above Average",
      improvement_recommendations: [
        "Focus on maintaining current performance",
        "Consider advanced placement courses",
        "Participate in academic competitions"
      ],
      last_updated: now.toISOString()
    };
  }

  getMockClassroomAnalytics(params) {
    const now = new Date();
    const startDate = params.start_date || new Date(now.getFullYear(), now.getMonth() - 3, 1);
    const endDate = params.end_date || now;

    return {
      student_id: "mock-student-id",
      time_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        period_type: params.period_type || "monthly"
      },
      classrooms: [
        {
          classroom_id: "math-classroom-1",
          classroom_name: "Advanced Mathematics",
          teacher_name: "Dr. Smith",
          subject: "Mathematics",
          attendance_rate: 94.5,
          participation_score: 87.3,
          assignment_submission_rate: 93.3,
          on_time_submission_rate: 86.7,
          classroom_average: 82.3,
          student_average: 87.5,
          performance_vs_classroom: 5.2,
          classroom_rank: 3,
          total_classroom_students: 28,
          questions_asked: 24,
          discussions_participated: 18,
          peer_interactions: 45,
          help_requests: 8,
          assignments_completed: 14,
          assignments_total: 15,
          average_assignment_score: 88.2,
          best_assignment_score: 95,
          time_spent_in_classroom: 120,
          active_learning_time: 95,
          positive_feedback_count: 12,
          improvement_suggestions: [
            "Participate more in class discussions",
            "Ask questions when concepts are unclear"
          ]
        },
        {
          classroom_id: "science-classroom-1",
          classroom_name: "General Science",
          teacher_name: "Prof. Johnson",
          subject: "Science",
          attendance_rate: 96.8,
          participation_score: 91.7,
          assignment_submission_rate: 100,
          on_time_submission_rate: 91.7,
          classroom_average: 85.7,
          student_average: 91.2,
          performance_vs_classroom: 5.5,
          classroom_rank: 2,
          total_classroom_students: 28,
          questions_asked: 31,
          discussions_participated: 22,
          peer_interactions: 52,
          help_requests: 5,
          assignments_completed: 12,
          assignments_total: 12,
          average_assignment_score: 92.1,
          best_assignment_score: 98,
          time_spent_in_classroom: 110,
          active_learning_time: 98,
          positive_feedback_count: 15,
          improvement_suggestions: [
            "Continue excellent performance",
            "Consider helping struggling classmates"
          ]
        }
      ],
      most_engaged_classroom: "General Science",
      least_engaged_classroom: "Advanced Mathematics",
      overall_engagement_score: 89.5,
      engagement_trend: "improving",
      recommendations: [
        "Maintain high engagement in Science class",
        "Increase participation in Mathematics discussions",
        "Continue asking thoughtful questions"
      ],
      last_updated: now.toISOString()
    };
  }

  getMockCompetitionAnalytics(params) {
    const now = new Date();
    const startDate = params.start_date || new Date(now.getFullYear(), now.getMonth() - 6, 1);
    const endDate = params.end_date || now;

    return {
      student_id: "mock-student-id",
      time_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        period_type: params.period_type || "monthly"
      },
      competitions: [
        {
          competition_id: "math-olympiad-2025",
          competition_name: "Mathematics Olympiad 2025",
          competition_type: "Academic",
          event_date: new Date(2025, 2, 15).toISOString(),
          registration_date: new Date(2025, 1, 1).toISOString(),
          participation_status: "completed",
          score_obtained: 87,
          total_possible_score: 100,
          percentage_score: 87,
          rank: 12,
          total_participants: 150,
          percentile: 92,
          category_scores: {
            "Algebra": 92,
            "Geometry": 85,
            "Number Theory": 84
          },
          strongest_categories: ["Algebra"],
          weakest_categories: ["Number Theory"],
          time_taken: 180,
          time_efficiency: 90,
          awards_received: ["Bronze Medal"],
          certificates_earned: ["Participation Certificate"],
          areas_for_improvement: ["Number theory concepts", "Time management"]
        },
        {
          competition_id: "science-fair-2025",
          competition_name: "Regional Science Fair 2025",
          competition_type: "Science",
          event_date: new Date(2025, 1, 20).toISOString(),
          registration_date: new Date(2025, 0, 10).toISOString(),
          participation_status: "completed",
          score_obtained: 94,
          total_possible_score: 100,
          percentage_score: 94,
          rank: 3,
          total_participants: 85,
          percentile: 96.5,
          category_scores: {
            "Research": 96,
            "Presentation": 92,
            "Innovation": 94
          },
          strongest_categories: ["Research", "Innovation"],
          weakest_categories: ["Presentation"],
          time_taken: 240,
          time_efficiency: 95,
          awards_received: ["Silver Medal", "Best Research Award"],
          certificates_earned: ["Excellence Certificate"],
          areas_for_improvement: ["Presentation skills"]
        }
      ],
      summary: {
        total_competitions_participated: 2,
        total_competitions_completed: 2,
        average_score: 90.5,
        best_performance_score: 94,
        worst_performance_score: 87,
        average_rank: 7.5,
        best_rank: 3,
        top_10_finishes: 2,
        top_25_percent_finishes: 2,
        total_awards: 3,
        total_certificates: 2,
        achievement_rate: 100,
        performance_trend: [
          { period: "Jan 2025", value: 87, change_percentage: 0, trend_direction: "stable" },
          { period: "Feb 2025", value: 94, change_percentage: 8.0, trend_direction: "up" }
        ],
        improvement_rate: 8.0,
        strongest_competition_categories: ["Research", "Algebra"],
        preferred_competition_types: ["Academic", "Science"]
      },
      upcoming_competitions: [
        {
          competition_name: "Physics Challenge 2025",
          event_date: new Date(2025, 3, 10).toISOString(),
          registration_deadline: new Date(2025, 2, 25).toISOString()
        }
      ],
      recommended_competitions: [
        {
          competition_name: "Chemistry Bowl 2025",
          match_score: 85,
          reason: "Strong science performance"
        }
      ],
      last_updated: now.toISOString()
    };
  }

  getMockComprehensiveAnalytics(params) {
    const now = new Date();
    const startDate = params.start_date || new Date(now.getFullYear(), now.getMonth() - 3, 1);
    const endDate = params.end_date || now;

    // Combine all analytics types
    const subjectAnalytics = this.getMockSubjectAnalytics(params);
    const classGradeAnalytics = this.getMockClassGradeAnalytics(params);
    const classroomAnalytics = this.getMockClassroomAnalytics(params);
    const competitionAnalytics = this.getMockCompetitionAnalytics(params);

    return {
      student_id: "mock-student-id",
      student_name: "John Doe",
      time_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        period_type: params.period_type || "monthly"
      },
      subject_analytics: subjectAnalytics,
      class_grade_analytics: classGradeAnalytics,
      classroom_analytics: classroomAnalytics,
      competition_analytics: competitionAnalytics,
      overall_performance_score: 88.7,
      academic_strength_areas: [
        "Science Research",
        "Mathematical Problem Solving",
        "Analytical Thinking",
        "Academic Competitions"
      ],
      improvement_opportunities: [
        "Geometry concepts in Mathematics",
        "Chemistry equations and formulas",
        "Class participation and discussions",
        "Time management skills"
      ],
      personalized_recommendations: [
        "Continue excelling in Science - consider advanced courses",
        "Practice more geometry problems to strengthen Math skills",
        "Participate more actively in classroom discussions",
        "Join academic clubs and competitions",
        "Develop a structured study schedule"
      ],
      current_goals: [
        {
          goal_id: "improve-math-rank",
          title: "Improve Mathematics Ranking",
          target: "Top 3 in class",
          current_progress: 60,
          deadline: new Date(2025, 5, 30).toISOString()
        },
        {
          goal_id: "science-competition",
          title: "Win Science Competition",
          target: "Gold Medal in Regional Fair",
          current_progress: 80,
          deadline: new Date(2025, 4, 15).toISOString()
        }
      ],
      achievement_progress: {
        "Academic Excellence": 85,
        "Class Participation": 70,
        "Competition Performance": 90,
        "Study Consistency": 75
      },
      last_updated: now.toISOString()
    };
  }
}

// Create and export a singleton instance
const studentAnalyticsService = new StudentAnalyticsService();
export default studentAnalyticsService;
