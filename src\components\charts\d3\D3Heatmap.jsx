import React, { useCallback } from 'react';
import * as d3 from 'd3';
import BaseD3<PERSON>hart from './BaseD3Chart';

/**
 * D3 Heatmap Component
 * Interactive heatmap for matrix data visualization
 */
const D3Heatmap = ({
  data = [],
  width = 600,
  height = 400,
  margin = { top: 50, right: 50, bottom: 50, left: 100 },
  xKey = 'x',
  yKey = 'y',
  valueKey = 'value',
  colorScheme = 'Blues',
  showValues = true,
  animate = true,
  xAxisLabel = '',
  yAxisLabel = '',
  onCellHover,
  onCellClick,
  className = '',
  ...props
}) => {
  const renderChart = useCallback(({
    svg,
    g,
    data,
    colors,
    createTooltip,
    innerWidth,
    innerHeight
  }) => {
    // Prepare data
    const chartData = data.map(d => ({
      x: d[xKey],
      y: d[yKey],
      value: d[valueKey],
      original: d
    }));

    // Get unique x and y values
    const xValues = [...new Set(chartData.map(d => d.x))].sort();
    const yValues = [...new Set(chartData.map(d => d.y))].sort();

    // Create scales
    const xScale = d3.scaleBand()
      .domain(xValues)
      .range([0, innerWidth])
      .padding(0.05);

    const yScale = d3.scaleBand()
      .domain(yValues)
      .range([0, innerHeight])
      .padding(0.05);

    // Color scale
    const colorScale = d3.scaleSequential()
      .interpolator(d3[`interpolate${colorScheme}`] || d3.interpolateBlues)
      .domain(d3.extent(chartData, d => d.value));

    // Create tooltip
    const tooltip = createTooltip();

    // Add axes labels
    if (xAxisLabel) {
      g.append('text')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + 40)
        .style('text-anchor', 'middle')
        .style('fill', colors.text)
        .style('font-size', '14px')
        .style('font-weight', 'bold')
        .text(xAxisLabel);
    }

    if (yAxisLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('y', -60)
        .attr('x', -innerHeight / 2)
        .style('text-anchor', 'middle')
        .style('fill', colors.text)
        .style('font-size', '14px')
        .style('font-weight', 'bold')
        .text(yAxisLabel);
    }

    // Add x-axis
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '12px')
      .attr('transform', 'rotate(-45)')
      .style('text-anchor', 'end');

    // Add y-axis
    g.append('g')
      .attr('class', 'y-axis')
      .call(d3.axisLeft(yScale))
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '12px');

    // Style axes
    g.selectAll('.x-axis line, .x-axis path, .y-axis line, .y-axis path')
      .style('stroke', colors.border);

    // Create cells
    const cells = g.selectAll('.cell')
      .data(chartData)
      .enter()
      .append('rect')
      .attr('class', 'cell')
      .attr('x', d => xScale(d.x))
      .attr('y', d => yScale(d.y))
      .attr('width', xScale.bandwidth())
      .attr('height', yScale.bandwidth())
      .style('fill', d => colorScale(d.value))
      .style('stroke', colors.background)
      .style('stroke-width', 1)
      .style('cursor', 'pointer');

    // Animation
    if (animate) {
      cells
        .style('opacity', 0)
        .transition()
        .delay((d, i) => i * 10)
        .duration(500)
        .style('opacity', 1);
    }

    // Add hover effects
    cells
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('stroke', colors.text)
          .style('stroke-width', 2);

        // Highlight row and column
        g.selectAll('.cell')
          .filter(cell => cell.x === d.x || cell.y === d.y)
          .transition()
          .duration(200)
          .style('opacity', 0.8);

        tooltip
          .style('visibility', 'visible')
          .html(`
            <div>
              <strong>${xAxisLabel || 'X'}: ${d.x}</strong><br/>
              <strong>${yAxisLabel || 'Y'}: ${d.y}</strong><br/>
              <strong>Value: ${d.value}</strong>
            </div>
          `);

        if (onCellHover) {
          onCellHover(d.original, event);
        }
      })
      .on('mousemove', function(event) {
        tooltip
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this)
          .transition()
          .duration(200)
          .style('stroke', colors.background)
          .style('stroke-width', 1);

        // Reset all cells
        g.selectAll('.cell')
          .transition()
          .duration(200)
          .style('opacity', 1);

        tooltip.style('visibility', 'hidden');
      })
      .on('click', function(event, d) {
        if (onCellClick) {
          onCellClick(d.original, event);
        }
      });

    // Add value labels if enabled
    if (showValues) {
      const labels = g.selectAll('.cell-label')
        .data(chartData)
        .enter()
        .append('text')
        .attr('class', 'cell-label')
        .attr('x', d => xScale(d.x) + xScale.bandwidth() / 2)
        .attr('y', d => yScale(d.y) + yScale.bandwidth() / 2)
        .attr('dy', '0.35em')
        .style('text-anchor', 'middle')
        .style('fill', d => {
          // Use contrasting color based on cell brightness
          const rgb = d3.rgb(colorScale(d.value));
          const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
          return brightness > 128 ? '#000000' : '#ffffff';
        })
        .style('font-size', '11px')
        .style('font-weight', 'bold')
        .style('pointer-events', 'none')
        .text(d => d.value);

      if (animate) {
        labels
          .style('opacity', 0)
          .transition()
          .delay((d, i) => i * 10 + 300)
          .duration(300)
          .style('opacity', 1);
      }
    }

    // Add color legend
    const legendWidth = 20;
    const legendHeight = innerHeight * 0.6;
    const legendX = innerWidth + 20;
    const legendY = innerHeight * 0.2;

    // Create gradient for legend
    const defs = svg.append('defs');
    const gradient = defs.append('linearGradient')
      .attr('id', 'heatmap-gradient')
      .attr('x1', '0%')
      .attr('y1', '100%')
      .attr('x2', '0%')
      .attr('y2', '0%');

    const colorStops = d3.range(0, 1.1, 0.1);
    gradient.selectAll('stop')
      .data(colorStops)
      .enter()
      .append('stop')
      .attr('offset', d => `${d * 100}%`)
      .attr('stop-color', d => colorScale(colorScale.domain()[0] + d * (colorScale.domain()[1] - colorScale.domain()[0])));

    // Add legend rectangle
    g.append('rect')
      .attr('x', legendX)
      .attr('y', legendY)
      .attr('width', legendWidth)
      .attr('height', legendHeight)
      .style('fill', 'url(#heatmap-gradient)')
      .style('stroke', colors.border);

    // Add legend scale
    const legendScale = d3.scaleLinear()
      .domain(colorScale.domain())
      .range([legendY + legendHeight, legendY]);

    const legendAxis = d3.axisRight(legendScale)
      .ticks(5)
      .tickFormat(d3.format('.1f'));

    g.append('g')
      .attr('class', 'legend-axis')
      .attr('transform', `translate(${legendX + legendWidth}, 0)`)
      .call(legendAxis)
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '10px');

    g.select('.legend-axis')
      .selectAll('line, path')
      .style('stroke', colors.border);

  }, [
    xKey, yKey, valueKey, colorScheme, showValues, animate,
    xAxisLabel, yAxisLabel, onCellHover, onCellClick
  ]);

  return (
    <BaseD3Chart
      data={data}
      width={width}
      height={height}
      margin={margin}
      onRender={renderChart}
      className={`d3-heatmap ${className}`}
      {...props}
    />
  );
};

export default D3Heatmap;
