import React from 'react';
import SimpleSpeakerSelector from '../SimpleSpeakerSelector';
import SimpleMentorSelector from '../SimpleMentorSelector';
import { EVENT_CATEGORIES } from '../../../constants/eventCategories';

const PeopleTab = ({
  formData,
  selectedSpeakers,
  selectedMentors,
  onSpeakersChange,
  onMentorsChange
}) => {
  console.log('🔍 PeopleTab render:', {
    category: formData.category,
    isCompetition: formData.category === EVENT_CATEGORIES.COMPETITION,
    selectedSpeakers,
    selectedMentors,
    onSpeakersChange: typeof onSpeakersChange,
    onMentorsChange: typeof onMentorsChange
  });

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">
        {formData.category === EVENT_CATEGORIES.COMPETITION ? 'Competition Mentors' : 'Event Speakers'}
      </h2>

      {formData.category === EVENT_CATEGORIES.COMPETITION ? (
        <SimpleMentorSelector
          selectedMentors={selectedMentors}
          onMentorsChange={onMentorsChange}
        />
      ) : (
        <SimpleSpeakerSelector
          selectedSpeakers={selectedSpeakers}
          onSpeakersChange={onSpeakersChange}
        />
      )}
    </div>
  );
};

export default PeopleTab;
