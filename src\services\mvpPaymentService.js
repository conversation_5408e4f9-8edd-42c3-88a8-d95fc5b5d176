/**
 * Demo Payment Service
 *
 * Simple demo payment service for testing purposes:
 * - Simulates ticket purchases without real payment processing
 * - Instantly confirms ticket purchases for demo purposes
 * - No PayFast or real payment integration
 *
 * Frontend never decides prices, only passes ticket ID.
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { getAuthToken } from '../utils/helpers/authHelpers';
import logger from '../utils/helpers/logger';

class DemoPaymentService {
  constructor() {
    this.baseUrl = BASE_API;
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Purchase ticket instantly for demo purposes
   * No real payment processing - instantly confirms purchase
   *
   * @param {string} ticketId - The ticket ID to purchase
   * @returns {Promise<Object>} Instant purchase confirmation
   */
  async purchaseTicket(ticketId) {
    try {
      logger.info('Demo purchase for ticket:', ticketId);

      // Simulate a small delay for realistic UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try to call backend ticket purchase endpoint (without payment)
      try {
        const response = await axios.post(
          `${this.baseUrl}/api/tickets/purchase-demo`,
          { ticket_id: ticketId },
          { headers: this.getAuthHeaders() }
        );

        logger.info('Demo ticket purchased successfully:', response.data);
        return response.data;
      } catch (backendError) {
        // If backend endpoint doesn't exist, use demo confirmation
        logger.warn('Backend demo purchase endpoint not found, using local demo confirmation');
        return this.createDemoConfirmation(ticketId);
      }

    } catch (error) {
      logger.error('Failed to purchase ticket:', error);

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 400:
            throw new Error(data.message || 'Invalid ticket ID');
          case 401:
            throw new Error('Please log in to purchase tickets');
          case 403:
            throw new Error('You are not authorized to purchase this ticket');
          case 422:
            throw new Error(data.message || 'Validation error');
          default:
            throw new Error(data.message || 'Failed to purchase ticket');
        }
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }



  /**
   * Create demo purchase confirmation
   * @param {string} ticketId - The ticket ID
   * @returns {Object} Demo purchase confirmation
   */
  createDemoConfirmation(ticketId) {
    logger.info('Creating demo purchase confirmation for ticket:', ticketId);

    // Simulate different ticket types for testing
    const mockTickets = {
      'ticket-free': { price: 0, name: 'Free Entry' },
      'ticket-basic': { price: 50, name: 'Basic Ticket' },
      'ticket-premium': { price: 150, name: 'Premium Ticket' },
      'ticket-1': { price: 100, name: 'General Admission' },
      'ticket-2': { price: 250, name: 'VIP Pass' },
      'ticket-3': { price: 0, name: 'Free Entry' }
    };

    const ticket = mockTickets[ticketId] || { price: 99, name: 'Demo Ticket' };

    // All tickets are instantly confirmed for demo
    return {
      success: true,
      payment_required: false, // No payment required for demo
      ticket_id: ticketId,
      ticket_name: ticket.name,
      amount: ticket.price,
      currency: 'ZAR',
      status: 'confirmed',
      purchase_id: `demo-purchase-${Date.now()}`,
      confirmation_code: `DEMO-${ticketId.toUpperCase()}-${Math.random().toString(36).substr(2, 6)}`,
      message: `Demo ticket "${ticket.name}" purchased successfully! No payment required.`,
      demo_mode: true
    };
  }

  /**
   * Check ticket purchase status
   * Useful for users who return later or close browser during payment
   *
   * @param {string} ticketId - The ticket ID to check status for
   * @returns {Promise<Object>} Ticket status information
   */
  async checkTicketStatus(ticketId) {
    try {
      logger.info('Checking ticket status:', ticketId);

      const response = await axios.get(
        `${this.baseUrl}/api/tickets/${ticketId}/status`,
        { headers: this.getAuthHeaders() }
      );

      logger.info('Ticket status retrieved:', response.data);
      return response.data;

    } catch (error) {
      logger.error('Failed to check ticket status:', error);

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case 401:
            throw new Error('Please log in to check ticket status');
          case 403:
            throw new Error('You are not authorized to view this ticket');
          case 404:
            // If 404, try mock implementation
            logger.warn('Ticket status endpoint not found, using mock implementation');
            return this.createMockTicketStatus(ticketId);
          default:
            throw new Error(data.message || 'Failed to check ticket status');
        }
      } else if (error.request) {
        // If network error, try mock implementation
        logger.warn('Network error, using mock ticket status');
        return this.createMockTicketStatus(ticketId);
      } else {
        throw new Error('An unexpected error occurred');
      }
    }
  }

  /**
   * Create mock ticket status for testing
   * @param {string} ticketId - The ticket ID
   * @returns {Object} Mock ticket status
   */
  createMockTicketStatus(ticketId) {
    logger.warn('Using mock ticket status - backend endpoints not available');

    // Simulate different statuses for testing
    const mockStatuses = ['confirmed', 'pending', 'failed'];
    const randomStatus = mockStatuses[Math.floor(Math.random() * mockStatuses.length)];

    return {
      ticket_id: ticketId,
      status: randomStatus,
      ticket: {
        id: ticketId,
        name: 'Mock Ticket',
        price: 99,
        currency: 'ZAR'
      },
      event: {
        id: 'mock-event-123',
        title: 'Mock Event for Testing',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        location: 'Mock Venue, Test City'
      },
      payment: randomStatus === 'confirmed' ? {
        payment_id: 'mock-payment-' + Date.now(),
        amount: 99,
        currency: 'ZAR',
        date: new Date().toISOString()
      } : null,
      message: `Mock ticket status: ${randomStatus}`
    };
  }

  /**
   * Generate demo purchase URLs
   *
   * @param {string} ticketId - Ticket ID for reference
   * @returns {Object} Demo success and cancel URLs
   */
  generateDemoUrls(ticketId) {
    const baseUrl = window.location.origin;

    return {
      success_url: `${baseUrl}/payment/demo-success?ticket_id=${ticketId}`,
      cancel_url: `${baseUrl}/events` // Just go back to events
    };
  }

  /**
   * Parse URL parameters from demo purchase
   *
   * @param {string} url - Current URL with parameters
   * @returns {Object} Parsed parameters
   */
  parseDemoParams(url = window.location.href) {
    try {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);

      return {
        ticket_id: params.get('ticket_id'),
        purchase_id: params.get('purchase_id'),
        confirmation_code: params.get('confirmation_code'),
        demo_mode: params.get('demo_mode') === 'true'
      };
    } catch (error) {
      logger.error('Failed to parse demo parameters:', error);
      return {};
    }
  }

  /**
   * Validate if user is authenticated
   * 
   * @returns {boolean} True if user is logged in
   */
  isAuthenticated() {
    const token = getAuthToken();
    return !!token;
  }

  /**
   * Get user info for payment context
   * 
   * @returns {Object|null} User information or null if not logged in
   */
  getUserInfo() {
    try {
      const userdata = localStorage.getItem('userdata');
      return userdata ? JSON.parse(userdata) : null;
    } catch (error) {
      logger.error('Failed to get user info:', error);
      return null;
    }
  }

  /**
   * Log demo purchase event for analytics/debugging
   *
   * @param {string} ticketId - Ticket ID
   * @param {string} action - Action taken (purchase, success, cancel, error)
   * @param {Object} data - Additional data to log
   */
  logDemoEvent(ticketId, action, data = {}) {
    try {
      const user = this.getUserInfo();
      const logData = {
        ticket_id: ticketId,
        action,
        user_id: user?.id || 'anonymous',
        timestamp: new Date().toISOString(),
        demo_mode: true,
        ...data
      };

      logger.info('Demo purchase event:', logData);

      // Could send to analytics service here
      // analytics.track('demo_purchase_event', logData);

    } catch (error) {
      logger.error('Failed to log demo event:', error);
    }
  }
}

// Export singleton instance
const demoPaymentService = new DemoPaymentService();
export default demoPaymentService;

// Export class for testing
export { DemoPaymentService };
