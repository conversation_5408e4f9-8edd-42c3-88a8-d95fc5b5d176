import React from 'react';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

const TicketsTab = ({ 
  tickets, 
  formData,
  onTicketsChange,
  onAddTicket,
  onRemoveTicket 
}) => {
  // Handle ticket changes
  const handleTicketChange = (index, field, value) => {
    const newTickets = [...tickets];

    // Validate numeric fields
    if (field === 'price') {
      // Ensure price is a valid number >= 0
      const numValue = parseFloat(value);
      if (isNaN(numValue) || numValue < 0) {
        return; // Don't update if invalid
      }
      newTickets[index] = { ...newTickets[index], [field]: numValue };
    } else if (field === 'total_quantity' || field === 'min_quantity_per_order' || field === 'max_quantity_per_order') {
      // Ensure quantity fields are valid integers >= 1
      const numValue = parseInt(value);
      if (isNaN(numValue) || numValue < 1) {
        return; // Don't update if invalid
      }
      newTickets[index] = { ...newTickets[index], [field]: numValue };
    } else {
      newTickets[index] = { ...newTickets[index], [field]: value };
    }

    onTicketsChange(newTickets);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Ticket Types</h3>
          <p className="text-sm text-gray-600 mt-1">Configure different ticket types for your event</p>
        </div>
        <button
          type="button"
          onClick={onAddTicket}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <FiPlus className="h-4 w-4 mr-1" />
          Add Ticket Type
        </button>
      </div>

      <div className="space-y-6">
        {tickets.map((ticket, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-6 bg-gray-50">
            <div className="flex justify-between items-start mb-4">
              <h4 className="text-md font-medium text-gray-900">
                Ticket Type {index + 1}
              </h4>
              {tickets.length > 1 && (
                <button
                  type="button"
                  onClick={() => onRemoveTicket(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FiTrash2 className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ticket Name *
                </label>
                <input
                  type="text"
                  value={ticket.name}
                  onChange={(e) => handleTicketChange(index, 'name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., General Admission"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={ticket.price}
                    onChange={(e) => handleTicketChange(index, 'price', e.target.value)}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Available Quantity *
                </label>
                <input
                  type="number"
                  value={ticket.total_quantity}
                  onChange={(e) => handleTicketChange(index, 'total_quantity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="1"
                  placeholder="1"
                />
              </div>
            </div>

            {/* Description */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={ticket.description}
                onChange={(e) => handleTicketChange(index, 'description', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe what this ticket includes"
              />
            </div>

            {/* Sale Period - Uses Registration Dates */}
            <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3">
              <h5 className="text-sm font-medium text-blue-800 mb-2">Sale Period</h5>
              <p className="text-sm text-blue-700">
                This ticket will be available for purchase during the registration period:<br />
                <strong>Registration Start:</strong> {formData?.registration_start ? new Date(formData.registration_start).toLocaleString() : 'Not set'}<br />
                <strong>Registration End:</strong> {formData?.registration_end ? new Date(formData.registration_end).toLocaleString() : 'Not set'}
              </p>
            </div>

            {/* Purchase Limits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Purchase
                </label>
                <input
                  type="number"
                  value={ticket.min_quantity_per_order}
                  onChange={(e) => handleTicketChange(index, 'min_quantity_per_order', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="1"
                  placeholder="1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Purchase
                </label>
                <input
                  type="number"
                  value={ticket.max_quantity_per_order}
                  onChange={(e) => handleTicketChange(index, 'max_quantity_per_order', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="1"
                  placeholder="10"
                />
              </div>
            </div>

            {/* Additional Options */}
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={`requires_approval_${index}`}
                  checked={ticket.requires_approval}
                  onChange={(e) => handleTicketChange(index, 'requires_approval', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`requires_approval_${index}`} className="ml-2 block text-sm text-gray-900">
                  Requires Approval
                </label>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Terms and Conditions
              </label>
              <textarea
                value={ticket.terms_and_conditions}
                onChange={(e) => handleTicketChange(index, 'terms_and_conditions', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter specific terms and conditions for this ticket type..."
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TicketsTab;
