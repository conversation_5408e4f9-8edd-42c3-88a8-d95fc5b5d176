/**
 * MVP Payment Success Page
 * 
 * Simple confirmation page shown when PayFast redirects back after successful payment.
 * No sensitive logic here - just display confirmation and allow status checking.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  FiCheck,
  FiCalendar,
  FiMapPin,
  FiUser,
  FiMail,
  FiRefreshCw,
  FiHome,
  FiDownload,
  FiEye
} from 'react-icons/fi';
import { useNotification } from '../../contexts/NotificationContext';
import mvpPaymentService from '../../services/mvpPaymentService';
import { LoadingSpinner } from '../../components/ui';

const MVPPaymentSuccess = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { showSuccess, showError, showInfo } = useNotification();
  
  // State
  const [ticketStatus, setTicketStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [paymentParams, setPaymentParams] = useState({});

  // Extract parameters from URL
  useEffect(() => {
    const params = mvpPaymentService.parseReturnParams();
    setPaymentParams(params);
    
    // Log the successful return
    if (params.ticket_id) {
      mvpPaymentService.logPaymentEvent(params.ticket_id, 'success', params);
    }
    
    // Auto-check status if we have a ticket ID
    if (params.ticket_id) {
      checkTicketStatus(params.ticket_id);
    } else {
      setIsLoading(false);
    }
  }, []);

  /**
   * Check ticket status from backend
   */
  const checkTicketStatus = async (ticketId) => {
    if (!ticketId) return;
    
    setIsCheckingStatus(true);
    
    try {
      showInfo('Verifying your ticket...');
      const status = await mvpPaymentService.checkTicketStatus(ticketId);
      setTicketStatus(status);
      
      if (status.status === 'confirmed' || status.status === 'paid') {
        showSuccess('Your ticket has been confirmed!');
      } else if (status.status === 'pending') {
        showInfo('Your payment is being processed. Please wait a moment.');
      } else {
        showError('There was an issue with your payment. Please contact support.');
      }
      
    } catch (error) {
      console.error('Failed to check ticket status:', error);
      showError('Could not verify ticket status. Please try again or contact support.');
    } finally {
      setIsLoading(false);
      setIsCheckingStatus(false);
    }
  };

  /**
   * Handle manual status refresh
   */
  const handleRefreshStatus = () => {
    if (paymentParams.ticket_id) {
      checkTicketStatus(paymentParams.ticket_id);
    }
  };

  /**
   * Navigate to user's events (My Events for students)
   */
  const goToMyEvents = () => {
    navigate('/student/events/my');
  };

  /**
   * Navigate to events
   */
  const goToEvents = () => {
    navigate('/events');
  };

  /**
   * Navigate home
   */
  const goHome = () => {
    navigate('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mb-4" />
          <p className="text-gray-600">Verifying your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiCheck className="w-10 h-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h1>
          <p className="text-gray-600">
            Thank you for your purchase. Your payment has been processed.
          </p>
        </div>

        {/* Payment Details */}
        {paymentParams.ticket_id && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Ticket ID</p>
                <p className="font-medium text-gray-900">{paymentParams.ticket_id}</p>
              </div>
              
              {paymentParams.pf_payment_id && (
                <div>
                  <p className="text-gray-600">Payment ID</p>
                  <p className="font-medium text-gray-900">{paymentParams.pf_payment_id}</p>
                </div>
              )}
              
              {paymentParams.item_name && (
                <div>
                  <p className="text-gray-600">Item</p>
                  <p className="font-medium text-gray-900">{paymentParams.item_name}</p>
                </div>
              )}
              
              {paymentParams.amount_gross && (
                <div>
                  <p className="text-gray-600">Amount Paid</p>
                  <p className="font-medium text-gray-900">R {paymentParams.amount_gross}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Ticket Status */}
        {ticketStatus && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Ticket Status</h2>
              <button
                onClick={handleRefreshStatus}
                disabled={isCheckingStatus}
                className="flex items-center text-blue-600 hover:text-blue-700 disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 mr-1 ${isCheckingStatus ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  ticketStatus.status === 'confirmed' || ticketStatus.status === 'paid' 
                    ? 'bg-green-500' 
                    : ticketStatus.status === 'pending'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`} />
                <span className="font-medium text-gray-900">
                  Status: {ticketStatus.status?.toUpperCase() || 'UNKNOWN'}
                </span>
              </div>
              
              {ticketStatus.event && (
                <div className="border-t pt-4">
                  <h3 className="font-medium text-gray-900 mb-2">Event Details</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <FiCalendar className="w-4 h-4 mr-2" />
                      {ticketStatus.event.title}
                    </div>
                    {ticketStatus.event.date && (
                      <div className="flex items-center">
                        <FiCalendar className="w-4 h-4 mr-2" />
                        {new Date(ticketStatus.event.date).toLocaleDateString()}
                      </div>
                    )}
                    {ticketStatus.event.location && (
                      <div className="flex items-center">
                        <FiMapPin className="w-4 h-4 mr-2" />
                        {ticketStatus.event.location}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">What's Next?</h2>
          
          <div className="space-y-3">
            <button
              onClick={goToMyEvents}
              className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiEye className="w-5 h-5 mr-2" />
              View My Events
            </button>
            
            <button
              onClick={goToEvents}
              className="w-full flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FiCalendar className="w-5 h-5 mr-2" />
              Browse More Events
            </button>
            
            <button
              onClick={goHome}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FiHome className="w-5 h-5 mr-2" />
              Go to Homepage
            </button>
          </div>
        </div>

        {/* Support Info */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Need help? Contact our support team with your payment ID: {paymentParams.pf_payment_id || 'N/A'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default MVPPaymentSuccess;
