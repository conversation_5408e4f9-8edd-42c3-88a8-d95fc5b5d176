/**
 * Exam Session Reconnection Redux Slice
 * Manages reconnection requests, status tracking, and approval workflows
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getAuthToken } from '../../../utils/helpers/authHelpers';

const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';

// Request reconnection to exam session
export const requestReconnection = createAsyncThunk(
  'examReconnection/requestReconnection',
  async ({ sessionId, reason }, { rejectWithValue }) => {
    try {
      console.log('🔄 Requesting reconnection for session:', sessionId);
      
      const response = await fetch(`${BASE_URL}/exams/session/exam-session/${sessionId}/request-reconnection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          session_id: sessionId,
          reason: reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to request reconnection');
      }

      const data = await response.json();
      console.log('✅ Reconnection requested successfully:', data);
      return { sessionId, ...data };
    } catch (error) {
      console.error('❌ Reconnection request failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Check reconnection status
export const checkReconnectionStatus = createAsyncThunk(
  'examReconnection/checkStatus',
  async ({ requestId }, { rejectWithValue }) => {
    try {
      console.log('🔍 Checking reconnection status for request:', requestId);
      
      const response = await fetch(`${BASE_URL}/exams/session/exam-session/reconnection-status/${requestId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to check reconnection status');
      }

      const data = await response.json();
      console.log('📊 Reconnection status:', data);
      return { requestId, ...data };
    } catch (error) {
      console.error('❌ Status check failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Resume exam session after approval
export const resumeExamSession = createAsyncThunk(
  'examReconnection/resumeSession',
  async ({ sessionId }, { rejectWithValue }) => {
    try {
      console.log('▶️ Resuming exam session:', sessionId);
      
      const response = await fetch(`${BASE_URL}/exams/session/exam-session/${sessionId}/resume`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to resume exam session');
      }

      const data = await response.json();
      console.log('✅ Exam session resumed successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Session resume failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Get pending reconnection requests (for teachers)
export const getPendingReconnectionRequests = createAsyncThunk(
  'examReconnection/getPendingRequests',
  async (_, { rejectWithValue }) => {
    try {
      console.log('📋 Fetching pending reconnection requests');
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/reconnection-requests`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch reconnection requests');
      }

      const data = await response.json();
      console.log('📋 Pending requests fetched:', data);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch pending requests:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Approve or deny reconnection request (for teachers)
export const approveReconnectionRequest = createAsyncThunk(
  'examReconnection/approveRequest',
  async ({ requestId, approved, reason }, { rejectWithValue }) => {
    try {
      console.log('⚖️ Processing reconnection request:', { requestId, approved, reason });
      
      const response = await fetch(`${BASE_URL}/exams/session/admin/reconnection-request/${requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          request_id: requestId,
          approved: approved,
          reason: reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to process reconnection request');
      }

      const data = await response.json();
      console.log('✅ Reconnection request processed:', data);
      return { requestId, approved, reason, ...data };
    } catch (error) {
      console.error('❌ Request processing failed:', error);
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  // Student reconnection state
  currentRequest: null,
  requestStatus: 'idle', // idle, pending, approved, denied
  reconnectionReason: '',
  
  // Teacher/Admin state
  pendingRequests: [],
  
  // Session resume state
  resumedSession: null,
  
  // UI state
  loading: false,
  error: null,
  success: null,
  
  // Status polling
  statusPolling: false,
  pollInterval: null
};

const examReconnectionSlice = createSlice({
  name: 'examReconnection',
  initialState,
  reducers: {
    // Clear error and success messages
    clearMessages: (state) => {
      state.error = null;
      state.success = null;
    },
    
    // Reset reconnection state
    resetReconnection: (state) => {
      state.currentRequest = null;
      state.requestStatus = 'idle';
      state.reconnectionReason = '';
      state.resumedSession = null;
      state.error = null;
      state.success = null;
    },
    
    // Start status polling
    startStatusPolling: (state, action) => {
      state.statusPolling = true;
      state.pollInterval = action.payload.interval || 5000;
    },
    
    // Stop status polling
    stopStatusPolling: (state) => {
      state.statusPolling = false;
      state.pollInterval = null;
    },
    
    // Update request status locally
    updateRequestStatus: (state, action) => {
      state.requestStatus = action.payload.status;
      if (action.payload.teacherReason) {
        state.reconnectionReason = action.payload.teacherReason;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Request reconnection
      .addCase(requestReconnection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(requestReconnection.fulfilled, (state, action) => {
        state.loading = false;
        state.currentRequest = action.payload;
        state.requestStatus = 'pending';
        state.success = 'Reconnection request submitted successfully';
      })
      .addCase(requestReconnection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Check status
      .addCase(checkReconnectionStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(checkReconnectionStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.requestStatus = action.payload.status;
        if (action.payload.teacher_reason) {
          state.reconnectionReason = action.payload.teacher_reason;
        }
      })
      .addCase(checkReconnectionStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Resume session
      .addCase(resumeExamSession.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resumeExamSession.fulfilled, (state, action) => {
        state.loading = false;
        state.resumedSession = action.payload;
        state.requestStatus = 'approved';
        state.success = 'Exam session resumed successfully';
      })
      .addCase(resumeExamSession.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get pending requests
      .addCase(getPendingReconnectionRequests.pending, (state) => {
        state.loading = true;
      })
      .addCase(getPendingReconnectionRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.pendingRequests = action.payload;
      })
      .addCase(getPendingReconnectionRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Approve/deny request
      .addCase(approveReconnectionRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(approveReconnectionRequest.fulfilled, (state, action) => {
        state.loading = false;
        // Remove processed request from pending list
        state.pendingRequests = state.pendingRequests.filter(
          req => req.request_id !== action.payload.requestId
        );
        state.success = `Reconnection request ${action.payload.approved ? 'approved' : 'denied'} successfully`;
      })
      .addCase(approveReconnectionRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const {
  clearMessages,
  resetReconnection,
  startStatusPolling,
  stopStatusPolling,
  updateRequestStatus
} = examReconnectionSlice.actions;

// Selectors
export const selectReconnectionState = (state) => state.examReconnection;
export const selectCurrentRequest = (state) => state.examReconnection.currentRequest;
export const selectRequestStatus = (state) => state.examReconnection.requestStatus;
export const selectPendingRequests = (state) => state.examReconnection.pendingRequests;
export const selectResumedSession = (state) => state.examReconnection.resumedSession;

export default examReconnectionSlice.reducer;
