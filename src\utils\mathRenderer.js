/**
 * Math Renderer Utility
 * Handles proper rendering of mathematical expressions and special characters
 */

/**
 * Format mathematical expressions for better display
 * @param {string} text - The text containing mathematical expressions
 * @returns {string} - Formatted text with proper mathematical notation
 */
export const formatMathExpression = (text) => {
  if (!text || typeof text !== 'string') return text;

  let formattedText = text;

  // Handle superscripts (exponents)
  formattedText = formattedText.replace(/\^(\d+)/g, '<sup>$1</sup>');
  formattedText = formattedText.replace(/\^(\([^)]+\))/g, '<sup>$1</sup>');
  formattedText = formattedText.replace(/\^([a-zA-Z])/g, '<sup>$1</sup>');

  // Handle subscripts
  formattedText = formattedText.replace(/_(\d+)/g, '<sub>$1</sub>');
  formattedText = formattedText.replace(/_(\([^)]+\))/g, '<sub>$1</sub>');
  formattedText = formattedText.replace(/_([a-zA-Z])/g, '<sub>$1</sub>');

  // Handle mathematical symbols
  formattedText = formattedText.replace(/\*\*/g, '×'); // Double asterisk to multiplication
  formattedText = formattedText.replace(/(?<!\*)\*(?!\*)/g, '×'); // Single asterisk to multiplication (not part of **)
  formattedText = formattedText.replace(/\+\-/g, '±'); // Plus-minus
  formattedText = formattedText.replace(/\-\+/g, '∓'); // Minus-plus
  formattedText = formattedText.replace(/\<\=/g, '≤'); // Less than or equal
  formattedText = formattedText.replace(/\>\=/g, '≥'); // Greater than or equal
  formattedText = formattedText.replace(/\!\=/g, '≠'); // Not equal
  formattedText = formattedText.replace(/\~\=/g, '≈'); // Approximately equal

  // Handle fractions (simple format: a/b becomes proper fraction)
  formattedText = formattedText.replace(/(\d+)\/(\d+)/g, '<sup>$1</sup>⁄<sub>$2</sub>');

  // Handle square roots
  formattedText = formattedText.replace(/sqrt\(([^)]+)\)/g, '√($1)');
  formattedText = formattedText.replace(/√\(([^)]+)\)/g, '√$1');

  // Handle degree symbol
  formattedText = formattedText.replace(/(\d+)\s*degrees?/gi, '$1°');
  formattedText = formattedText.replace(/(\d+)\s*deg/gi, '$1°');

  // Handle common mathematical functions
  formattedText = formattedText.replace(/\bsin\b/g, 'sin');
  formattedText = formattedText.replace(/\bcos\b/g, 'cos');
  formattedText = formattedText.replace(/\btan\b/g, 'tan');
  formattedText = formattedText.replace(/\blog\b/g, 'log');
  formattedText = formattedText.replace(/\bln\b/g, 'ln');

  return formattedText;
};

/**
 * Format complex mathematical expressions with proper spacing and symbols
 * @param {string} text - The text to format
 * @returns {string} - Formatted text
 */
export const formatComplexMath = (text) => {
  if (!text || typeof text !== 'string') return text;

  let formattedText = formatMathExpression(text);

  // Handle polynomial expressions
  formattedText = formattedText.replace(/x\^(\d+)/g, 'x<sup>$1</sup>');
  formattedText = formattedText.replace(/([a-zA-Z])\^(\d+)/g, '$1<sup>$2</sup>');

  // Handle coefficients (like 2x becomes 2x with proper spacing)
  formattedText = formattedText.replace(/(\d+)([a-zA-Z])/g, '$1$2');

  // Handle mathematical operations with proper spacing
  formattedText = formattedText.replace(/\s*\+\s*/g, ' + ');
  formattedText = formattedText.replace(/\s*\-\s*/g, ' - ');
  formattedText = formattedText.replace(/\s*×\s*/g, ' × ');
  formattedText = formattedText.replace(/\s*÷\s*/g, ' ÷ ');
  formattedText = formattedText.replace(/\s*=\s*/g, ' = ');

  // Handle parentheses with proper spacing
  formattedText = formattedText.replace(/\(\s*/g, '(');
  formattedText = formattedText.replace(/\s*\)/g, ')');

  return formattedText;
};

/**
 * Escape HTML but preserve mathematical formatting
 * @param {string} text - The text to escape
 * @returns {string} - Escaped text with preserved math formatting
 */
export const escapeHtmlPreserveMath = (text) => {
  if (!text || typeof text !== 'string') return text;

  // First format math expressions
  let formattedText = formatComplexMath(text);

  // Then escape remaining HTML (but preserve our math formatting)
  const tempMarkers = [];
  let markerIndex = 0;

  // Temporarily replace our math formatting with markers
  formattedText = formattedText.replace(/<(sup|sub)>([^<]+)<\/(sup|sub)>/g, (match) => {
    const marker = `__MATH_MARKER_${markerIndex++}__`;
    tempMarkers.push({ marker, content: match });
    return marker;
  });

  // Escape HTML
  formattedText = formattedText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  // Restore math formatting
  tempMarkers.forEach(({ marker, content }) => {
    formattedText = formattedText.replace(marker, content);
  });

  return formattedText;
};

/**
 * Check if text contains mathematical expressions
 * @param {string} text - The text to check
 * @returns {boolean} - True if text contains math expressions
 */
export const containsMathExpressions = (text) => {
  if (!text || typeof text !== 'string') return false;

  const mathPatterns = [
    /\^[\d\w\(\)]/,  // Exponents
    /_[\d\w\(\)]/,   // Subscripts
    /\d+\/\d+/,      // Fractions
    /sqrt\(/,        // Square roots
    /[×÷±∓≤≥≠≈]/,    // Math symbols
    /\$[^$]*\$/,     // LaTeX-style math
    /\\[a-zA-Z]+/    // LaTeX commands
  ];

  return mathPatterns.some(pattern => pattern.test(text));
};

/**
 * Convert LaTeX-style math expressions to HTML
 * @param {string} text - Text containing LaTeX expressions
 * @returns {string} - Text with LaTeX converted to HTML
 */
export const convertLatexToHtml = (text) => {
  if (!text || typeof text !== 'string') return text;

  let converted = text;

  // Handle inline math expressions ($...$)
  converted = converted.replace(/\$([^$]+)\$/g, (match, expression) => {
    return `<span class="math-expression">${formatComplexMath(expression)}</span>`;
  });

  // Handle display math expressions ($$...$$)
  converted = converted.replace(/\$\$([^$]+)\$\$/g, (match, expression) => {
    return `<div class="math-expression-block">${formatComplexMath(expression)}</div>`;
  });

  // Handle common LaTeX commands
  converted = converted.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '<sup>$1</sup>⁄<sub>$2</sub>');
  converted = converted.replace(/\\sqrt\{([^}]+)\}/g, '√$1');
  converted = converted.replace(/\\times/g, '×');
  converted = converted.replace(/\\div/g, '÷');
  converted = converted.replace(/\\pm/g, '±');
  converted = converted.replace(/\\mp/g, '∓');
  converted = converted.replace(/\\leq/g, '≤');
  converted = converted.replace(/\\geq/g, '≥');
  converted = converted.replace(/\\neq/g, '≠');
  converted = converted.replace(/\\approx/g, '≈');

  return converted;
};

export default {
  formatMathExpression,
  formatComplexMath,
  escapeHtmlPreserveMath,
  containsMathExpressions,
  convertLatexToHtml
};
