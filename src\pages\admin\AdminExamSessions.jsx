/**
 * Admin Exam Sessions Page
 * Comprehensive page for managing exam sessions with view, delete, and control capabilities
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useThemeProvider } from '../../providers/ThemeContext';
import { useNotification } from '../../contexts/NotificationContext';
import adminSessionService from '../../services/exam/admin/AdminSessionService';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import {
  FiMonitor,
  FiUsers,
  FiClock,
  FiEye,
  FiTrash2,
  FiRefreshCw,
  FiAlertTriangle,
  FiCheckCircle,
  FiXCircle,
  FiSquare,
  FiSearch,
  FiFilter,
  FiActivity,
  FiCalendar
} from 'react-icons/fi';

const AdminExamSessions = () => {
  const { currentTheme } = useThemeProvider();
  const { showSuccess, showError, showWarning } = useNotification();
  
  // State management
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSession, setSelectedSession] = useState(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState(null); // 'view', 'terminate', 'submit'
  const [actionReason, setActionReason] = useState('');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-white text-gray-900 border-gray-300",
    button: currentTheme === "dark" ? "bg-violet-600 hover:bg-violet-700" : "bg-violet-600 hover:bg-violet-700"
  }), [currentTheme]);

  // Load sessions
  const loadSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Debug authentication
      const token = localStorage.getItem('token');
      const role = localStorage.getItem('role');
      console.log('🔍 Debug Auth - Token exists:', !!token);
      console.log('🔍 Debug Auth - Role:', role);
      console.log('🔍 Debug Auth - Token preview:', token ? token.substring(0, 20) + '...' : 'null');

      const data = await adminSessionService.getActiveExamSessions();
      setSessions(data || []);
    } catch (err) {
      console.error('❌ Load sessions error:', err);
      setError(err.message);
      showError(`Failed to load exam sessions: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh setup
  useEffect(() => {
    loadSessions();
    
    if (autoRefresh) {
      adminSessionService.startAutoRefresh((updatedSessions) => {
        setSessions(updatedSessions || []);
      }, 30000);
    }

    return () => {
      adminSessionService.stopAutoRefresh();
    };
  }, [autoRefresh]);

  // Filter sessions based on search and status
  const filteredSessions = useMemo(() => {
    return sessions.filter(session => {
      const matchesSearch = !searchTerm ||
        session.student_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.exam_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.session_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.student_full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.student_username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.student_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.exam_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.institute_name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || session.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [sessions, searchTerm, statusFilter]);

  // Handle session actions
  const handleAction = (session, action) => {
    setSelectedSession(session);
    setActionType(action);
    setActionReason('');
    setShowActionModal(true);
  };

  // Simple terminate function
  const terminateSession = async (session) => {
    try {
      setLoading(true);
      await adminSessionService.terminateExamSession(session.session_id, 'Admin terminated');
      showSuccess('Session terminated successfully');
      loadSessions();
    } catch (err) {
      showError(err.message || 'Failed to terminate session');
    } finally {
      setLoading(false);
    }
  };

  // Execute action
  const executeAction = async () => {
    if (!selectedSession || !actionType) return;

    try {
      setLoading(true);

      switch (actionType) {
        case 'view':
          const details = await adminSessionService.viewExamSession(selectedSession.session_id);
          showSuccess('Session details loaded');
          // You could show details in a modal or navigate to a details page
          console.log('Session details:', details);
          break;

        case 'submit':
          await adminSessionService.forceSubmitExamSession(selectedSession.session_id, actionReason);
          showSuccess('Session submitted successfully');
          loadSessions(); // Refresh the list
          break;

        case 'terminate':
          await adminSessionService.terminateExamSession(selectedSession.session_id, actionReason);
          showSuccess('Session terminated successfully');
          loadSessions(); // Refresh the list
          break;

        case 'delete':
          await adminSessionService.deleteExamSession(selectedSession.session_id);
          showSuccess('Session deleted successfully');
          loadSessions(); // Refresh the list
          break;

        default:
          showError('Unknown action type');
      }
    } catch (err) {
      showError(err.message || 'Action failed');
    } finally {
      setLoading(false);
      setShowActionModal(false);
      setSelectedSession(null);
      setActionType(null);
      setActionReason('');
    }
  };

  // Format duration
  const formatDuration = (startTime) => {
    if (!startTime) return 'N/A';
    const start = new Date(startTime);
    const now = new Date();
    const diff = Math.floor((now - start) / 1000 / 60); // minutes
    return `${diff}m`;
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      'ACTIVE': { color: 'bg-green-100 text-green-800', icon: FiCheckCircle },
      'PAUSED': { color: 'bg-yellow-100 text-yellow-800', icon: FiClock },
      'DISCONNECTED': { color: 'bg-red-100 text-red-800', icon: FiXCircle },
      'SUBMITTED': { color: 'bg-blue-100 text-blue-800', icon: FiCheckCircle },
      'TERMINATED': { color: 'bg-gray-100 text-gray-800', icon: FiSquare }
    };
    
    const config = statusConfig[status] || { color: 'bg-gray-100 text-gray-800', icon: FiActivity };
    const Icon = config.icon;
    
    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {status}
      </span>
    );
  };

  if (loading && sessions.length === 0) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} p-6`}>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeClasses.bg} p-6`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${themeClasses.text} flex items-center gap-3`}>
              <FiMonitor className="text-violet-600" />
              Exam Sessions
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Monitor and manage active exam sessions
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => {
                const token = localStorage.getItem('token');
                const role = localStorage.getItem('role');
                alert(`Auth Debug:\nToken: ${token ? 'Present' : 'Missing'}\nRole: ${role || 'Not set'}\nAPI URL: ${adminSessionService.baseUrl}/api/admin/exam-sessions/active`);
              }}
              className="px-3 py-2 rounded-lg bg-blue-600 text-white flex items-center gap-2 hover:bg-blue-700"
            >
              🔍 Debug Auth
            </button>

            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                autoRefresh
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-600 text-white'
              }`}
            >
              <FiRefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
              Auto Refresh
            </button>

            <button
              onClick={loadSessions}
              disabled={loading}
              className={`px-4 py-2 ${themeClasses.button} text-white rounded-lg flex items-center gap-2 disabled:opacity-50`}
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6`}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className={`block text-sm font-medium ${themeClasses.text} mb-2`}>
              Search Sessions
            </label>
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by student ID, exam ID, or session ID..."
                className={`w-full pl-10 pr-4 py-2 border rounded-lg ${themeClasses.input} focus:ring-2 focus:ring-violet-500 focus:border-transparent`}
              />
            </div>
          </div>
          
          <div>
            <label className={`block text-sm font-medium ${themeClasses.text} mb-2`}>
              Status Filter
            </label>
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg ${themeClasses.input} focus:ring-2 focus:ring-violet-500 focus:border-transparent`}
              >
                <option value="all">All Statuses</option>
                <option value="ACTIVE">Active</option>
                <option value="PAUSED">Paused</option>
                <option value="DISCONNECTED">Disconnected</option>
                <option value="SUBMITTED">Submitted</option>
                <option value="TERMINATED">Terminated</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-end">
            <div className={`text-sm ${themeClasses.text}`}>
              <span className="font-medium">{filteredSessions.length}</span> sessions found
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage 
          message={error} 
          onRetry={loadSessions}
          className="mb-6"
        />
      )}

      {/* Sessions List */}
      <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden`}>
        {filteredSessions.length === 0 ? (
          <div className="text-center py-12">
            <FiMonitor className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No exam sessions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? 'No sessions match your current filters.' 
                : 'No active exam sessions at the moment.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredSessions.map((session) => (
              <div key={session.session_id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <h3 className={`text-lg font-semibold ${themeClasses.text}`}>
                        {session.exam_title || 'Unknown Exam'}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${session.status_display?.includes('🟢') ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}`}>
                        {session.status_display || session.status}
                      </span>
                      {session.requires_attention && (
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                          ⚠️ Needs Attention
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-3">
                      <div className="flex items-center gap-1">
                        <FiUsers className="w-3 h-3" />
                        <span>{session.student_full_name || session.student_username || 'Unknown Student'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FiClock className="w-3 h-3" />
                        <span>Time: {session.time_remaining_minutes}m left</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FiActivity className="w-3 h-3" />
                        <span>Progress: {session.progress_percentage?.toFixed(1) || 0}%</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3" />
                        <span>Questions: {session.questions_answered || 0}/{session.exam_total_questions || 0}</span>
                      </div>
                    </div>

                    {/* Additional Info Row */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-500 dark:text-gray-400">
                      <div>
                        <span className="font-medium">Connection:</span> {session.connection_status || 'Unknown'}
                      </div>
                      <div>
                        <span className="font-medium">Strikes:</span> {session.strikes || 0}/{session.max_strikes || 3}
                      </div>
                      <div>
                        <span className="font-medium">Institute:</span> {session.institute_name || 'Unknown'}
                      </div>
                      <div>
                        <span className="font-medium">Last Activity:</span> {session.last_activity_minutes || 0}m ago
                      </div>
                    </div>

                    {/* Monitoring Alerts */}
                    {session.monitoring_alerts && session.monitoring_alerts.length > 0 && (
                      <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <div className="text-xs font-medium text-yellow-800 dark:text-yellow-200 mb-1">Alerts:</div>
                        <div className="text-xs text-yellow-700 dark:text-yellow-300">
                          {session.monitoring_alerts.join(' • ')}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => handleAction(session, 'view')}
                      className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-1 text-sm"
                    >
                      <FiEye className="w-3 h-3" />
                      View
                    </button>

                    {(session.status === 'active' || session.status === 'ACTIVE') && (
                      <button
                        onClick={() => terminateSession(session)}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 flex items-center gap-1 text-sm"
                      >
                        <FiSquare className="w-3 h-3" />
                        Terminate
                      </button>
                    )}

                    {(session.status === 'submitted' || session.status === 'terminated' ||
                      session.status === 'SUBMITTED' || session.status === 'TERMINATED') && (
                      <button
                        onClick={() => handleAction(session, 'delete')}
                        className="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center gap-1 text-sm"
                      >
                        <FiTrash2 className="w-3 h-3" />
                        Delete
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Modal */}
      {showActionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${themeClasses.cardBg} rounded-xl p-6 w-full max-w-md mx-4`}>
            <div className="flex items-center gap-3 mb-4">
              <FiAlertTriangle className="text-orange-500 w-6 h-6" />
              <h3 className={`text-lg font-semibold ${themeClasses.text}`}>
                Confirm {actionType === 'submit' ? 'Submit' : actionType === 'terminate' ? 'Terminate' : actionType === 'delete' ? 'Delete' : 'View'} Session
              </h3>
            </div>

            <p className={`text-sm ${themeClasses.text} mb-4`}>
              {actionType === 'view'
                ? 'View detailed information for this exam session?'
                : actionType === 'delete'
                ? 'Are you sure you want to delete this exam session? This action cannot be undone and will permanently remove all session data.'
                : `Are you sure you want to ${actionType} this exam session? This action cannot be undone.`
              }
            </p>
            
            {(actionType === 'submit' || actionType === 'terminate' || actionType === 'delete') && (
              <div className="mb-4">
                <label className={`block text-sm font-medium ${themeClasses.text} mb-2`}>
                  {actionType === 'delete' ? 'Confirmation (optional)' : 'Reason (required)'}
                </label>
                <textarea
                  value={actionReason}
                  onChange={(e) => setActionReason(e.target.value)}
                  placeholder={actionType === 'delete' ? 'Optional note for deletion...' : `Enter reason for ${actionType}...`}
                  className={`w-full px-3 py-2 border rounded-lg ${themeClasses.input} focus:ring-2 focus:ring-violet-500 focus:border-transparent`}
                  rows={3}
                  required={actionType !== 'delete'}
                />
              </div>
            )}
            
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowActionModal(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={executeAction}
                disabled={loading || ((actionType === 'submit' || actionType === 'terminate') && !actionReason.trim())}
                className={`px-4 py-2 ${
                  actionType === 'terminate' || actionType === 'delete'
                    ? 'bg-red-600 hover:bg-red-700'
                    : actionType === 'submit'
                    ? 'bg-orange-600 hover:bg-orange-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white rounded-lg disabled:opacity-50`}
              >
                {loading ? 'Processing...' : actionType === 'view' ? 'View Details' : `${actionType.charAt(0).toUpperCase() + actionType.slice(1)} Session`}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminExamSessions;
