/**
 * API Response Helpers
 * Utility functions to handle different API response structures consistently
 */

/**
 * Extract users array from various API response structures
 * @param {any} response - API response that might contain users in different formats
 * @param {string} preferredField - Preferred field to extract from (optional)
 * @returns {Array} Array of users or empty array if not found
 */
export const extractUsersFromResponse = (response, preferredField = null) => {
  console.log('🔍 extractUsersFromResponse called with:', { response, preferredField });

  if (!response) {
    console.log('🔍 No response, returning empty array');
    return [];
  }

  // Direct array response
  if (Array.isArray(response)) {
    console.log('🔍 Direct array response, length:', response.length);
    return response;
  }

  // If a preferred field is specified, try that first
  if (preferredField && response[preferredField] && Array.isArray(response[preferredField])) {
    console.log(`🔍 Found preferred field '${preferredField}', length:`, response[preferredField].length);
    return response[preferredField];
  }

  // Common response structures in order of preference
  // For search results, prioritize followable_users
  if (response.followable_users && Array.isArray(response.followable_users)) {
    console.log('🔍 Found followable_users, length:', response.followable_users.length);
    return response.followable_users;
  }

  if (response.suggestions && Array.isArray(response.suggestions)) {
    console.log('🔍 Found suggestions, length:', response.suggestions.length);
    return response.suggestions;
  }

  if (response.users && Array.isArray(response.users)) {
    console.log('🔍 Found users, length:', response.users.length);
    return response.users;
  }

  if (response.data && Array.isArray(response.data)) {
    console.log('🔍 Found data, length:', response.data.length);
    return response.data;
  }

  if (response.results && Array.isArray(response.results)) {
    console.log('🔍 Found results, length:', response.results.length);
    return response.results;
  }

  // Fallback to empty array
  console.warn('Could not extract users from response:', response);
  return [];
};

/**
 * Extract suggestions specifically from API response
 * @param {any} response - API response from suggestions endpoint
 * @returns {Array} Array of suggested users
 */
export const extractSuggestionsFromResponse = (response) => {
  return extractUsersFromResponse(response, 'suggestions');
};

/**
 * Extract followable users specifically from API response
 * @param {any} response - API response from search/discover endpoint
 * @returns {Array} Array of followable users
 */
export const extractFollowableUsersFromResponse = (response) => {
  console.log('🔍 extractFollowableUsersFromResponse input:', response);
  const result = extractUsersFromResponse(response, 'followable_users');
  console.log('🔍 extractFollowableUsersFromResponse output:', result);
  return result;
};

/**
 * Extract pagination info from API response
 * @param {any} response - API response that might contain pagination info
 * @returns {Object} Pagination info with defaults
 */
export const extractPaginationFromResponse = (response) => {
  const defaults = {
    total_count: 0,
    has_next: false,
    has_previous: false,
    current_page: 1,
    total_pages: 1
  };

  if (!response || typeof response !== 'object') {
    return defaults;
  }

  return {
    total_count: response.total_count || response.total || response.total_followable || response.total_suggestions || defaults.total_count,
    has_next: response.has_next || response.hasNext || defaults.has_next,
    has_previous: response.has_previous || response.hasPrevious || defaults.has_previous,
    current_page: response.current_page || response.page || defaults.current_page,
    total_pages: response.total_pages || response.totalPages || defaults.total_pages
  };
};

/**
 * Extract stats from API response
 * @param {any} response - API response that might contain stats
 * @returns {Object} Stats object with defaults
 */
export const extractStatsFromResponse = (response) => {
  const defaults = {
    followers_count: 0,
    following_count: 0,
    posts_count: 0,
    likes_count: 0
  };

  if (!response || typeof response !== 'object') {
    return defaults;
  }

  return {
    followers_count: response.followers_count || response.followersCount || defaults.followers_count,
    following_count: response.following_count || response.followingCount || defaults.following_count,
    posts_count: response.posts_count || response.postsCount || defaults.posts_count,
    likes_count: response.likes_count || response.likesCount || defaults.likes_count
  };
};

/**
 * Normalize user object from API response
 * @param {Object} user - User object from API
 * @returns {Object} Normalized user object
 */
export const normalizeUserFromResponse = (user) => {
  if (!user || typeof user !== 'object') {
    return null;
  }

  return {
    id: user.id,
    username: user.username || user.name || 'Unknown User',
    email: user.email,
    profile_picture: user.profile_picture || user.profilePicture || user.avatar,
    user_type: user.user_type || user.userType || user.type,
    country: user.country || user.location,
    bio: user.bio || user.description,
    is_following: user.is_following || user.isFollowing || false,
    is_followed_by: user.is_followed_by || user.isFollowedBy || false,
    created_at: user.created_at || user.createdAt,
    updated_at: user.updated_at || user.updatedAt
  };
};

/**
 * Handle API error responses consistently
 * @param {Error} error - Error object from API call
 * @returns {string} User-friendly error message
 */
export const extractErrorMessage = (error) => {
  if (!error) {
    return 'An unexpected error occurred';
  }

  // Check for response data error message
  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }

  if (error.response?.data?.error) {
    return error.response.data.error;
  }

  // Check for direct error message
  if (error.message) {
    return error.message;
  }

  // Fallback based on status code
  if (error.response?.status) {
    switch (error.response.status) {
      case 400:
        return 'Invalid request. Please check your input.';
      case 401:
        return 'Authentication required. Please log in.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 410:
        return 'This feature has been moved or is no longer available.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `Request failed with status ${error.response.status}`;
    }
  }

  return 'An unexpected error occurred';
};

/**
 * Validate if response contains expected data structure
 * @param {any} response - API response to validate
 * @param {string} expectedType - Expected type ('users', 'stats', 'pagination')
 * @returns {boolean} True if response is valid
 */
export const validateResponseStructure = (response, expectedType) => {
  if (!response) {
    return false;
  }

  switch (expectedType) {
    case 'users':
      return Array.isArray(response) || 
             (response.users && Array.isArray(response.users)) ||
             (response.data && Array.isArray(response.data));
    
    case 'stats':
      return typeof response === 'object' && 
             (response.followers_count !== undefined || response.followersCount !== undefined);
    
    case 'pagination':
      return typeof response === 'object' && 
             (response.total_count !== undefined || response.total !== undefined);
    
    default:
      return true;
  }
};
