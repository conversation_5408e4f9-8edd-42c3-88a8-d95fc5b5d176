/**
 * Reconnection Status Indicator
 * Shows current reconnection status and provides quick actions
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../../../providers/ThemeContext';
import {
  checkReconnectionStatus,
  resumeExamSession,
  selectReconnectionState,
  selectCurrentRequest,
  selectRequestStatus
} from '../../../../store/slices/exam/examReconnectionSlice';
import {
  FiWifi,
  FiWifiOff,
  FiClock,
  FiCheckCircle,
  FiXCircle,
  FiLoader,
  FiRefreshCw,
  FiPlay,
  FiAlertCircle
} from 'react-icons/fi';

const ReconnectionStatusIndicator = ({ 
  sessionId, 
  onResumeSuccess,
  className = '' 
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  
  const reconnectionState = useSelector(selectReconnectionState);
  const currentRequest = useSelector(selectCurrentRequest);
  const requestStatus = useSelector(selectRequestStatus);
  
  const [isResuming, setIsResuming] = useState(false);
  const [lastChecked, setLastChecked] = useState(null);

  // Theme-based styling
  const isDark = currentTheme === 'dark';

  // Handle resume exam session
  const handleResumeSession = async () => {
    if (!sessionId) return;

    setIsResuming(true);
    try {
      const result = await dispatch(resumeExamSession({ sessionId })).unwrap();
      if (onResumeSuccess) {
        onResumeSuccess(result);
      }
    } catch (error) {
      console.error('Failed to resume session:', error);
    } finally {
      setIsResuming(false);
    }
  };

  // Handle status check
  const handleCheckStatus = async () => {
    if (!currentRequest?.request_id) return;

    try {
      await dispatch(checkReconnectionStatus({ 
        requestId: currentRequest.request_id 
      })).unwrap();
      setLastChecked(new Date());
    } catch (error) {
      console.error('Failed to check status:', error);
    }
  };

  // Auto-check status periodically when pending
  useEffect(() => {
    if (requestStatus === 'pending' && currentRequest?.request_id) {
      const interval = setInterval(() => {
        handleCheckStatus();
      }, 10000); // Check every 10 seconds

      return () => clearInterval(interval);
    }
  }, [requestStatus, currentRequest?.request_id]);

  // Don't render if no reconnection request
  if (!currentRequest || requestStatus === 'idle') {
    return null;
  }

  const getStatusConfig = () => {
    switch (requestStatus) {
      case 'pending':
        return {
          icon: FiClock,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          title: 'Reconnection Pending',
          message: 'Waiting for teacher approval...',
          showRefresh: true
        };
      case 'approved':
        return {
          icon: FiCheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          title: 'Reconnection Approved',
          message: 'You can now resume your exam',
          showResume: true
        };
      case 'denied':
        return {
          icon: FiXCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          title: 'Reconnection Denied',
          message: 'Contact your teacher for assistance',
          showRefresh: false
        };
      default:
        return {
          icon: FiWifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          title: 'Connection Status',
          message: 'Unknown status',
          showRefresh: false
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  return (
    <div className={`${config.bgColor} border ${config.borderColor} rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        {/* Status Icon */}
        <div className="flex-shrink-0">
          <IconComponent className={`w-5 h-5 ${config.color} ${requestStatus === 'pending' ? 'animate-pulse' : ''}`} />
        </div>

        {/* Status Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
              {config.title}
            </h4>
            {currentRequest.request_id && (
              <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                #{currentRequest.request_id.slice(-8)}
              </span>
            )}
          </div>
          
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
            {config.message}
          </p>

          {/* Additional Info */}
          {reconnectionState.reconnectionReason && requestStatus === 'denied' && (
            <p className="text-xs text-red-600 dark:text-red-400 mb-2">
              Reason: {reconnectionState.reconnectionReason}
            </p>
          )}

          {lastChecked && requestStatus === 'pending' && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              Last checked: {lastChecked.toLocaleTimeString()}
            </p>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            {config.showRefresh && (
              <button
                onClick={handleCheckStatus}
                disabled={reconnectionState.loading}
                className="inline-flex items-center gap-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                <FiRefreshCw className={`w-3 h-3 ${reconnectionState.loading ? 'animate-spin' : ''}`} />
                Check Status
              </button>
            )}

            {config.showResume && (
              <button
                onClick={handleResumeSession}
                disabled={isResuming}
                className="inline-flex items-center gap-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {isResuming ? (
                  <>
                    <FiLoader className="w-3 h-3 animate-spin" />
                    Resuming...
                  </>
                ) : (
                  <>
                    <FiPlay className="w-3 h-3" />
                    Resume Exam
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Progress Indicator for Pending */}
      {requestStatus === 'pending' && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div className="bg-yellow-500 h-1 rounded-full w-1/3 animate-pulse"></div>
            </div>
            <span>Awaiting approval</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReconnectionStatusIndicator;
