import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';
import { 
  checkVerificationStatus,
  setVerificationStatus,
  selectIsEmailVerified,
  selectCheckingStatus,
  selectVerificationErrors,
} from '../../store/slices/EmailVerificationSlice';
import { selectCurrentUser } from '../../store/slices/userSlice';

/**
 * EmailVerificationGuard Component
 *
 * This component checks if the current user's email is verified.
 * If not verified, it redirects to the email verification page.
 * If verified, it renders the children components.
 *
 * Note: Admin users are exempt from email verification requirements.
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Components to render if email is verified
 * @param {boolean} props.requireVerification - Whether to enforce email verification (default: true)
 * @param {string} props.redirectTo - Path to redirect to if not verified (default: '/verify-email')
 * @param {Array<string>} props.exemptRoles - User roles exempt from verification (default: ['admin'])
 */
const EmailVerificationGuard = ({
  children,
  requireVerification = true,
  redirectTo = '/verify-email',
  exemptRoles = ['admin']
}) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasCheckedStatus, setHasCheckedStatus] = useState(false);

  // Redux state
  const currentUser = useSelector(selectCurrentUser);
  const isEmailVerified = useSelector(selectIsEmailVerified);
  const checkingStatus = useSelector(selectCheckingStatus);
  const statusError = useSelector(selectVerificationErrors).statusError;

  // Check if user role is exempt from verification
  const isUserExempt = () => {
    if (!currentUser) return false;

    const userRole = currentUser.user_type || currentUser.role;
    if (!userRole) return false;

    return exemptRoles.some(role =>
      role.toLowerCase() === userRole.toLowerCase()
    );
  };

  // Check verification status on component mount - FIXED to prevent multiple API calls
  useEffect(() => {
    // Only run once when we have user data and haven't initialized yet
    if (!currentUser || isInitialized || hasCheckedStatus || checkingStatus) {
      return;
    }

    const initializeVerificationStatus = async () => {
      setHasCheckedStatus(true);

      try {
        // Check if user is exempt from verification
        if (isUserExempt()) {
          setIsInitialized(true);
          return;
        }

        // Use user data for verification status
        const isVerified = currentUser.is_email_verified === true;

        // Set verification status from user data
        dispatch(setVerificationStatus({
          isEmailVerified: isVerified,
          email: currentUser.email,
          verifiedAt: currentUser.email_verified_at || null,
        }));

        // Only make API call if user is unverified (to double-check)
        if (!isVerified) {
          await dispatch(checkVerificationStatus()).unwrap();
        }
      } catch (error) {
        console.warn('Failed to check verification status:', error);
        // Fallback to user data
        const isVerified = currentUser.is_email_verified === true;
        dispatch(setVerificationStatus({
          isEmailVerified: isVerified,
          email: currentUser.email,
          verifiedAt: currentUser.email_verified_at || null,
        }));
      } finally {
        setIsInitialized(true);
      }
    };

    initializeVerificationStatus();
  }, [dispatch, currentUser, isInitialized, hasCheckedStatus, checkingStatus, exemptRoles]);

  // Show loading state while checking verification status
  if (!isInitialized || checkingStatus) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Checking email verification status...</p>
        </div>
      </div>
    );
  }

  // If verification is not required, always render children
  if (!requireVerification) {
    return children;
  }

  // If user is exempt from verification (e.g., admin), render children
  if (isUserExempt()) {
    return children;
  }

  // If there's an error checking status and no user data, redirect to login
  if (statusError && !currentUser) {
    return <Navigate to="/Login" replace />;
  }

  // If email is already verified and user is on verification page, redirect to dashboard
  if (isEmailVerified && location.pathname === '/verify-email') {
    const userRole = currentUser?.user_type || localStorage.getItem('role');
    if (userRole) {
      const dashboardPath = `/${userRole.toLowerCase()}/dashboard`;
      return <Navigate to={dashboardPath} replace />;
    }
  }

  // If email is not verified, redirect to verification page
  if (!isEmailVerified) {
    return <Navigate to={redirectTo} replace />;
  }

  // Email is verified, render children
  return children;
};

/**
 * Hook for checking email verification status
 * @param {Array<string>} exemptRoles - User roles exempt from verification (default: ['admin'])
 * @returns {Object} Object containing verification status and loading state
 */
export const useEmailVerification = (exemptRoles = ['admin']) => {
  const dispatch = useDispatch();
  const currentUser = useSelector(selectCurrentUser);
  const isEmailVerified = useSelector(selectIsEmailVerified);
  const checkingStatus = useSelector(selectCheckingStatus);
  const statusError = useSelector(selectVerificationErrors).statusError;

  // Check if user role is exempt from verification
  const isUserExempt = () => {
    if (!currentUser) return false;

    const userRole = currentUser.user_type || currentUser.role;
    if (!userRole) return false;

    return exemptRoles.some(role =>
      role.toLowerCase() === userRole.toLowerCase()
    );
  };

  const refreshVerificationStatus = async () => {
    try {
      await dispatch(checkVerificationStatus()).unwrap();
      return true;
    } catch (error) {
      console.error('Failed to refresh verification status:', error);
      return false;
    }
  };

  return {
    isEmailVerified: isUserExempt() ? true : isEmailVerified, // Treat exempt users as verified
    isLoading: checkingStatus,
    error: statusError,
    email: currentUser?.email,
    isExempt: isUserExempt(),
    userRole: currentUser?.user_type || currentUser?.role,
    refreshVerificationStatus,
  };
};

/**
 * Component for conditionally rendering content based on email verification status
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to render if email is verified
 * @param {React.ReactNode} props.fallback - Content to render if email is not verified (optional)
 * @param {boolean} props.requireVerification - Whether to require verification (default: true)
 * @param {Array<string>} props.exemptRoles - User roles exempt from verification (default: ['admin'])
 */
export const EmailVerificationContent = ({
  children,
  fallback = null,
  requireVerification = true,
  exemptRoles = ['admin']
}) => {
  const { isEmailVerified, isLoading, isExempt } = useEmailVerification(exemptRoles);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!requireVerification || isEmailVerified || isExempt) {
    return children;
  }

  return fallback;
};

/**
 * Higher-order component for wrapping components with email verification guard
 * @param {React.Component} Component - Component to wrap
 * @param {Object} options - Guard options (including exemptRoles)
 * @returns {React.Component} - Wrapped component with email verification guard
 */
export const withEmailVerificationGuard = (Component, options = {}) => {
  const defaultOptions = {
    exemptRoles: ['admin'],
    ...options
  };

  const WrappedComponent = (props) => {
    return (
      <EmailVerificationGuard {...defaultOptions}>
        <Component {...props} />
      </EmailVerificationGuard>
    );
  };

  WrappedComponent.displayName = `withEmailVerificationGuard(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default EmailVerificationGuard;
