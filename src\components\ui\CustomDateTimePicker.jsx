import React, { useState, useEffect } from 'react';
import { format, isValid } from 'date-fns';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { FiCalendar, FiClock, FiX } from 'react-icons/fi';
import { cn } from '../../lib/utils';

/**
 * Simplified Custom DateTime Picker Component
 *
 * A clean, easy-to-understand datetime picker that handles timezone conversions properly.
 *
 * Key Features:
 * - Simple local time input/display
 * - Automatic UTC conversion for API storage
 * - Clear, readable date/time format
 * - Calendar + time picker interface
 * - Proper error handling
 *
 * How it works:
 * 1. User sees and selects local time
 * 2. Component converts to UTC for API storage
 * 3. When displaying, converts UTC back to local time
 *
 * @param {string} value - UTC ISO string from API (e.g., "2024-01-15T14:30:00Z")
 * @param {function} onChange - Receives synthetic event with UTC ISO string
 * @param {string} placeholder - Placeholder text
 * @param {boolean} disabled - Whether disabled
 * @param {string} error - Error message
 * @param {string} className - Container classes
 * @param {string} inputClassName - Input classes
 * @param {boolean} required - Whether required
 */
export const CustomDateTimePicker = ({
  value,
  onChange,
  placeholder = "Select date and time",
  disabled = false,
  error,
  className = '',
  inputClassName = '',
  required = false,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeValue, setTimeValue] = useState({ hours: '09', minutes: '00' });
  const [displayValue, setDisplayValue] = useState('');

  // Initialize from value prop (UTC from API)
  useEffect(() => {
    if (value) {
      try {
        // Parse UTC time from API and convert to local time for display
        const utcDate = new Date(value);

        if (isValid(utcDate)) {
          // Set the date and time in local timezone for user display
          setSelectedDate(utcDate);
          setTimeValue({
            hours: String(utcDate.getHours()).padStart(2, '0'),
            minutes: String(utcDate.getMinutes()).padStart(2, '0')
          });

          // Create clean display format
          const displayText = format(utcDate, 'MMM dd, yyyy - HH:mm');
          setDisplayValue(displayText);

          console.log('📅 DatePicker initialized:', {
            utcInput: value,
            localDisplay: displayText,
            localDate: utcDate
          });
        }
      } catch (error) {
        console.warn('Invalid date value:', value);
        clearSelection();
      }
    } else {
      clearSelection();
    }
  }, [value]);

  // Clear all selections
  const clearSelection = () => {
    setSelectedDate(null);
    setTimeValue({ hours: '09', minutes: '00' });
    setDisplayValue('');
  };

  // Handle date selection from calendar
  const handleDateSelect = (date) => {
    if (date) {
      setSelectedDate(date);
      updateDateTime(date, timeValue);
    }
  };

  // Handle time change
  const handleTimeChange = (field, newValue) => {
    const newTimeValue = { ...timeValue, [field]: newValue };
    setTimeValue(newTimeValue);

    if (selectedDate) {
      updateDateTime(selectedDate, newTimeValue);
    }
  };

  // Update the combined datetime and send to parent
  const updateDateTime = (date, time) => {
    if (!date) return;

    // Create local datetime with selected date and time
    const localDateTime = new Date(date);
    localDateTime.setHours(parseInt(time.hours, 10), parseInt(time.minutes, 10), 0, 0);

    // Convert to UTC for API storage
    const utcISOString = localDateTime.toISOString();

    // Update display with clean format
    const displayText = format(localDateTime, 'MMM dd, yyyy - HH:mm');
    setDisplayValue(displayText);

    console.log('🕒 DateTime updated:', {
      localTime: displayText,
      utcForAPI: utcISOString,
      userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      localDateTime: localDateTime.toString(),
      utcDateTime: new Date(utcISOString).toUTCString()
    });

    // Send UTC time to parent component
    const syntheticEvent = {
      target: {
        value: utcISOString,
        name: props.name
      }
    };

    onChange?.(syntheticEvent);
  };

  // Clear the selection
  const handleClear = (e) => {
    e.stopPropagation();
    clearSelection();

    const syntheticEvent = {
      target: {
        value: '',
        name: props.name
      }
    };

    onChange?.(syntheticEvent);
  };

  // Generate hour options (0-23)
  const hourOptions = Array.from({ length: 24 }, (_, i) =>
    String(i).padStart(2, '0')
  );

  // Generate minute options (0-59, every 5 minutes for simplicity)
  const minuteOptions = Array.from({ length: 12 }, (_, i) =>
    String(i * 5).padStart(2, '0')
  );

  const hasError = !!error;
  const baseInputClasses = cn(
    "flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm",
    "placeholder:text-gray-500 dark:placeholder:text-gray-400",
    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
    "disabled:cursor-not-allowed disabled:opacity-50",
    hasError && "border-red-500 focus:ring-red-500",
    inputClassName
  );

  return (
    <div className={cn("relative", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <div className="relative">
          <PopoverTrigger asChild>
            <button
              type="button"
              disabled={disabled}
              className={cn(
                baseInputClasses,
                "justify-between cursor-pointer pr-20",
                !displayValue && "text-gray-500 dark:text-gray-400"
              )}
              {...props}
            >
              <span className="flex items-center gap-2">
                <FiCalendar className="h-4 w-4 text-gray-400" />
                {displayValue || placeholder}
              </span>
              <FiClock className="h-4 w-4 text-gray-400" />
            </button>
          </PopoverTrigger>
          {displayValue && (
            <button
              type="button"
              onClick={handleClear}
              className="absolute right-8 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded z-10"
            >
              <FiX className="h-3 w-3" />
            </button>
          )}
        </div>
        
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {/* Calendar Section */}
            <div className="p-3">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={disabled}
                className="rounded-md border-0"
              />
            </div>
            
            {/* Time Section */}
            <div className="border-l border-gray-200 dark:border-gray-700 p-4 min-w-[120px]">
              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                  <FiClock className="h-4 w-4" />
                  Time
                </div>
                
                <div className="space-y-2">
                  <div>
                    <label className="text-xs text-gray-500 dark:text-gray-400">Hour</label>
                    <select
                      value={timeValue.hours}
                      onChange={(e) => handleTimeChange('hours', e.target.value)}
                      className="w-full mt-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                    >
                      {hourOptions.map(hour => (
                        <option key={hour} value={hour}>{hour}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="text-xs text-gray-500 dark:text-gray-400">Minute</label>
                    <select
                      value={timeValue.minutes}
                      onChange={(e) => handleTimeChange('minutes', e.target.value)}
                      className="w-full mt-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                    >
                      {minuteOptions.map(minute => (
                        <option key={minute} value={minute}>{minute}</option>
                      ))}
                    </select>
                  </div>
                </div>
                
                {selectedDate && (
                  <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Preview:</div>
                    <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      {format(selectedDate, 'MMM dd, yyyy')} at {timeValue.hours}:{timeValue.minutes}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Local time
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {hasError && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
};

export default CustomDateTimePicker;
