import React from 'react';
import { 
  Fi<PERSON><PERSON>ding<PERSON>p, 
  FiTrendingDown, 
  FiMinus,
  FiTarget,
  FiAward,
  FiBookOpen,
  FiUsers
} from 'react-icons/fi';
import { D3LineChart } from '../charts';

/**
 * Performance Overview Component
 * Displays key performance metrics and trends
 */
const PerformanceOverview = ({ 
  data, 
  loading = false, 
  className = '' 
}) => {
  if (loading) {
    return (
      <div className={`animate-pulse space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No performance data available</p>
      </div>
    );
  }

  // Extract metrics from data
  const overallScore = data.overall_performance_score || 0;
  const gpa = data.subject_analytics?.overall_gpa || 0;
  const classRank = data.class_grade_analytics?.class_performance?.[0]?.student_rank || 'N/A';
  const totalStudents = data.class_grade_analytics?.class_performance?.[0]?.total_students || 0;
  const studyHours = data.subject_analytics?.total_study_hours || 0;
  const competitionAwards = data.competition_analytics?.summary?.total_awards || 0;

  // Performance trend data
  const trendData = data.subject_analytics?.subjects?.[0]?.performance_trend?.map(item => ({
    x: item.period,
    y: item.value
  })) || [];

  // Get trend direction
  const getTrendIcon = (current, previous) => {
    if (current > previous) return <FiTrendingUp className="w-4 h-4 text-green-500" />;
    if (current < previous) return <FiTrendingDown className="w-4 h-4 text-red-500" />;
    return <FiMinus className="w-4 h-4 text-gray-500" />;
  };

  // Calculate trend percentage
  const getTrendPercentage = (current, previous) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  // Mock previous values for trend calculation
  const previousScore = overallScore - 3.2;
  const previousGPA = gpa - 0.1;
  const previousHours = studyHours - 5;

  const metrics = [
    {
      title: 'Overall Performance',
      value: `${overallScore.toFixed(1)}%`,
      icon: FiTarget,
      color: 'blue',
      trend: getTrendIcon(overallScore, previousScore),
      change: `${getTrendPercentage(overallScore, previousScore)}%`,
      subtitle: 'Academic average'
    },
    {
      title: 'GPA',
      value: gpa.toFixed(2),
      icon: FiBookOpen,
      color: 'green',
      trend: getTrendIcon(gpa, previousGPA),
      change: `${getTrendPercentage(gpa, previousGPA)}%`,
      subtitle: 'Grade point average'
    },
    {
      title: 'Class Rank',
      value: `#${classRank}`,
      icon: FiUsers,
      color: 'purple',
      subtitle: `of ${totalStudents} students`,
      percentile: classRank !== 'N/A' ? Math.round((1 - (classRank - 1) / totalStudents) * 100) : 0
    },
    {
      title: 'Study Hours',
      value: studyHours,
      icon: FiTrendingUp,
      color: 'yellow',
      trend: getTrendIcon(studyHours, previousHours),
      change: `${getTrendPercentage(studyHours, previousHours)}%`,
      subtitle: 'This period'
    }
  ];

  return (
    <div className={className}>
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {metric.title}
                </p>
                <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                  {metric.value}
                </p>
                
                {/* Trend and change */}
                {metric.trend && metric.change && (
                  <div className="flex items-center mt-2">
                    {metric.trend}
                    <span className={`text-xs ml-1 ${
                      parseFloat(metric.change) > 0 
                        ? 'text-green-600' 
                        : parseFloat(metric.change) < 0 
                        ? 'text-red-600' 
                        : 'text-gray-500'
                    }`}>
                      {parseFloat(metric.change) > 0 ? '+' : ''}{metric.change}
                    </span>
                  </div>
                )}
                
                {/* Subtitle or percentile */}
                {metric.subtitle && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    {metric.subtitle}
                  </p>
                )}
                
                {metric.percentile && (
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    {metric.percentile}th percentile
                  </p>
                )}
              </div>
              
              <div className={`p-3 rounded-lg bg-${metric.color}-100 dark:bg-${metric.color}-900/20`}>
                <metric.icon className={`w-6 h-6 text-${metric.color}-600 dark:text-${metric.color}-400`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Performance Trend Chart */}
      {trendData.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
              Performance Trend
            </h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Performance Score</span>
              </div>
            </div>
          </div>
          
          <D3LineChart
            data={trendData}
            width={800}
            height={300}
            xKey="x"
            yKey="y"
            showDots={true}
            showArea={true}
            animate={true}
            xAxisLabel="Period"
            yAxisLabel="Score"
          />
        </div>
      )}

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Strongest Subject */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
          <div className="flex items-center mb-3">
            <FiAward className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
            <h4 className="font-semibold text-green-800 dark:text-green-200">
              Strongest Subject
            </h4>
          </div>
          <p className="text-lg font-bold text-green-800 dark:text-green-200">
            {data.subject_analytics?.strongest_subject || 'N/A'}
          </p>
          {data.subject_analytics?.subjects?.find(s => s.subject_name === data.subject_analytics?.strongest_subject) && (
            <p className="text-sm text-green-600 dark:text-green-400 mt-1">
              Average: {data.subject_analytics.subjects.find(s => s.subject_name === data.subject_analytics.strongest_subject).average_score}%
            </p>
          )}
        </div>

        {/* Competition Performance */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
          <div className="flex items-center mb-3">
            <FiAward className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
            <h4 className="font-semibold text-yellow-800 dark:text-yellow-200">
              Competition Awards
            </h4>
          </div>
          <p className="text-lg font-bold text-yellow-800 dark:text-yellow-200">
            {competitionAwards} Awards
          </p>
          <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-1">
            {data.competition_analytics?.summary?.top_10_finishes || 0} top 10 finishes
          </p>
        </div>

        {/* Engagement Score */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
          <div className="flex items-center mb-3">
            <FiUsers className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h4 className="font-semibold text-blue-800 dark:text-blue-200">
              Engagement Score
            </h4>
          </div>
          <p className="text-lg font-bold text-blue-800 dark:text-blue-200">
            {data.classroom_analytics?.overall_engagement_score || 0}%
          </p>
          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
            {data.classroom_analytics?.engagement_trend || 'stable'} trend
          </p>
        </div>
      </div>
    </div>
  );
};

export default PerformanceOverview;
