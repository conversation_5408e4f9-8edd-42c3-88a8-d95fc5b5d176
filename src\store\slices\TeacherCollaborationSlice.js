import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

const BASE_URL = `${API_BASE_URL}/api/institute/teachers`;
const TEACHER_BASE_URL = `${API_BASE_URL}/api/teacher`;
const getAuthToken = () => localStorage.getItem("token");

// Helper function to get auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

// Async thunks for Teacher-Institute Invitation Management
export const fetchTeacherSentInvitations = createAsyncThunk(
  'teacherCollaboration/fetchSentInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/sent`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      // Handle successful response with empty data
      if (res.status === 200) {
        return res.data || { invitations: [], total: 0, page: 1, size: 20 };
      }

      return res.data;
    } catch (err) {
      console.error('Error fetching teacher sent invitations:', err);
      const errorMessage = err.response?.data?.message || err.response?.data?.error || err.message || 'Failed to fetch sent invitations';
      return thunkAPI.rejectWithValue(errorMessage);
    }
  }
);

export const fetchTeacherReceivedInvitations = createAsyncThunk(
  'teacherCollaboration/fetchReceivedInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/received`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });

      // Handle successful response with empty data
      if (res.status === 200) {
        return res.data || { invitations: [], total: 0, page: 1, size: 20 };
      }

      return res.data;
    } catch (err) {
      console.error('Error fetching teacher received invitations:', err);
      const errorMessage = err.response?.data?.message || err.response?.data?.error || err.message || 'Failed to fetch received invitations';
      return thunkAPI.rejectWithValue(errorMessage);
    }
  }
);

// Send invitation to teacher (from institute)
export const sendInvitationToTeacher = createAsyncThunk(
  'teacherCollaboration/sendInvitationToTeacher',
  async (invitationData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/send-to-teacher`, invitationData, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Send invitation to institute (from teacher)
export const sendInvitationToInstituteFromTeacher = createAsyncThunk(
  'teacherCollaboration/sendInvitationToInstitute',
  async (invitationData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/send-to-institute`, invitationData, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Accept invitation
export const acceptTeacherInvitation = createAsyncThunk(
  'teacherCollaboration/acceptInvitation',
  async (invitationId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/invite/${invitationId}/respond`, {
        action: 'accept'
      }, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Reject invitation
export const rejectTeacherInvitation = createAsyncThunk(
  'teacherCollaboration/rejectInvitation',
  async (invitationId, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/invite/${invitationId}/respond`, {
        action: 'reject'
      }, {
        headers: getAuthHeaders()
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch all teacher invitations (both sent and received)
export const fetchAllTeacherInvitations = createAsyncThunk(
  'teacherCollaboration/fetchAllInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const [sentRes, receivedRes] = await Promise.all([
        axios.get(`${BASE_URL}/sent`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        }),
        axios.get(`${BASE_URL}/received`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        })
      ]);

      return {
        sent: sentRes.data,
        received: receivedRes.data
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Async thunks for Teacher-Institute Collaboration Management

// Get teacher collaborations for current user
export const getTeacherCollaborations = createAsyncThunk(
  'teacherCollaboration/getCollaborations',
  async ({ statusFilter = '', page = 1, size = 20 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString()
      });

      if (statusFilter) {
        params.append('status_filter', statusFilter);
      }

      const response = await axios.get(
        `${TEACHER_BASE_URL}/collaborations/?${params.toString()}`,
        { headers: getAuthHeaders() }
      );

      // Handle successful response with empty data
      if (response.status === 200) {
        return response.data || { collaborations: [], total: 0, page: 1, size: 20 };
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching teacher collaborations:', error);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || 'Failed to fetch collaborations';
      return rejectWithValue(errorMessage);
    }
  }
);

// Get teacher collaboration details by ID
export const getTeacherCollaborationById = createAsyncThunk(
  'teacherCollaboration/getCollaborationById',
  async (collaborationId, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${TEACHER_BASE_URL}/collaborations/${collaborationId}`,
        { headers: getAuthHeaders() }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Update teacher collaboration
export const updateTeacherCollaboration = createAsyncThunk(
  'teacherCollaboration/updateCollaboration',
  async ({ collaborationId, updateData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(
        `${TEACHER_BASE_URL}/collaborations/${collaborationId}`,
        updateData,
        { headers: getAuthHeaders() }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Delete teacher collaboration
export const deleteTeacherCollaboration = createAsyncThunk(
  'teacherCollaboration/deleteCollaboration',
  async (collaborationId, { rejectWithValue }) => {
    try {
      await axios.delete(
        `${TEACHER_BASE_URL}/collaborations/${collaborationId}`,
        { headers: getAuthHeaders() }
      );

      return collaborationId;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  sentInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  receivedInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  sendInvitation: {
    loading: false,
    error: null,
    success: false
  },
  respondInvitation: {
    loading: false,
    error: null,
    success: false
  },
  collaborations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  currentCollaboration: null,
  collaborationActions: {
    loading: false,
    error: null,
    success: false
  }
};

const teacherCollaborationSlice = createSlice({
  name: 'teacherCollaboration',
  initialState,
  reducers: {
    clearSendInvitationState: (state) => {
      state.sendInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearRespondInvitationState: (state) => {
      state.respondInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearCollaborationActions: (state) => {
      state.collaborationActions = {
        loading: false,
        error: null,
        success: false
      };
    },
    setCurrentCollaboration: (state, action) => {
      state.currentCollaboration = action.payload;
    },
    clearCurrentCollaboration: (state) => {
      state.currentCollaboration = null;
    }
  },
  extraReducers: (builder) => {
    // Sent Invitations
    builder
      .addCase(fetchTeacherSentInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.sentInvitations.error = null;
      })
      .addCase(fetchTeacherSentInvitations.fulfilled, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.data = action.payload.invitations || [];
        state.sentInvitations.total = action.payload.total || 0;
        state.sentInvitations.page = action.payload.page || 1;
        state.sentInvitations.size = action.payload.size || 20;
        state.sentInvitations.hasNext = action.payload.hasNext || false;
        state.sentInvitations.hasPrev = action.payload.hasPrev || false;
      })
      .addCase(fetchTeacherSentInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.error = action.payload;
      })

    // Received Invitations
      .addCase(fetchTeacherReceivedInvitations.pending, (state) => {
        state.receivedInvitations.loading = true;
        state.receivedInvitations.error = null;
      })
      .addCase(fetchTeacherReceivedInvitations.fulfilled, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.data = action.payload.invitations || [];
        state.receivedInvitations.total = action.payload.total || 0;
        state.receivedInvitations.page = action.payload.page || 1;
        state.receivedInvitations.size = action.payload.size || 20;
        state.receivedInvitations.hasNext = action.payload.hasNext || false;
        state.receivedInvitations.hasPrev = action.payload.hasPrev || false;
      })
      .addCase(fetchTeacherReceivedInvitations.rejected, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.error = action.payload;
      })

    // Send Invitations
      .addCase(sendInvitationToTeacher.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
      })
      .addCase(sendInvitationToTeacher.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
      })
      .addCase(sendInvitationToTeacher.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
      })

      .addCase(sendInvitationToInstituteFromTeacher.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
      })
      .addCase(sendInvitationToInstituteFromTeacher.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
      })
      .addCase(sendInvitationToInstituteFromTeacher.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
      })

    // Respond to Invitations
      .addCase(acceptTeacherInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
      })
      .addCase(acceptTeacherInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
      })
      .addCase(acceptTeacherInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
      })

      .addCase(rejectTeacherInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
      })
      .addCase(rejectTeacherInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
      })
      .addCase(rejectTeacherInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
      })

    // Collaborations
      .addCase(getTeacherCollaborations.pending, (state) => {
        state.collaborations.loading = true;
        state.collaborations.error = null;
      })
      .addCase(getTeacherCollaborations.fulfilled, (state, action) => {
        state.collaborations.loading = false;
        state.collaborations.data = action.payload.collaborations || [];
        state.collaborations.total = action.payload.total || 0;
        state.collaborations.page = action.payload.page || 1;
        state.collaborations.size = action.payload.size || 20;
        state.collaborations.hasNext = action.payload.hasNext || false;
        state.collaborations.hasPrev = action.payload.hasPrev || false;
      })
      .addCase(getTeacherCollaborations.rejected, (state, action) => {
        state.collaborations.loading = false;
        state.collaborations.error = action.payload;
      })

    // Collaboration Actions
      .addCase(updateTeacherCollaboration.pending, (state) => {
        state.collaborationActions.loading = true;
        state.collaborationActions.error = null;
        state.collaborationActions.success = false;
      })
      .addCase(updateTeacherCollaboration.fulfilled, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.success = true;
        // Update the collaboration in the list
        const index = state.collaborations.data.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.collaborations.data[index] = action.payload;
        }
      })
      .addCase(updateTeacherCollaboration.rejected, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.error = action.payload;
      })

      .addCase(deleteTeacherCollaboration.pending, (state) => {
        state.collaborationActions.loading = true;
        state.collaborationActions.error = null;
        state.collaborationActions.success = false;
      })
      .addCase(deleteTeacherCollaboration.fulfilled, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.success = true;
        // Remove the collaboration from the list
        state.collaborations.data = state.collaborations.data.filter(c => c.id !== action.payload);
        state.collaborations.total = Math.max(0, state.collaborations.total - 1);
      })
      .addCase(deleteTeacherCollaboration.rejected, (state, action) => {
        state.collaborationActions.loading = false;
        state.collaborationActions.error = action.payload;
      });
  }
});

export const {
  clearSendInvitationState,
  clearRespondInvitationState,
  clearCollaborationActions,
  setCurrentCollaboration,
  clearCurrentCollaboration
} = teacherCollaborationSlice.actions;

// Selectors
export const selectTeacherSentInvitations = (state) => state.teacherCollaboration.sentInvitations;
export const selectTeacherReceivedInvitations = (state) => state.teacherCollaboration.receivedInvitations;
export const selectTeacherSendInvitationState = (state) => state.teacherCollaboration.sendInvitation;
export const selectTeacherRespondInvitationState = (state) => state.teacherCollaboration.respondInvitation;
export const selectTeacherCollaborations = (state) => state.teacherCollaboration.collaborations;
export const selectTeacherCurrentCollaboration = (state) => state.teacherCollaboration.currentCollaboration;
export const selectTeacherCollaborationActions = (state) => state.teacherCollaboration.collaborationActions;

export default teacherCollaborationSlice.reducer;
