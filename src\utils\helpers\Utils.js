export const formatValue = (value) => Intl.NumberFormat('en-PK', {
  style: 'currency',
  currency: 'PKR',
  maximumSignificantDigits: 3,
  notation: 'compact',
}).format(value);

export const formatThousands = (value) => Intl.NumberFormat('en-US', {
  maximumSignificantDigits: 3,
  notation: 'compact',
}).format(value);

export const getCssVariable = (variable) => {
  return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
};

const adjustHexOpacity = (hexColor, opacity) => {
  // Remove the '#' if it exists
  hexColor = hexColor.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hexColor.substring(0, 2), 16);
  const g = parseInt(hexColor.substring(2, 4), 16);
  const b = parseInt(hexColor.substring(4, 6), 16);

  // Return RGBA string
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

const adjustHSLOpacity = (hslColor, opacity) => {
  // Convert HSL to HSLA
  return hslColor.replace('hsl(', 'hsla(').replace(')', `, ${opacity})`);
};

const adjustOKLCHOpacity = (oklchColor, opacity) => {
  // Add alpha value to OKLCH color
  return oklchColor.replace(/oklch\((.*?)\)/, (match, p1) => `oklch(${p1} / ${opacity})`);
};

const adjustRGBAOpacity = (rgbaColor, opacity) => {
  // Extract RGB values and replace alpha
  const match = rgbaColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
  if (match) {
    const [, r, g, b] = match;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return rgbaColor;
};

export const adjustColorOpacity = (color, opacity) => {
  // Handle null, undefined, or empty color values
  if (!color || typeof color !== 'string') {
    console.warn('Invalid color value provided to adjustColorOpacity:', color);
    return `rgba(0, 0, 0, ${opacity})`;
  }

  // Trim whitespace
  color = color.trim();

  // Handle empty string
  if (color === '') {
    console.warn('Empty color value provided to adjustColorOpacity');
    return `rgba(0, 0, 0, ${opacity})`;
  }

  try {
    if (color.startsWith('#')) {
      return adjustHexOpacity(color, opacity);
    } else if (color.startsWith('hsl')) {
      return adjustHSLOpacity(color, opacity);
    } else if (color.startsWith('oklch')) {
      return adjustOKLCHOpacity(color, opacity);
    } else if (color.startsWith('rgb')) {
      return adjustRGBAOpacity(color, opacity);
    } else {
      console.warn('Unsupported color format:', color);
      return `rgba(0, 0, 0, ${opacity})`;
    }
  } catch (error) {
    console.warn('Error adjusting color opacity:', error, 'Color:', color);
    return `rgba(0, 0, 0, ${opacity})`;
  }
};

export const oklchToRGBA = (oklchColor) => {
  // Create a temporary div to use for color conversion
  const tempDiv = document.createElement('div');
  tempDiv.style.color = oklchColor;
  document.body.appendChild(tempDiv);
  
  // Get the computed style and convert to RGB
  const computedColor = window.getComputedStyle(tempDiv).color;
  document.body.removeChild(tempDiv);
  
  return computedColor;
};