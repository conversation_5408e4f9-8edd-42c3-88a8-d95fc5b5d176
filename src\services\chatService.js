import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Chat Service
 * Handles all chat-related API calls including messaging, conversations, and chat statistics
 */
class ChatService {
  constructor() {
    this.baseUrl = `${API_BASE_URL}/api/social/chat`;
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Handle API errors consistently
   */
  handleApiError(error) {
    console.error('Chat API Error:', error);
    
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          throw new Error('Authentication required. Please log in again.');
        case 403:
          throw new Error('You can only message users you follow.');
        case 404:
          throw new Error('User or conversation not found.');
        case 422:
          throw new Error(data.detail?.[0]?.msg || 'Invalid request data.');
        case 429:
          throw new Error('Too many requests. Please wait before sending another message.');
        case 500:
          throw new Error('Server error. Please try again later.');
        default:
          throw new Error(data.message || 'An unexpected error occurred.');
      }
    }
    
    if (error.request) {
      throw new Error('Network error. Please check your connection.');
    }
    
    throw new Error(error.message || 'An unexpected error occurred.');
  }

  /**
   * Send a chat message
   * POST /api/social/chat/send
   */
  async sendMessage(receiverId, message) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/send`,
        {
          receiver_id: receiverId,
          message: message.trim()
        },
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get user conversations
   * GET /api/social/chat/conversations
   */
  async getConversations(page = 1, pageSize = 20) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/conversations`,
        {
          params: { page, page_size: pageSize },
          headers: this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get conversation messages with a specific user
   * GET /api/social/chat/conversation/{user_id}/messages
   */
  async getConversationMessages(userId, page = 1, pageSize = 50) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/conversation/${userId}/messages`,
        {
          params: { page, page_size: pageSize },
          headers: this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get a specific chat message
   * GET /api/social/chat/message/{message_id}
   */
  async getMessage(messageId) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/message/${messageId}`,
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete a chat message
   * DELETE /api/social/chat/message/{message_id}
   */
  async deleteMessage(messageId) {
    try {
      const response = await axios.delete(
        `${this.baseUrl}/message/${messageId}`,
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Mark a message as read
   * PUT /api/social/chat/message/{message_id}/read
   */
  async markMessageRead(messageId) {
    try {
      const response = await axios.put(
        `${this.baseUrl}/message/${messageId}/read`,
        {},
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Mark all messages in a conversation as read
   * PUT /api/social/chat/conversation/{user_id}/read
   */
  async markConversationRead(userId) {
    try {
      const response = await axios.put(
        `${this.baseUrl}/conversation/${userId}/read`,
        {},
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get chat statistics
   * GET /api/social/chat/stats
   */
  async getChatStats() {
    try {
      const response = await axios.get(
        `${this.baseUrl}/stats`,
        { headers: this.getAuthHeaders() }
      );

      return response.data;
    } catch (error) {
      console.warn('Chat stats endpoint error:', error.response?.data || error.message);

      // If the backend has issues, return default stats to prevent UI breaking
      if (error.response?.status === 500) {
        console.warn('🚨 BACKEND ISSUE: Chat stats endpoint has a server error');
        console.warn('Error details:', error.response?.data?.detail || 'Unknown server error');
        console.warn('💡 Suggestion: Check backend implementation for Function.__init__() else_ parameter issue');
        console.log('📊 Returning default chat stats to maintain UI functionality');

        return {
          user_id: localStorage.getItem('userId'),
          total_conversations: 0,
          total_messages_sent: 0,
          total_messages_received: 0,
          unread_messages_count: 0,
          active_conversations_count: 0
        };
      }

      throw this.handleApiError(error);
    }
  }

  /**
   * Bulk mark messages as read
   * POST /api/social/chat/bulk/read
   */
  async bulkMarkMessagesRead(messageIds) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/bulk/read`,
        { message_ids: messageIds },
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Bulk delete messages
   * POST /api/social/chat/bulk/delete
   */
  async bulkDeleteMessages(messageIds, permanent = false) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/bulk/delete`,
        { 
          message_ids: messageIds,
          permanent 
        },
        { headers: this.getAuthHeaders() }
      );
      
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }
}

// Export singleton instance
const chatService = new ChatService();
export default chatService;
