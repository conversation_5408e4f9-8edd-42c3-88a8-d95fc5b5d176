/**
 * Enhanced Speaker Manager Component
 * Manages event speakers with full API integration
 */

import React, { useState, useEffect, useRef } from 'react';
import { FiPlus, FiEdit2, FiTrash2, FiUser, FiMail, FiPhone, FiGlobe, FiLinkedin, FiTwitter, FiRefreshCw, FiChevronDown, FiX, FiSearch, FiCheck } from 'react-icons/fi';
import speakerService from '../../services/speakerService';
import { LoadingSpinner, ErrorMessage } from '../ui';

const SpeakerManager = ({ 
  selectedSpeakers = [], 
  onSpeakersChange,
  className = '' 
}) => {
  const [speakers, setSpeakers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [editingSpeaker, setEditingSpeaker] = useState(null);

  // Dropdown state
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const dropdownRef = useRef(null);
  const [newSpeaker, setNewSpeaker] = useState({
    name: '',
    title: '',
    bio: '',
    profile_image_url: '',
    company: '',
    website: '',
    linkedin_url: '',
    twitter_url: '',
    email: '',
    phone: '',
    expertise_areas: [],
    is_featured: false
  });

  const expertiseOptions = [
    'Technology', 'Business', 'Education', 'Healthcare', 'Finance',
    'Marketing', 'Leadership', 'Innovation', 'Entrepreneurship', 'AI/ML',
    'Data Science', 'Cybersecurity', 'Sustainability', 'Design', 'Research',
    'Software Development', 'Project Management', 'Digital Marketing'
  ];

  // Load speakers on component mount
  useEffect(() => {
    loadSpeakers();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const loadSpeakers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Loading speakers from API...');

      // Use the API parameters: skip=0, limit=100
      const speakersData = await speakerService.getAllSpeakers(0, 100);
      console.log('✅ Speakers loaded successfully:', speakersData);

      // The API returns an array of speakers directly
      setSpeakers(Array.isArray(speakersData) ? speakersData : []);
    } catch (err) {
      console.error('❌ Failed to load speakers:', err);
      setError(err.message);
      // Set empty array on error to avoid undefined issues
      setSpeakers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSpeaker = async () => {
    try {
      setError(null);
      console.log('🔄 Creating new speaker:', newSpeaker);

      // Validate speaker data
      const validation = speakerService.validateSpeakerData(newSpeaker);
      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        return;
      }

      const formattedData = speakerService.formatSpeakerData(newSpeaker);
      const createdSpeaker = await speakerService.createSpeaker(formattedData);
      console.log('✅ Speaker created successfully:', createdSpeaker);

      // Add the new speaker to the list
      setSpeakers(prev => [...prev, createdSpeaker]);

      // Reset form
      setNewSpeaker({
        name: '',
        title: '',
        bio: '',
        profile_image_url: '',
        company: '',
        website: '',
        linkedin_url: '',
        twitter_url: '',
        email: '',
        phone: '',
        expertise_areas: [],
        is_featured: false
      });
      setIsCreating(false);
    } catch (err) {
      console.error('❌ Failed to create speaker:', err);
      setError(err.message);
    }
  };

  const handleUpdateSpeaker = async (speakerId, updatedData) => {
    try {
      setError(null);
      console.log('🔄 Updating speaker:', { speakerId, updatedData });

      const validation = speakerService.validateSpeakerData(updatedData);
      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        return;
      }

      const formattedData = speakerService.formatSpeakerData(updatedData);
      const updatedSpeaker = await speakerService.updateSpeaker(speakerId, formattedData);
      console.log('✅ Speaker updated successfully:', updatedSpeaker);

      setSpeakers(prev => prev.map(speaker =>
        speaker.id === speakerId ? updatedSpeaker : speaker
      ));
      setEditingSpeaker(null);
    } catch (err) {
      console.error('❌ Failed to update speaker:', err);
      setError(err.message);
    }
  };

  const handleDeleteSpeaker = async (speakerId) => {
    if (!window.confirm('Are you sure you want to delete this speaker?')) {
      return;
    }

    try {
      setError(null);
      console.log('🔄 Deleting speaker:', speakerId);

      await speakerService.deleteSpeaker(speakerId);
      console.log('✅ Speaker deleted successfully');

      setSpeakers(prev => prev.filter(speaker => speaker.id !== speakerId));

      // Remove from selected speakers if it was selected
      const updatedSelected = selectedSpeakers.filter(id => id !== speakerId);
      onSpeakersChange(updatedSelected);
    } catch (err) {
      console.error('❌ Failed to delete speaker:', err);
      setError(err.message);
    }
  };

  const handleSpeakerToggle = (speakerId) => {
    const isSelected = selectedSpeakers.includes(speakerId);
    const updatedSelected = isSelected
      ? selectedSpeakers.filter(id => id !== speakerId)
      : [...selectedSpeakers, speakerId];

    onSpeakersChange(updatedSelected);
  };

  const handleRemoveSpeaker = (speakerId) => {
    const updatedSelected = selectedSpeakers.filter(id => id !== speakerId);
    onSpeakersChange(updatedSelected);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleExpertiseChange = (expertise, isSelected, isEditing = false) => {
    const target = isEditing ? editingSpeaker : newSpeaker;
    const setter = isEditing ? setEditingSpeaker : setNewSpeaker;
    
    let updatedAreas;
    if (isSelected) {
      updatedAreas = [...target.expertise_areas, expertise];
    } else {
      updatedAreas = target.expertise_areas.filter(area => area !== expertise);
    }
    
    setter(prev => ({ ...prev, expertise_areas: updatedAreas }));
  };

  const SpeakerForm = ({ speaker, isEditing, onSave, onCancel }) => (
    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            value={speaker.name}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, name: e.target.value })
              : setNewSpeaker({ ...speaker, name: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Dr. Jane Smith"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Title/Position *
          </label>
          <input
            type="text"
            value={speaker.title}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, title: e.target.value })
              : setNewSpeaker({ ...speaker, title: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Chief Technology Officer"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Company/Organization
          </label>
          <input
            type="text"
            value={speaker.company}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, company: e.target.value })
              : setNewSpeaker({ ...speaker, company: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="e.g., Tech Corp Inc."
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            value={speaker.email}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, email: e.target.value })
              : setNewSpeaker({ ...speaker, email: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone
          </label>
          <input
            type="tel"
            value={speaker.phone}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, phone: e.target.value })
              : setNewSpeaker({ ...speaker, phone: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="+****************"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website
          </label>
          <input
            type="url"
            value={speaker.website}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, website: e.target.value })
              : setNewSpeaker({ ...speaker, website: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://example.com"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            LinkedIn URL
          </label>
          <input
            type="url"
            value={speaker.linkedin_url}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, linkedin_url: e.target.value })
              : setNewSpeaker({ ...speaker, linkedin_url: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://linkedin.com/in/username"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Twitter URL
          </label>
          <input
            type="url"
            value={speaker.twitter_url}
            onChange={(e) => isEditing 
              ? setEditingSpeaker({ ...speaker, twitter_url: e.target.value })
              : setNewSpeaker({ ...speaker, twitter_url: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://twitter.com/username"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Profile Image URL
        </label>
        <input
          type="url"
          value={speaker.profile_image_url}
          onChange={(e) => isEditing 
            ? setEditingSpeaker({ ...speaker, profile_image_url: e.target.value })
            : setNewSpeaker({ ...speaker, profile_image_url: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="https://example.com/image.jpg"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Bio/Description
        </label>
        <textarea
          value={speaker.bio}
          onChange={(e) => isEditing 
            ? setEditingSpeaker({ ...speaker, bio: e.target.value })
            : setNewSpeaker({ ...speaker, bio: e.target.value })
          }
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Brief biography and background..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Expertise Areas
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {expertiseOptions.map((expertise) => (
            <label key={expertise} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={speaker.expertise_areas.includes(expertise)}
                onChange={(e) => handleExpertiseChange(expertise, e.target.checked, isEditing)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{expertise}</span>
            </label>
          ))}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id={`featured-${isEditing ? 'edit' : 'new'}`}
          checked={speaker.is_featured}
          onChange={(e) => isEditing 
            ? setEditingSpeaker({ ...speaker, is_featured: e.target.checked })
            : setNewSpeaker({ ...speaker, is_featured: e.target.checked })
          }
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor={`featured-${isEditing ? 'edit' : 'new'}`} className="text-sm font-medium text-gray-700">
          Featured Speaker
        </label>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={onSave}
          className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
        >
          {isEditing ? 'Update' : 'Create'} Speaker
        </button>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Event Speakers</h3>
          <p className="text-sm text-gray-500">
            {selectedSpeakers.length} speaker{selectedSpeakers.length !== 1 ? 's' : ''} selected
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={loadSpeakers}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => setIsCreating(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Add Speaker
          </button>
        </div>
      </div>

      {error && <ErrorMessage message={error} />}

      {/* Multi-Select Dropdown for Speaker Selection */}
      <div className="relative" ref={dropdownRef}>
        {/* Selected Speakers Display */}
        <div
          onClick={toggleDropdown}
          className="min-h-[42px] w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer hover:border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap gap-1 flex-1">
              {selectedSpeakers.length === 0 ? (
                <span className="text-gray-500">Select speakers for this event...</span>
              ) : (
                selectedSpeakers.map((speakerId) => {
                  const speaker = speakers.find(s => s.id === speakerId);
                  return speaker ? (
                    <span
                      key={speaker.id}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {speaker.name}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveSpeaker(speaker.id);
                        }}
                        className="ml-1 hover:text-blue-600"
                      >
                        <FiX className="w-3 h-3" />
                      </button>
                    </span>
                  ) : null;
                })
              )}
            </div>
            <FiChevronDown
              className={`w-4 h-4 text-gray-400 transition-transform ${
                isDropdownOpen ? 'transform rotate-180' : ''
              }`}
            />
          </div>
        </div>

        {/* Dropdown Content */}
        {isDropdownOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
            {/* Search */}
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search speakers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Loading */}
            {loading && (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="md" />
              </div>
            )}

            {/* Error */}
            {error && (
              <div className="p-3">
                <ErrorMessage message={error} />
              </div>
            )}

            {/* Speakers List */}
            {!loading && (
              <div className="max-h-60 overflow-y-auto">
                {speakers.filter(speaker =>
                  !searchTerm ||
                  speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  speaker.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  speaker.company?.toLowerCase().includes(searchTerm.toLowerCase())
                ).length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <FiUser className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-sm">No speakers found</p>
                  </div>
                ) : (
                  speakers
                    .filter(speaker =>
                      !searchTerm ||
                      speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      speaker.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      speaker.company?.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                    .map((speaker) => {
                      const isSelected = selectedSpeakers.includes(speaker.id);

                      return (
                        <div
                          key={speaker.id}
                          onClick={() => handleSpeakerToggle(speaker.id)}
                          className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                            isSelected ? 'bg-blue-50' : ''
                          }`}
                        >
                          <div className="flex items-center flex-1">
                            {speaker.profile_image_url ? (
                              <img
                                src={speaker.profile_image_url}
                                alt={speaker.name}
                                className="w-8 h-8 rounded-full object-cover mr-3"
                              />
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                <FiUser className="w-4 h-4 text-gray-400" />
                              </div>
                            )}
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <p className="text-sm font-medium text-gray-900">
                                  {speaker.name}
                                </p>
                                {speaker.is_featured && (
                                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Featured
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-blue-600">{speaker.title}</p>
                              {speaker.company && (
                                <p className="text-xs text-gray-500">{speaker.company}</p>
                              )}
                              {speaker.expertise_areas?.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {speaker.expertise_areas.slice(0, 2).map((area) => (
                                    <span
                                      key={area}
                                      className="px-1 py-0.5 rounded text-xs bg-green-100 text-green-800"
                                    >
                                      {area}
                                    </span>
                                  ))}
                                  {speaker.expertise_areas.length > 2 && (
                                    <span className="px-1 py-0.5 rounded text-xs bg-gray-100 text-gray-800">
                                      +{speaker.expertise_areas.length - 2}
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                          {isSelected && (
                            <FiCheck className="w-4 h-4 text-blue-600 ml-2" />
                          )}
                        </div>
                      );
                    })
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create Speaker Form */}
      {isCreating && (
        <SpeakerForm
          speaker={newSpeaker}
          isEditing={false}
          onSave={handleCreateSpeaker}
          onCancel={() => setIsCreating(false)}
        />
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      )}

      {/* Speaker Management Cards - Only show when editing */}
      {!loading && speakers.length > 0 && editingSpeaker && (
        <div className="speaker-card-grid">
          <div className="speaker-card bg-white border border-gray-200 rounded-lg p-4">
            <SpeakerForm
              speaker={editingSpeaker}
              isEditing={true}
              onSave={() => handleUpdateSpeaker(editingSpeaker.id, editingSpeaker)}
              onCancel={() => setEditingSpeaker(null)}
            />
          </div>
        </div>
      )}

      {/* Speaker Management Actions */}
      {!loading && speakers.length > 0 && !editingSpeaker && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Manage Speakers</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {speakers.map((speaker) => (
              <div key={speaker.id} className="flex items-center justify-between p-2 bg-white rounded border">
                <div className="flex items-center space-x-2">
                  {speaker.profile_image_url ? (
                    <img
                      src={speaker.profile_image_url}
                      alt={speaker.name}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                      <FiUser className="w-3 h-3 text-gray-400" />
                    </div>
                  )}
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {speaker.name}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setEditingSpeaker(speaker)}
                    className="p-1 text-gray-400 hover:text-blue-600 rounded"
                    title="Edit speaker"
                  >
                    <FiEdit2 className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleDeleteSpeaker(speaker.id)}
                    className="p-1 text-gray-400 hover:text-red-600 rounded"
                    title="Delete speaker"
                  >
                    <FiTrash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!loading && speakers.length === 0 && !error && (
        <div className="text-center py-8">
          <FiUser className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No speakers available</p>
          <p className="text-sm text-gray-400 mb-4">Create your first speaker to get started</p>
          <button
            onClick={() => setIsCreating(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create First Speaker
          </button>
        </div>
      )}

      {selectedSpeakers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            <strong>{selectedSpeakers.length}</strong> speaker{selectedSpeakers.length !== 1 ? 's' : ''} selected for this event
          </p>
        </div>
      )}
    </div>
  );
};

export default SpeakerManager;
