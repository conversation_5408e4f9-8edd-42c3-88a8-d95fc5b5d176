import { useParams, useNavigate, useLocation } from "react-router-dom";

// Components
import ExamLoadingState from "../../components/exam/student/ExamLoadingState";
import ExamAttemptManager from "../../components/exam/student/ExamAttemptManager";
import SimpleExamAttemptManager from "../../components/exam/student/SimpleExamAttemptManager";
import DirectExamInterface from "../../components/exam/student/DirectExamInterface";

/**
 * Student Take Exam Page
 * Entry point for students to take exams - delegates to ExamAttemptManager
 */
function StudentTakeExam() {
  const { examId: paramExamId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // Extract exam ID from URL path (similar to other components in DashboardLayout)
  const pathParts = location.pathname.split('/');
  const examIdFromPath = pathParts[pathParts.length - 1];

  // Get exam ID from params, location state, or URL path
  const examId = paramExamId || location.state?.examId || examIdFromPath;
  const competitionMode = location.state?.competitionMode || false;
  const competitionEvent = location.state?.event;

  // Validate exam ID
  if (!examId) {
    return (
      <ExamLoadingState
        isLoading={false}
        error="No exam ID provided. Please access this page through the exam list."
        examId={null}
        onRetry={() => window.location.reload()}
        onBackToExams={() => navigate('/student/exams')}
      />
    );
  }

  // Note: ExamAttemptManager will handle loading exam data through the proper WebSocket API flow
  // No need to pre-load exam info here since the proper flow is:
  // 1. Request session ID via POST /exam-session/request/{exam_id}
  // 2. Connect to WebSocket
  // 3. Get exam data via GET /attempt/{session_id}

  // Check if exam data is already provided in location state or URL params
  const examDataFromState = location.state?.examData;

  console.log('🚀 [StudentTakeExam] Exam data from state:', examDataFromState);
  console.log('🚀 [StudentTakeExam] Location state:', location.state);

  // TEMPORARY: For debugging, let's create a simple direct exam interface
  // This bypasses all the complex session management and WebSocket issues

  return (
    <SimpleExamAttemptManager
      examId={examId}
      examData={examDataFromState}
      skipAPICall={!!examDataFromState?.session_id} // Skip API if we have session data
      competitionMode={competitionMode}
      competitionEvent={competitionEvent}
      onBackToExams={() => navigate('/student/exams')}
    />
  );
}

export default StudentTakeExam;
