import React, { useState, useEffect, useCallback, useMemo, createContext, useContext } from 'react';

// Create a simple context with default values
const ThemeContext = createContext({
  currentTheme: 'light',
  changeCurrentTheme: () => {},
});

// Simple Theme Provider Component - Simplified for debugging
function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');

  // Initialize theme from localStorage on mount
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        setTheme(savedTheme);
      }
    } catch (error) {
      console.warn('Could not access localStorage:', error);
    }
  }, []);

  // Function to change theme
  const changeCurrentTheme = useCallback((newTheme) => {
    setTheme(newTheme);
    try {
      localStorage.setItem('theme', newTheme);
    } catch (error) {
      console.warn('Could not save theme to localStorage:', error);
    }
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    if (theme === 'dark') {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
    } else {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
    }
  }, [theme]);

  const value = useMemo(() => ({
    currentTheme: theme,
    changeCurrentTheme
  }), [theme, changeCurrentTheme]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Hook to use theme context
function useThemeProvider() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeProvider must be used within a ThemeProvider');
  }
  return context;
}

export { useThemeProvider };
export default ThemeProvider;