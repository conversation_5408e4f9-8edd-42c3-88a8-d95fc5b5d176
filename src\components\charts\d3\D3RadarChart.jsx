import React, { useCallback } from 'react';
import * as d3 from 'd3';
import BaseD3<PERSON><PERSON> from './BaseD3Chart';

/**
 * D3 Radar Chart Component
 * Interactive radar/spider chart for multi-dimensional data
 */
const D3RadarChart = ({
  data = [],
  width = 400,
  height = 400,
  margin = { top: 50, right: 50, bottom: 50, left: 50 },
  levels = 5,
  maxValue,
  labelFactor = 1.25,
  wrapWidth = 60,
  opacityArea = 0.35,
  dotRadius = 4,
  opacityCircles = 0.1,
  strokeWidth = 2,
  roundStrokes = false,
  color,
  animate = true,
  showLabels = true,
  showLegend = false,
  onPointHover,
  onPointClick,
  className = '',
  ...props
}) => {
  const renderChart = useCallback(({
    svg,
    g,
    data,
    colors,
    createTooltip,
    innerWidth,
    innerHeight
  }) => {
    // Calculate radius
    const radius = Math.min(innerWidth, innerHeight) / 2;

    // Center the chart
    g.attr('transform', `translate(${innerWidth/2},${innerHeight/2})`);

    // Prepare data - assume data is array of objects with axis names as keys
    const allAxes = Object.keys(data[0]).filter(key => key !== 'name' && key !== 'color');
    const total = allAxes.length;
    const angleSlice = Math.PI * 2 / total;

    // Calculate max value
    const maxVal = maxValue || d3.max(data, d => d3.max(allAxes, axis => d[axis]));

    // Create radial scale
    const rScale = d3.scaleLinear()
      .range([0, radius])
      .domain([0, maxVal]);

    // Create tooltip
    const tooltip = createTooltip();

    // Draw the circular grid
    const axisGrid = g.append('g').attr('class', 'axisWrapper');

    // Draw the background circles
    axisGrid.selectAll('.levels')
      .data(d3.range(1, levels + 1).reverse())
      .enter()
      .append('circle')
      .attr('class', 'gridCircle')
      .attr('r', d => radius / levels * d)
      .style('fill', colors.grid)
      .style('stroke', colors.border)
      .style('fill-opacity', opacityCircles)
      .style('filter', 'url(#glow)');

    // Add level labels
    axisGrid.selectAll('.axisLabel')
      .data(d3.range(1, levels + 1).reverse())
      .enter()
      .append('text')
      .attr('class', 'axisLabel')
      .attr('x', 4)
      .attr('y', d => -d * radius / levels)
      .attr('dy', '0.4em')
      .style('font-size', '10px')
      .style('fill', colors.textSecondary)
      .text(d => (maxVal * d / levels).toFixed(0));

    // Draw the axes
    const axis = axisGrid.selectAll('.axis')
      .data(allAxes)
      .enter()
      .append('g')
      .attr('class', 'axis');

    // Append the lines
    axis.append('line')
      .attr('x1', 0)
      .attr('y1', 0)
      .attr('x2', (d, i) => rScale(maxVal * 1.1) * Math.cos(angleSlice * i - Math.PI / 2))
      .attr('y2', (d, i) => rScale(maxVal * 1.1) * Math.sin(angleSlice * i - Math.PI / 2))
      .attr('class', 'line')
      .style('stroke', colors.border)
      .style('stroke-width', '2px');

    // Append the labels at each axis
    if (showLabels) {
      axis.append('text')
        .attr('class', 'legend')
        .style('font-size', '11px')
        .style('fill', colors.text)
        .attr('text-anchor', 'middle')
        .attr('dy', '0.35em')
        .attr('x', (d, i) => rScale(maxVal * labelFactor) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('y', (d, i) => rScale(maxVal * labelFactor) * Math.sin(angleSlice * i - Math.PI / 2))
        .text(d => d)
        .call(wrap, wrapWidth);
    }

    // Line radial generator
    const radarLine = d3.lineRadial()
      .radius(d => rScale(d.value))
      .angle(d => d.angle)
      .curve(roundStrokes ? d3.curveCardinalClosed : d3.curveLinearClosed);

    // Create color scale
    const colorScale = d3.scaleOrdinal()
      .domain(data.map(d => d.name))
      .range(color || [colors.primary, colors.secondary, colors.accent, colors.success, colors.warning]);

    // Draw the radar chart blobs
    const radarWrapper = g.selectAll('.radarWrapper')
      .data(data)
      .enter()
      .append('g')
      .attr('class', 'radarWrapper');

    // Append the backgrounds
    radarWrapper
      .append('path')
      .attr('class', 'radarArea')
      .attr('d', d => {
        const values = allAxes.map((axis, i) => ({
          angle: angleSlice * i,
          value: d[axis]
        }));
        return radarLine(values);
      })
      .style('fill', d => colorScale(d.name))
      .style('fill-opacity', animate ? 0 : opacityArea)
      .on('mouseover', function(event, d) {
        // Dim all other areas
        d3.selectAll('.radarArea')
          .transition().duration(200)
          .style('fill-opacity', 0.1);
        
        // Highlight current area
        d3.select(this)
          .transition().duration(200)
          .style('fill-opacity', 0.7);

        tooltip
          .style('visibility', 'visible')
          .html(`<strong>${d.name}</strong>`);
      })
      .on('mousemove', function(event) {
        tooltip
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px');
      })
      .on('mouseout', function() {
        // Restore all areas
        d3.selectAll('.radarArea')
          .transition().duration(200)
          .style('fill-opacity', opacityArea);

        tooltip.style('visibility', 'hidden');
      });

    // Append the borders
    radarWrapper
      .append('path')
      .attr('class', 'radarStroke')
      .attr('d', d => {
        const values = allAxes.map((axis, i) => ({
          angle: angleSlice * i,
          value: d[axis]
        }));
        return radarLine(values);
      })
      .style('stroke-width', strokeWidth + 'px')
      .style('stroke', d => colorScale(d.name))
      .style('fill', 'none')
      .style('filter', 'url(#glow)');

    // Append the circles
    radarWrapper.selectAll('.radarCircle')
      .data(d => allAxes.map((axis, i) => ({
        axis,
        value: d[axis],
        angle: angleSlice * i,
        name: d.name
      })))
      .enter()
      .append('circle')
      .attr('class', 'radarCircle')
      .attr('r', dotRadius)
      .attr('cx', d => rScale(d.value) * Math.cos(d.angle - Math.PI / 2))
      .attr('cy', d => rScale(d.value) * Math.sin(d.angle - Math.PI / 2))
      .style('fill', d => colorScale(d.name))
      .style('fill-opacity', 0.8)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        tooltip
          .style('visibility', 'visible')
          .html(`
            <div>
              <strong>${d.name}</strong><br/>
              <strong>${d.axis}: ${d.value}</strong>
            </div>
          `);

        if (onPointHover) {
          onPointHover(d, event);
        }
      })
      .on('mousemove', function(event) {
        tooltip
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px');
      })
      .on('mouseout', function() {
        tooltip.style('visibility', 'hidden');
      })
      .on('click', function(event, d) {
        if (onPointClick) {
          onPointClick(d, event);
        }
      });

    // Animate if enabled
    if (animate) {
      radarWrapper.selectAll('.radarArea')
        .transition()
        .duration(1000)
        .style('fill-opacity', opacityArea);

      radarWrapper.selectAll('.radarCircle')
        .style('opacity', 0)
        .transition()
        .delay((d, i) => i * 50)
        .duration(500)
        .style('opacity', 1);
    }

    // Text wrapping function
    function wrap(text, width) {
      text.each(function() {
        const text = d3.select(this);
        const words = text.text().split(/\s+/).reverse();
        let word;
        let line = [];
        let lineNumber = 0;
        const lineHeight = 1.4;
        const y = text.attr('y');
        const dy = parseFloat(text.attr('dy'));
        let tspan = text.text(null).append('tspan').attr('x', 0).attr('y', y).attr('dy', dy + 'em');
        
        while (word = words.pop()) {
          line.push(word);
          tspan.text(line.join(' '));
          if (tspan.node().getComputedTextLength() > width) {
            line.pop();
            tspan.text(line.join(' '));
            line = [word];
            tspan = text.append('tspan').attr('x', 0).attr('y', y).attr('dy', ++lineNumber * lineHeight + dy + 'em').text(word);
          }
        }
      });
    }

  }, [
    levels, maxValue, labelFactor, wrapWidth, opacityArea, dotRadius,
    opacityCircles, strokeWidth, roundStrokes, color, animate, showLabels,
    onPointHover, onPointClick
  ]);

  return (
    <BaseD3Chart
      data={data}
      width={width}
      height={height}
      margin={margin}
      onRender={renderChart}
      className={`d3-radar-chart ${className}`}
      {...props}
    />
  );
};

export default D3RadarChart;
