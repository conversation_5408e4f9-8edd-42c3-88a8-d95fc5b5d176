/**
 * Student Dashboard Service
 * 
 * Handles all student dashboard API endpoints including:
 * - Complete dashboard data
 * - Summary statistics
 * - Quick actions
 * - Performance metrics
 * - Schedule data
 */

import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';
import logger from '../utils/helpers/logger';

class StudentDashboardService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/api/student/dashboard`;
  }

  /**
   * Get auth headers for API requests
   */
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get complete student dashboard data
   * @returns {Promise<Object>} Complete dashboard data
   */
  async getDashboardData() {
    try {
      logger.info('Fetching complete student dashboard data', {}, 'StudentDashboardService');

      const response = await axios.get(this.baseURL, {
        headers: this.getAuthHeaders()
      });

      if (response.data?.success) {
        logger.info('Successfully fetched dashboard data', {
          dataKeys: Object.keys(response.data.data || {})
        }, 'StudentDashboardService');

        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      logger.error('Failed to fetch dashboard data', {
        error: error.message,
        status: error.response?.status
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get dashboard summary statistics
   * @returns {Promise<Object>} Summary statistics
   */
  async getDashboardSummary() {
    try {
      logger.info('Fetching dashboard summary', {}, 'StudentDashboardService');

      const response = await axios.get(`${this.baseURL}/summary`, {
        headers: this.getAuthHeaders()
      });

      if (response.data?.success) {
        logger.info('Successfully fetched dashboard summary', {}, 'StudentDashboardService');
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      logger.error('Failed to fetch dashboard summary', {
        error: error.message
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get quick actions for student
   * @returns {Promise<Array>} Quick actions list
   */
  async getQuickActions() {
    try {
      logger.info('Fetching quick actions', {}, 'StudentDashboardService');

      const response = await axios.get(`${this.baseURL}/quick-actions`, {
        headers: this.getAuthHeaders()
      });

      if (response.data?.success) {
        logger.info('Successfully fetched quick actions', {
          count: response.data.data?.length || 0
        }, 'StudentDashboardService');
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      logger.error('Failed to fetch quick actions', {
        error: error.message
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get performance metrics and analytics
   * @returns {Promise<Object>} Performance data
   */
  async getPerformanceMetrics() {
    try {
      logger.info('Fetching performance metrics', {}, 'StudentDashboardService');

      const response = await axios.get(`${this.baseURL}/performance`, {
        headers: this.getAuthHeaders()
      });

      if (response.data?.success) {
        logger.info('Successfully fetched performance metrics', {}, 'StudentDashboardService');
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      logger.error('Failed to fetch performance metrics', {
        error: error.message
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get student schedule
   * @returns {Promise<Array>} Schedule items
   */
  async getSchedule() {
    try {
      logger.info('Fetching student schedule', {}, 'StudentDashboardService');

      const response = await axios.get(`${this.baseURL}/schedule`, {
        headers: this.getAuthHeaders()
      });

      if (response.data?.success) {
        logger.info('Successfully fetched schedule', {
          count: response.data.data?.length || 0
        }, 'StudentDashboardService');
        return response.data.data;
      }

      throw new Error('Invalid response format');
    } catch (error) {
      logger.error('Failed to fetch schedule', {
        error: error.message
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Refresh all dashboard data
   * @returns {Promise<Object>} Complete refreshed data
   */
  async refreshAllData() {
    try {
      logger.info('Refreshing all dashboard data', {}, 'StudentDashboardService');
      
      const [
        dashboardData,
        summary,
        quickActions,
        performance,
        schedule
      ] = await Promise.allSettled([
        this.getDashboardData(),
        this.getDashboardSummary(),
        this.getQuickActions(),
        this.getPerformanceMetrics(),
        this.getSchedule()
      ]);

      const result = {
        dashboardData: dashboardData.status === 'fulfilled' ? dashboardData.value : null,
        summary: summary.status === 'fulfilled' ? summary.value : null,
        quickActions: quickActions.status === 'fulfilled' ? quickActions.value : [],
        performance: performance.status === 'fulfilled' ? performance.value : null,
        schedule: schedule.status === 'fulfilled' ? schedule.value : [],
        errors: []
      };

      // Collect any errors
      [dashboardData, summary, quickActions, performance, schedule].forEach((result, index) => {
        if (result.status === 'rejected') {
          const endpoints = ['dashboard', 'summary', 'quickActions', 'performance', 'schedule'];
          result.errors.push({
            endpoint: endpoints[index],
            error: result.reason?.message || 'Unknown error'
          });
        }
      });

      logger.info('Dashboard data refresh completed', { 
        hasErrors: result.errors.length > 0,
        errorCount: result.errors.length
      }, 'StudentDashboardService');

      return result;
    } catch (error) {
      logger.error('Failed to refresh dashboard data', { 
        error: error.message 
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get analytics summary for performance tracking
   * @returns {Promise<Object>} Analytics data
   */
  async getAnalyticsSummary() {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.analytics_summary || null;
    } catch (error) {
      logger.error('Failed to fetch analytics summary', { 
        error: error.message 
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get personalized recommendations
   * @returns {Promise<Array>} Recommendations list
   */
  async getRecommendations() {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.personalized_recommendations || [];
    } catch (error) {
      logger.error('Failed to fetch recommendations', { 
        error: error.message 
      }, 'StudentDashboardService');
      throw error;
    }
  }

  /**
   * Get upcoming competitions
   * @returns {Promise<Array>} Competitions list
   */
  async getUpcomingCompetitions() {
    try {
      const dashboardData = await this.getDashboardData();
      return dashboardData.upcoming_competitions || [];
    } catch (error) {
      logger.error('Failed to fetch upcoming competitions', { 
        error: error.message 
      }, 'StudentDashboardService');
      throw error;
    }
  }
}

// Export singleton instance
const studentDashboardService = new StudentDashboardService();
export default studentDashboardService;
