import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Social Follow Service
 * Handles all social follow-related API operations
 */
class SocialFollowService {
  constructor() {
    this.baseUrl = API_BASE_URL;
    this.apiPrefix = '/api/social/follow';
    this.rateLimitDelay = 1000; // 1 second between requests
    this.lastRequestTime = 0;
  }

  /**
   * Get auth headers for API requests with security headers
   */
  getAuthHeaders() {
    const token = getAuthToken();
    if (!token) {
      throw new Error('Authentication token not found');
    }
    
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest', // CSRF protection
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache'
    };
  }

  /**
   * Validate UUID format
   */
  validateUUID(id) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  /**
   * Handle API errors consistently
   */
  handleApiError(error) {
    console.error('Social Follow API Error:', error);
    
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 422) {
        const validationErrors = data.detail || [];
        const errorMessages = validationErrors.map(err => err.msg).join(', ');
        throw new Error(`Validation Error: ${errorMessages}`);
      }
      
      if (status === 401) {
        throw new Error('Authentication required. Please log in.');
      }
      
      if (status === 403) {
        throw new Error('You do not have permission to perform this action.');
      }
      
      if (status === 404) {
        throw new Error('User not found.');
      }
      
      throw new Error(data.message || `API Error: ${status}`);
    }
    
    if (error.request) {
      throw new Error('Network error. Please check your connection.');
    }
    
    throw new Error(error.message || 'An unexpected error occurred.');
  }

  /**
   * Follow a user
   * POST /api/social/follow/follow
   */
  async followUser(followingId) {
    try {
      // SECURITY: Validate user ID format
      if (!followingId || !this.validateUUID(followingId)) {
        throw new Error('Invalid user ID format');
      }

      console.log('Following user:', followingId);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/follow`,
        { following_id: followingId },
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Follow response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to follow user:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Unfollow a user
   * DELETE /api/social/follow/unfollow/{user_id}
   */
  async unfollowUser(userId) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      console.log('Unfollowing user:', userId);
      
      const response = await axios.delete(
        `${this.baseUrl}${this.apiPrefix}/unfollow/${userId}`,
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Unfollow response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to unfollow user:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get follow status between current user and another user
   * GET /api/social/follow/status/{user_id}
   */
  async getFollowStatus(userId) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      console.log('Getting follow status for user:', userId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/status/${userId}`,
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Follow status response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get follow status:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get user followers
   * GET /api/social/follow/followers/{user_id}
   */
  async getUserFollowers(userId, page = 1, pageSize = 20) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      // SECURITY: Validate pagination parameters
      const validPage = Math.max(1, parseInt(page) || 1);
      const validPageSize = Math.min(100, Math.max(1, parseInt(pageSize) || 20));

      console.log('Getting followers for user:', userId, { page: validPage, pageSize: validPageSize });
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/followers/${userId}`,
        { 
          headers: this.getAuthHeaders(),
          params: { page: validPage, page_size: validPageSize }
        }
      );
      
      console.log('Followers response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get user followers:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get users that the specified user is following
   * GET /api/social/follow/following/{user_id}
   */
  async getUserFollowing(userId, page = 1, pageSize = 20) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      // SECURITY: Validate pagination parameters
      const validPage = Math.max(1, parseInt(page) || 1);
      const validPageSize = Math.min(100, Math.max(1, parseInt(pageSize) || 20));

      console.log('Getting following for user:', userId, { page: validPage, pageSize: validPageSize });
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/following/${userId}`,
        { 
          headers: this.getAuthHeaders(),
          params: { page: validPage, page_size: validPageSize }
        }
      );
      
      console.log('Following response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get user following:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get mutual followers between current user and specified user
   * GET /api/social/follow/mutual/{user_id}
   */
  async getMutualFollowers(userId, page = 1, pageSize = 20) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      // SECURITY: Validate pagination parameters
      const validPage = Math.max(1, parseInt(page) || 1);
      const validPageSize = Math.min(100, Math.max(1, parseInt(pageSize) || 20));

      console.log('Getting mutual followers for user:', userId, { page: validPage, pageSize: validPageSize });
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/mutual/${userId}`,
        { 
          headers: this.getAuthHeaders(),
          params: { page: validPage, page_size: validPageSize }
        }
      );
      
      console.log('Mutual followers response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get mutual followers:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get follow statistics for a user
   * GET /api/social/follow/stats/{user_id}
   */
  async getFollowStats(userId) {
    try {
      // SECURITY: Validate user ID format
      if (!userId || !this.validateUUID(userId)) {
        throw new Error('Invalid user ID format');
      }

      console.log('Getting follow stats for user:', userId);
      
      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/stats/${userId}`,
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Follow stats response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get follow stats:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get follow suggestions for the current user
   * GET /api/social/follow/discover
   */
  async getFollowSuggestions(limit = 10, skip = 0) {
    try {
      // SECURITY: Validate parameters
      const validLimit = Math.min(100, Math.max(1, parseInt(limit) || 10));
      const validSkip = Math.max(0, parseInt(skip) || 0);

      console.log('Getting discoverable users with limit:', validLimit, 'skip:', validSkip);

      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/discover`,
        {
          headers: this.getAuthHeaders(),
          params: { limit: validLimit, skip: validSkip }
        }
      );

      console.log('Discoverable users response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to get discoverable users:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Search for users (using discover endpoint with filtering)
   * @param {string} searchTerm - Search term for username/email
   * @param {string} userType - Filter by user type (optional)
   * @param {number} limit - Number of results to return
   * @param {number} skip - Number of results to skip
   * @returns {Promise<Array>} Array of matching users
   */
  async searchUsers(searchTerm = '', userType = 'all', limit = 20, skip = 0) {
    try {
      // SECURITY: Validate parameters
      const validLimit = Math.min(100, Math.max(1, parseInt(limit) || 20));
      const validSkip = Math.max(0, parseInt(skip) || 0);
      const sanitizedSearchTerm = searchTerm.trim().substring(0, 100); // Limit search term length

      console.log('Searching users:', { searchTerm: sanitizedSearchTerm, userType, limit: validLimit, skip: validSkip });

      // Use the new discover endpoint with search parameters
      const params = {
        limit: validLimit,
        skip: validSkip
      };

      // Add search term if provided
      if (sanitizedSearchTerm) {
        params.search = sanitizedSearchTerm;
      }

      // Add user type filter if provided
      if (userType) {
        params.user_type = userType;
      }

      const response = await axios.get(
        `${this.baseUrl}${this.apiPrefix}/discover`,
        {
          headers: this.getAuthHeaders(),
          params
        }
      );

      const users = response.data || [];

      console.log('Search results:', users);
      return users;
    } catch (error) {
      console.error('Failed to search users:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Follow multiple users at once
   * POST /api/social/follow/bulk-follow
   */
  async bulkFollowUsers(userIds) {
    try {
      // SECURITY: Validate input
      if (!Array.isArray(userIds) || userIds.length === 0) {
        throw new Error('User IDs must be a non-empty array');
      }

      if (userIds.length > 50) {
        throw new Error('Cannot follow more than 50 users at once');
      }

      // SECURITY: Validate all user IDs
      const validUserIds = userIds.filter(id => this.validateUUID(id));
      if (validUserIds.length !== userIds.length) {
        throw new Error('One or more user IDs have invalid format');
      }

      console.log('Bulk following users:', validUserIds);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/bulk-follow`,
        { user_ids: validUserIds },
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Bulk follow response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to bulk follow users:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Unfollow multiple users at once
   * POST /api/social/follow/bulk-unfollow
   */
  async bulkUnfollowUsers(userIds) {
    try {
      // SECURITY: Validate input
      if (!Array.isArray(userIds) || userIds.length === 0) {
        throw new Error('User IDs must be a non-empty array');
      }

      if (userIds.length > 50) {
        throw new Error('Cannot unfollow more than 50 users at once');
      }

      // SECURITY: Validate all user IDs
      const validUserIds = userIds.filter(id => this.validateUUID(id));
      if (validUserIds.length !== userIds.length) {
        throw new Error('One or more user IDs have invalid format');
      }

      console.log('Bulk unfollowing users:', validUserIds);
      
      const response = await axios.post(
        `${this.baseUrl}${this.apiPrefix}/bulk-unfollow`,
        { user_ids: validUserIds },
        { headers: this.getAuthHeaders() }
      );
      
      console.log('Bulk unfollow response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to bulk unfollow users:', error);
      throw this.handleApiError(error);
    }
  }
}

// Export singleton instance
export default new SocialFollowService();
