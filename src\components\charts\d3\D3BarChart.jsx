import React, { useCallback } from 'react';
import * as d3 from 'd3';
import BaseD3<PERSON>hart from './BaseD3Chart';

/**
 * D3 Bar Chart Component
 * Interactive bar chart with hover effects, tooltips, and animations
 * Enhanced with modern styling and responsive design
 */
const D3BarChart = ({
  data = [],
  width = 600,
  height = 400,
  margin = { top: 30, right: 40, bottom: 60, left: 60 },
  xKey = 'x',
  yKey = 'y',
  barColor,
  colorScale,
  horizontal = false,
  animate = true,
  showValues = false,
  xAxisLabel = '',
  yAxisLabel = '',
  xTickFormat,
  yTickFormat,
  onBarHover,
  onBarClick,
  className = '',
  theme = 'light', // Add theme support
  gradient = true, // Add gradient support
  cornerRadius = 4, // Add rounded corners
  ...props
}) => {
  const renderChart = useCallback(({
    svg,
    g,
    data,
    colors,
    createScales,
    createAxes,
    createGrid,
    createTooltip,
    innerWidth,
    innerHeight
  }) => {
    // Prepare data
    const chartData = data.map(d => ({
      x: d[xKey],
      y: d[yKey],
      original: d
    }));

    // Create scales
    const scaleConfig = horizontal ? {
      xType: 'linear',
      yType: 'band',
      xRange: [0, innerWidth],
      yRange: [0, innerHeight]
    } : {
      xType: 'band',
      yType: 'linear',
      xRange: [0, innerWidth],
      yRange: [innerHeight, 0]
    };

    const { xScale, yScale } = createScales(chartData, scaleConfig);

    // Create grid
    createGrid(g, xScale, yScale);

    // Create axes
    createAxes(g, xScale, yScale, {
      xAxisLabel,
      yAxisLabel,
      xTickFormat,
      yTickFormat
    });

    // Create tooltip
    const tooltip = createTooltip();

    // Enhanced color scale for bars with gradients
    let getBarColor;
    if (colorScale) {
      const scale = d3.scaleOrdinal()
        .domain(chartData.map(d => d.x))
        .range(colorScale);
      getBarColor = d => scale(d.x);
    } else {
      // Modern color palette
      const modernColors = [
        '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
        '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
      ];
      const scale = d3.scaleOrdinal()
        .domain(chartData.map(d => d.x))
        .range(modernColors);
      getBarColor = d => barColor || scale(d.x);
    }

    // Create gradient definitions if enabled
    if (gradient) {
      const defs = svg.select('defs').empty() ? svg.append('defs') : svg.select('defs');

      chartData.forEach((d, i) => {
        const baseColor = getBarColor(d);
        const gradientId = `gradient-${i}`;

        const gradient = defs.append('linearGradient')
          .attr('id', gradientId)
          .attr('gradientUnits', 'userSpaceOnUse')
          .attr('x1', 0).attr('y1', 0)
          .attr('x2', 0).attr('y2', 1);

        gradient.append('stop')
          .attr('offset', '0%')
          .attr('stop-color', d3.color(baseColor).brighter(0.3));

        gradient.append('stop')
          .attr('offset', '100%')
          .attr('stop-color', baseColor);
      });
    }

    // Create bars with enhanced styling
    const bars = g.selectAll('.bar')
      .data(chartData)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('rx', cornerRadius)
      .attr('ry', cornerRadius)
      .style('fill', (d, i) => gradient ? `url(#gradient-${i})` : getBarColor(d))
      .style('cursor', 'pointer')
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');

    if (horizontal) {
      // Horizontal bars
      bars
        .attr('x', 0)
        .attr('y', d => yScale(d.x))
        .attr('height', yScale.bandwidth())
        .attr('width', animate ? 0 : d => xScale(d.y));

      if (animate) {
        bars
          .transition()
          .delay((d, i) => i * 100)
          .duration(800)
          .attr('width', d => xScale(d.y));
      }
    } else {
      // Vertical bars
      bars
        .attr('x', d => xScale(d.x))
        .attr('width', xScale.bandwidth())
        .attr('y', animate ? yScale(0) : d => yScale(d.y))
        .attr('height', animate ? 0 : d => innerHeight - yScale(d.y));

      if (animate) {
        bars
          .transition()
          .delay((d, i) => i * 100)
          .duration(800)
          .attr('y', d => yScale(d.y))
          .attr('height', d => innerHeight - yScale(d.y));
      }
    }

    // Enhanced hover effects with modern styling
    bars
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 0.9)
          .style('transform', 'scale(1.02)')
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))');

        tooltip
          .style('visibility', 'visible')
          .style('background', theme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)')
          .style('color', theme === 'dark' ? '#F9FAFB' : '#1F2937')
          .style('border', `1px solid ${theme === 'dark' ? '#374151' : '#E5E7EB'}`)
          .style('border-radius', '8px')
          .style('padding', '12px')
          .style('font-size', '14px')
          .style('box-shadow', '0 10px 25px rgba(0,0,0,0.1)')
          .style('backdrop-filter', 'blur(10px)')
          .html(`
            <div style="font-weight: 600; margin-bottom: 4px;">${d.x}</div>
            <div style="color: ${theme === 'dark' ? '#9CA3AF' : '#6B7280'};">
              ${yAxisLabel || 'Value'}: <span style="font-weight: 600; color: ${getBarColor(d)};">${d.y}</span>
            </div>
          `);

        if (onBarHover) {
          onBarHover(d.original, event);
        }
      })
      .on('mousemove', function(event) {
        tooltip
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 1)
          .style('transform', 'scale(1)')
          .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');

        tooltip.style('visibility', 'hidden');
      })
      .on('click', function(event, d) {
        if (onBarClick) {
          onBarClick(d.original, event);
        }
      });

    // Add value labels if enabled
    if (showValues) {
      const labels = g.selectAll('.value-label')
        .data(chartData)
        .enter()
        .append('text')
        .attr('class', 'value-label')
        .style('fill', colors.text)
        .style('font-size', '12px')
        .style('text-anchor', 'middle')
        .style('pointer-events', 'none');

      if (horizontal) {
        labels
          .attr('x', d => xScale(d.y) + 5)
          .attr('y', d => yScale(d.x) + yScale.bandwidth() / 2)
          .attr('dy', '0.35em')
          .text(d => d.y);
      } else {
        labels
          .attr('x', d => xScale(d.x) + xScale.bandwidth() / 2)
          .attr('y', d => yScale(d.y) - 5)
          .text(d => d.y);
      }

      if (animate) {
        labels
          .style('opacity', 0)
          .transition()
          .delay((d, i) => i * 100 + 400)
          .duration(400)
          .style('opacity', 1);
      }
    }

    // Add average line if data has enough points
    if (chartData.length > 2) {
      const average = d3.mean(chartData, d => d.y);
      
      if (!horizontal) {
        g.append('line')
          .attr('class', 'average-line')
          .attr('x1', 0)
          .attr('x2', innerWidth)
          .attr('y1', yScale(average))
          .attr('y2', yScale(average))
          .style('stroke', colors.accent)
          .style('stroke-width', 2)
          .style('stroke-dasharray', '5,5')
          .style('opacity', 0.7);

        g.append('text')
          .attr('class', 'average-label')
          .attr('x', innerWidth - 5)
          .attr('y', yScale(average) - 5)
          .style('text-anchor', 'end')
          .style('fill', colors.accent)
          .style('font-size', '12px')
          .text(`Avg: ${average.toFixed(1)}`);
      }
    }

  }, [
    xKey, yKey, barColor, colorScale, horizontal, animate, showValues,
    xAxisLabel, yAxisLabel, xTickFormat, yTickFormat,
    onBarHover, onBarClick, theme, gradient, cornerRadius
  ]);

  return (
    <BaseD3Chart
      data={data}
      width={width}
      height={height}
      margin={margin}
      onRender={renderChart}
      className={`d3-bar-chart ${className}`}
      {...props}
    />
  );
};

export default D3BarChart;
