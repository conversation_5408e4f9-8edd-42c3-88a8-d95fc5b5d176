/**
 * Performance Metrics Component
 * 
 * Displays comprehensive student performance analytics including:
 * - Overall grade and trends
 * - Subject-wise performance
 * - Class ranking and percentiles
 * - Recent scores and improvements
 */

import React from 'react';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiAward,
  FiTarget,
  FiBarChart2
} from 'react-icons/fi';
import { Card } from '../ui/layout';

const PerformanceMetrics = ({ performance, studyMetrics, loading = false }) => {
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </Card>
    );
  }

  if (!performance && !studyMetrics) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <FiBarChart2 className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No performance data available</p>
        </div>
      </Card>
    );
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'improving':
        return <FiTrendingUp className="w-4 h-4 text-green-500" />;
      case 'declining':
        return <FiTrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <FiMinus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'improving':
        return 'text-green-600 dark:text-green-400';
      case 'declining':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getGradeColor = (grade) => {
    if (grade >= 90) return 'text-green-600 dark:text-green-400';
    if (grade >= 80) return 'text-blue-600 dark:text-blue-400';
    if (grade >= 70) return 'text-yellow-600 dark:text-yellow-400';
    if (grade >= 60) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <Card className="p-6 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-2 border-gray-100 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <FiBarChart2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Performance Overview
          </h3>
        </div>
        {performance?.improvement_trend && (
          <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100 dark:bg-gray-700">
            {getTrendIcon(performance.improvement_trend)}
            <span className={`text-sm font-medium ${getTrendColor(performance.improvement_trend)}`}>
              {performance.improvement_trend}
            </span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Overall Grade */}
        {performance?.overall_grade !== undefined && (
          <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl border border-blue-200 dark:border-blue-700">
            <div className={`text-3xl font-bold ${getGradeColor(performance.overall_grade)} mb-1`}>
              {performance.overall_grade.toFixed(1)}%
            </div>
            <div className="text-sm font-medium text-blue-600 dark:text-blue-400">Overall Grade</div>
          </div>
        )}

        {/* Class Rank */}
        {performance?.rank_in_class && (
          <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl border border-purple-200 dark:border-purple-700">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1">
              #{performance.rank_in_class}
            </div>
            <div className="text-sm font-medium text-purple-600 dark:text-purple-400">Class Rank</div>
          </div>
        )}

        {/* Percentile */}
        {performance?.grade_percentile && (
          <div className="text-center p-6 bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 rounded-xl border border-indigo-200 dark:border-indigo-700">
            <div className="text-3xl font-bold text-indigo-600 dark:text-indigo-400 mb-1">
              {performance.grade_percentile}th
            </div>
            <div className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Percentile</div>
          </div>
        )}

        {/* Study Level */}
        {studyMetrics?.level && (
          <div className="text-center p-6 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-xl border border-amber-200 dark:border-amber-700">
            <div className="text-3xl font-bold text-amber-600 dark:text-amber-400 flex items-center justify-center gap-2 mb-1">
              <FiAward className="w-7 h-7" />
              {studyMetrics.level}
            </div>
            <div className="text-sm font-medium text-amber-600 dark:text-amber-400">Level</div>
          </div>
        )}
      </div>

      {/* Subject Grades */}
      {performance?.subject_grades && Object.keys(performance.subject_grades).length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
            Subject Performance
          </h4>
          <div className="space-y-3">
            {Object.entries(performance.subject_grades).map(([subject, grade]) => (
              <div key={subject} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {subject}
                </span>
                <div className="flex items-center gap-3">
                  <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        grade >= 90 ? 'bg-green-500' :
                        grade >= 80 ? 'bg-blue-500' :
                        grade >= 70 ? 'bg-yellow-500' :
                        grade >= 60 ? 'bg-orange-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(grade, 100)}%` }}
                    ></div>
                  </div>
                  <span className={`text-sm font-semibold ${getGradeColor(grade)} min-w-[3rem] text-right`}>
                    {grade.toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Study Metrics */}
      {studyMetrics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          {studyMetrics.total_points !== undefined && (
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {studyMetrics.total_points.toLocaleString()}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Points</div>
            </div>
          )}
          
          {studyMetrics.tasks_completed !== undefined && (
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                {studyMetrics.tasks_completed}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Tasks Done</div>
            </div>
          )}
          
          {studyMetrics.exams_taken !== undefined && (
            <div className="text-center">
              <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                {studyMetrics.exams_taken}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Exams</div>
            </div>
          )}
          
          {studyMetrics.badges_earned && studyMetrics.badges_earned.length > 0 && (
            <div className="text-center">
              <div className="text-lg font-semibold text-amber-600 dark:text-amber-400">
                {studyMetrics.badges_earned.length}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Badges</div>
            </div>
          )}
        </div>
      )}

      {/* Strengths and Weaknesses */}
      {(performance?.strongest_subject || performance?.weakest_subject) && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {performance.strongest_subject && (
              <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <FiTarget className="w-5 h-5 text-green-600 dark:text-green-400" />
                <div>
                  <div className="text-sm font-medium text-green-800 dark:text-green-200">
                    Strongest Subject
                  </div>
                  <div className="text-sm text-green-600 dark:text-green-400">
                    {performance.strongest_subject}
                  </div>
                </div>
              </div>
            )}
            
            {performance.weakest_subject && (
              <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <FiTarget className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <div>
                  <div className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Focus Area
                  </div>
                  <div className="text-sm text-orange-600 dark:text-orange-400">
                    {performance.weakest_subject}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </Card>
  );
};

export default PerformanceMetrics;
