import { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchExamById,
  deleteExam,
  updateExam
} from '../../store/slices/ExamSlice';
import { getErrorMessage } from '../../utils/helpers/errorHandler';
import {
  FiArrowLeft,
  FiEdit,
  FiTrash2,
  FiClock,
  FiCalendar,
  FiFileText,
  FiCheckCircle,
  FiAlertCircle,
  FiPlay,
  FiPause,
  FiDownload,
  FiPrinter,
  FiBookOpen,
  FiTarget,
  FiUsers,
  FiHome
} from 'react-icons/fi';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { formatDateTimeSync } from '../../utils/timezone';
import useTimezone from '../../hooks/useTimezone';
import InstituteEnhancedExamDetails from '../../components/exam/InstituteEnhancedExamDetails';

const InstituteExamDetail = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentTheme } = useThemeProvider();
  const { timezoneData, formatDateTime } = useTimezone();

  // Extract exam ID from URL path
  const pathParts = location.pathname.split('/');
  const examId = pathParts[pathParts.length - 1];

  // Redux state
  const {
    currentExam: exam,
    loading,
    error
  } = useSelector((state) => state.exams);

  // Local state
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Theme classes
  const themeClasses = useMemo(() => ({
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    textSecondary: currentTheme === "dark" ? "text-gray-300" : "text-gray-600",
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    button: currentTheme === "dark" ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-600 hover:bg-blue-700"
  }), [currentTheme]);

  // Fetch exam details on component mount
  useEffect(() => {
    if (examId) {
      console.log('🎯 InstituteExamDetail: Fetching exam data for ID:', examId);
      
      // Use the basic fetchExamById which calls /api/exams/{id}
      dispatch(fetchExamById(examId))
        .unwrap()
        .then((result) => {
          console.log('✅ Exam data fetched successfully:', result);
        })
        .catch((error) => {
          console.error('❌ Failed to fetch exam data:', error);
        });
    }
  }, [dispatch, examId]);

  // Get exam status info
  const getStatusInfo = (exam) => {
    if (!exam) return { icon: FiFileText, color: 'gray', label: 'Unknown' };
    
    const now = new Date();
    const startTime = exam.start_time ? new Date(exam.start_time) : null;
    const endTime = exam.end_time ? new Date(exam.end_time) : null;

    if (!startTime || !endTime) {
      return { icon: FiFileText, color: 'gray', label: 'Draft' };
    }

    if (now < startTime) {
      return { icon: FiClock, color: 'blue', label: 'Scheduled' };
    } else if (now >= startTime && now <= endTime) {
      return { icon: FiPlay, color: 'green', label: 'Active' };
    } else {
      return { icon: FiCheckCircle, color: 'gray', label: 'Completed' };
    }
  };

  // Format duration
  const formatDuration = (minutes) => {
    if (!minutes) return 'Not set';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Handlers
  const handleBack = () => {
    navigate('/institute/exams');
  };

  const handleEdit = () => {
    // For institutes, "edit" actually means "copy and create new"
    navigate(`/institute/exam/${examId}/edit`, {
      state: { examData: exam, isEditing: true, isCopying: true }
    });
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this exam? This action cannot be undone.')) {
      return;
    }

    setDeleteLoading(true);
    try {
      await dispatch(deleteExam(examId)).unwrap();
      navigate('/institute/exams');
    } catch (error) {
      console.error('Failed to delete exam:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} p-6`}>
        <div className="max-w-7xl mx-auto">
          <LoadingSpinner size="lg" text="Loading exam details..." />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} p-6`}>
        <div className="max-w-7xl mx-auto">
          <ErrorMessage 
            message={getErrorMessage(error)} 
            onRetry={() => dispatch(fetchExamById(examId))}
          />
        </div>
      </div>
    );
  }

  // No exam found
  if (!exam) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} p-6`}>
        <div className="max-w-7xl mx-auto">
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center`}>
            <FiAlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className={`text-lg font-medium ${themeClasses.text} mb-2`}>Exam Not Found</h3>
            <p className={`${themeClasses.textSecondary} mb-4`}>
              The exam you're looking for doesn't exist or you don't have permission to view it.
            </p>
            <button
              onClick={handleBack}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <FiArrowLeft className="h-4 w-4 mr-2" />
              Back to Exams
            </button>
          </div>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(exam);

  return (
    <div className={`min-h-screen ${themeClasses.bg} p-6`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className={`p-2 rounded-lg ${themeClasses.textSecondary} hover:bg-gray-100 dark:hover:bg-gray-700`}
              >
                <FiArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className={`text-2xl font-bold ${themeClasses.text}`}>{exam.title}</h1>
                <div className="flex items-center space-x-4 mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusInfo.color}-100 text-${statusInfo.color}-800`}>
                    <statusInfo.icon className="h-3 w-3 mr-1" />
                    {statusInfo.label}
                  </span>
                  {exam.class_number && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800`}>
                      <FiHome className="h-3 w-3 mr-1" />
                      Class {exam.class_number}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleEdit}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <FiEdit className="h-4 w-4 mr-2" />
                Copy & Edit
              </button>

              <button
                onClick={handleDelete}
                disabled={deleteLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
              >
                <FiTrash2 className="h-4 w-4 mr-2" />
                {deleteLoading ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>

        {/* Exam Details */}
        <InstituteEnhancedExamDetails exam={exam} className="mb-6" />

        {/* Exam Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Questions */}
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiBookOpen className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className={`text-sm font-medium ${themeClasses.textSecondary}`}>Total Questions</p>
                <p className={`text-2xl font-semibold ${themeClasses.text}`}>
                  {exam.total_questions || exam.questions?.length || 0}
                </p>
              </div>
            </div>
          </div>

          {/* Total Marks */}
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiTarget className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className={`text-sm font-medium ${themeClasses.textSecondary}`}>Total Marks</p>
                <p className={`text-2xl font-semibold ${themeClasses.text}`}>
                  {exam.total_marks || 0}
                </p>
              </div>
            </div>
          </div>

          {/* Duration */}
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiClock className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className={`text-sm font-medium ${themeClasses.textSecondary}`}>Duration</p>
                <p className={`text-2xl font-semibold ${themeClasses.text}`}>
                  {formatDuration(exam.total_duration)}
                </p>
              </div>
            </div>
          </div>


        </div>

        {/* Exam Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>Basic Information</h3>
            <div className="space-y-4">
              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Description</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {exam.description || 'No description provided'}
                </p>
              </div>

              {exam.class_number && (
                <div>
                  <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Class Number</label>
                  <p className={`mt-1 ${themeClasses.text}`}>{exam.class_number}</p>
                </div>
              )}

              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Created</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {exam.created_at ? formatDateTimeSync(exam.created_at) : 'Unknown'}
                </p>
              </div>

              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Last Updated</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {exam.updated_at ? formatDateTimeSync(exam.updated_at) : 'Unknown'}
                </p>
              </div>
            </div>
          </div>

          {/* Schedule Information */}
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>Schedule</h3>
            <div className="space-y-4">
              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Start Time</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {exam.start_time ? formatDateTimeSync(exam.start_time) : 'Not scheduled'}
                </p>
              </div>

              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>End Time</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {exam.end_time ? formatDateTimeSync(exam.end_time) : 'Not scheduled'}
                </p>
              </div>

              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Duration</label>
                <p className={`mt-1 ${themeClasses.text}`}>
                  {formatDuration(exam.total_duration)}
                </p>
              </div>

              <div>
                <label className={`text-sm font-medium ${themeClasses.textSecondary}`}>Status</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusInfo.color}-100 text-${statusInfo.color}-800 mt-1`}>
                  <statusInfo.icon className="h-3 w-3 mr-1" />
                  {statusInfo.label}
                </span>
              </div>
            </div>
          </div>
        </div>



        {/* Questions Overview */}
        {exam.questions && exam.questions.length > 0 && (
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>Questions Overview</h3>

            {/* Question Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className={`text-2xl font-bold text-blue-600`}>
                  {exam.questions.filter(q => q.Type === 'MCQS').length}
                </p>
                <p className={`text-sm ${themeClasses.textSecondary}`}>Multiple Choice</p>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <p className={`text-2xl font-bold text-green-600`}>
                  {exam.questions.filter(q => q.Type === 'SHORT').length}
                </p>
                <p className={`text-sm ${themeClasses.textSecondary}`}>Short Answer</p>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <p className={`text-2xl font-bold text-purple-600`}>
                  {exam.questions.filter(q => q.Type === 'LONG').length}
                </p>
                <p className={`text-sm ${themeClasses.textSecondary}`}>Long Answer</p>
              </div>
            </div>

            {/* Difficulty Distribution */}
            <div className="mb-6">
              <h4 className={`text-md font-medium ${themeClasses.text} mb-3`}>Difficulty Distribution</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <span className={`text-sm font-medium ${themeClasses.text}`}>Easy</span>
                  <span className="text-lg font-bold text-green-600">
                    {exam.questions.filter(q => q.Level === 'EASY').length}
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <span className={`text-sm font-medium ${themeClasses.text}`}>Medium</span>
                  <span className="text-lg font-bold text-yellow-600">
                    {exam.questions.filter(q => q.Level === 'MEDIUM').length}
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <span className={`text-sm font-medium ${themeClasses.text}`}>Hard</span>
                  <span className="text-lg font-bold text-red-600">
                    {exam.questions.filter(q => q.Level === 'HARD').length}
                  </span>
                </div>
              </div>
            </div>

            {/* Subject/Chapter Breakdown */}
            <div className="mb-6">
              <h4 className={`text-md font-medium ${themeClasses.text} mb-3`}>Subject & Chapter Breakdown</h4>
              <div className="space-y-3">
                {/* Group questions by subject and chapter */}
                {Object.entries(
                  exam.questions.reduce((acc, question) => {
                    const subject = question.subject?.name || 'Unknown Subject';
                    const chapter = question.chapter?.name || 'Unknown Chapter';
                    const key = `${subject} - ${chapter}`;
                    if (!acc[key]) {
                      acc[key] = { count: 0, marks: 0, subject, chapter };
                    }
                    acc[key].count++;
                    acc[key].marks += question.marks || 0;
                    return acc;
                  }, {})
                ).map(([key, data]) => (
                  <div key={key} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div>
                      <p className={`font-medium ${themeClasses.text}`}>{data.subject}</p>
                      <p className={`text-sm ${themeClasses.textSecondary}`}>{data.chapter}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${themeClasses.text}`}>{data.count} questions</p>
                      <p className={`text-sm ${themeClasses.textSecondary}`}>{data.marks} marks</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Detailed Questions List */}
        {exam.questions && exam.questions.length > 0 && (
          <div className={`${themeClasses.cardBg} rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
            <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>Questions Details</h3>
            <div className="space-y-6">
              {exam.questions.map((question, index) => (
                <div key={question.id || index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  {/* Question Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium`}>
                        {index + 1}
                      </span>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            question.Type === 'MCQS' ? 'bg-blue-100 text-blue-800' :
                            question.Type === 'SHORT' ? 'bg-green-100 text-green-800' :
                            'bg-purple-100 text-purple-800'
                          }`}>
                            {question.Type}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            question.Level === 'EASY' ? 'bg-green-100 text-green-800' :
                            question.Level === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {question.Level}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800`}>
                            {question.marks} {question.marks === 1 ? 'mark' : 'marks'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Question Text */}
                  <div className="mb-3">
                    <p className={`${themeClasses.text} leading-relaxed`}>
                      {question.text}
                    </p>
                  </div>

                  {/* Question Metadata */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    {question.subject && (
                      <div>
                        <label className={`text-xs font-medium ${themeClasses.textSecondary} uppercase tracking-wide`}>Subject</label>
                        <p className={`text-sm ${themeClasses.text}`}>{question.subject.name}</p>
                      </div>
                    )}
                    {question.chapter && (
                      <div>
                        <label className={`text-xs font-medium ${themeClasses.textSecondary} uppercase tracking-wide`}>Chapter</label>
                        <p className={`text-sm ${themeClasses.text}`}>{question.chapter.name}</p>
                      </div>
                    )}
                    {question.class_ && (
                      <div>
                        <label className={`text-xs font-medium ${themeClasses.textSecondary} uppercase tracking-wide`}>Class</label>
                        <p className={`text-sm ${themeClasses.text}`}>Class {question.class_.ClassNo}</p>
                      </div>
                    )}
                  </div>

                  {/* MCQ Options */}
                  {question.Type === 'MCQS' && question.options && question.options.length > 0 && (
                    <div className="mt-4">
                      <label className={`text-xs font-medium ${themeClasses.textSecondary} uppercase tracking-wide mb-2 block`}>Options</label>
                      <div className="space-y-2">
                        {question.options.map((option, optionIndex) => (
                          <div key={option.id || optionIndex} className={`flex items-center p-2 rounded-md ${
                            option.is_correct
                              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700'
                              : 'bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600'
                          }`}>
                            <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium mr-3 ${
                              option.is_correct
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {String.fromCharCode(65 + optionIndex)}
                            </span>
                            <span className={`${themeClasses.text} ${option.is_correct ? 'font-medium' : ''}`}>
                              {option.option_text}
                            </span>
                            {option.is_correct && (
                              <FiCheckCircle className="h-4 w-4 text-green-600 ml-auto" />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Answer for Short/Long Questions */}
                  {(question.Type === 'SHORT' || question.Type === 'LONG') && question.answer && (
                    <div className="mt-4">
                      <label className={`text-xs font-medium ${themeClasses.textSecondary} uppercase tracking-wide mb-2 block`}>Expected Answer</label>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
                        <p className={`${themeClasses.text} text-sm leading-relaxed whitespace-pre-wrap`}>
                          {question.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstituteExamDetail;
