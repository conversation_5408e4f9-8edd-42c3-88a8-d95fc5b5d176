import React from 'react';
import { FiSave } from 'react-icons/fi';
import { isFormReadyForPublishing, getMissingRequiredFields } from '../../../utils/eventValidation';

const ActionButtons = ({ 
  formData, 
  isSubmitting, 
  onCancel, 
  onSaveDraft, 
  onPublish,
  onShowValidation 
}) => {
  const handlePublishClick = () => {
    if (!isFormReadyForPublishing(formData)) {
      onShowValidation();
      // Scroll to top to show validation summary
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      onPublish();
    }
  };

  return (
    <div className="flex justify-between mt-6">
      <button
        type="button"
        onClick={onCancel}
        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        Cancel
      </button>
      <div className="flex space-x-3">
        <button
          type="button"
          onClick={onSaveDraft}
          disabled={isSubmitting}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          Save as Draft
        </button>
        <button
          type="button"
          onClick={handlePublishClick}
          disabled={isSubmitting}
          className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white ${
            isFormReadyForPublishing(formData)
              ? 'bg-blue-600 hover:bg-blue-700'
              : 'bg-gray-400 hover:bg-gray-500'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
          title={!isFormReadyForPublishing(formData) ? `Missing required fields: ${getMissingRequiredFields(formData).join(', ')}` : 'Publish event'}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating...
            </>
          ) : (
            <>
              <FiSave className="h-4 w-4 mr-2" />
              Publish Event
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ActionButtons;
