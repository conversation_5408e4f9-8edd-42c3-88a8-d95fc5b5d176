import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiCreditCard, 
  FiShield, 
  FiUsers, 
  FiCalendar, 
  FiMapPin,
  FiInfo,
  FiAlertCircle,
  FiCheck,
  FiX
} from 'react-icons/fi';
import { 
  canPurchaseEventTickets, 
  getPaymentAccessMessage,
  getAvailablePaymentMethods,
  validatePaymentAmount,
  getPaymentFlowConfig
} from '../../utils/payment/paymentAccessControl';
import { getCurrentUser } from '../../utils/helpers/authHelpers';
import { useNotification } from '../../contexts/NotificationContext';
import { 
  createEventPayment,
  selectCreatePaymentLoading,
  selectCreatePaymentError,
  selectCreatePaymentSuccess,
  selectCurrentPayment
} from '../../store/slices/PaymentSlice';
import { LoadingSpinner } from '../ui';

const EventTicketPurchase = ({ 
  event, 
  onClose, 
  onSuccess,
  onError 
}) => {
  const dispatch = useDispatch();
  const { showSuccess, showError, showWarning } = useNotification();
  
  // Redux state
  const paymentLoading = useSelector(selectCreatePaymentLoading);
  const paymentError = useSelector(selectCreatePaymentError);
  const paymentSuccess = useSelector(selectCreatePaymentSuccess);
  const currentPayment = useSelector(selectCurrentPayment);

  // Local state
  const [currentUser, setCurrentUser] = useState(null);
  const [paymentAccess, setPaymentAccess] = useState(null);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('demo');
  const [availablePaymentMethods, setAvailablePaymentMethods] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [paymentFlowConfig, setPaymentFlowConfig] = useState(null);

  // Initialize component
  useEffect(() => {
    const user = getCurrentUser();
    setCurrentUser(user);

    if (user && event) {
      // Check payment access
      const access = getPaymentAccessMessage(event, user);
      setPaymentAccess(access);

      // Get available payment methods
      const methods = getAvailablePaymentMethods(user);
      setAvailablePaymentMethods(methods);

      // Get payment flow config
      const flowConfig = getPaymentFlowConfig(user);
      setPaymentFlowConfig(flowConfig);

      // Set default ticket if available
      if (event.tickets && event.tickets.length > 0) {
        setSelectedTicket(event.tickets[0]);
      }
    }
  }, [event]);

  // Calculate total amount when ticket or quantity changes
  useEffect(() => {
    if (selectedTicket && quantity) {
      const total = selectedTicket.price * quantity;
      setTotalAmount(total);
    }
  }, [selectedTicket, quantity]);

  // Handle payment success
  useEffect(() => {
    if (paymentSuccess && currentPayment) {
      showSuccess(`Demo ticket purchase completed successfully!`);
      if (onSuccess) {
        onSuccess(currentPayment);
      }
    }
  }, [paymentSuccess, currentPayment, showSuccess, onSuccess]);

  // Handle payment error
  useEffect(() => {
    if (paymentError) {
      showError(`Payment failed: ${paymentError}`);
      if (onError) {
        onError(paymentError);
      }
    }
  }, [paymentError, showError, onError]);

  const handlePurchase = async () => {
    if (!selectedTicket || !currentUser) {
      showError('Please select a ticket and ensure you are logged in');
      return;
    }

    // Validate payment amount
    const amountValidation = validatePaymentAmount(totalAmount, currentUser);
    if (!amountValidation.valid) {
      showError(amountValidation.message);
      return;
    }

    // Prepare payment data
    const paymentData = {
      event_id: event.id,
      ticket_id: selectedTicket.id,
      quantity: quantity,
      amount: totalAmount,
      currency: selectedTicket.currency || 'PKR',
      payment_method: selectedPaymentMethod,
      user_email: currentUser.email,
      user_name: currentUser.username || currentUser.name,
      return_url: `${window.location.origin}/payment/success`,
      cancel_url: `${window.location.origin}/payment/cancel`,
      notify_url: `${window.location.origin}/api/payments/demo/notify`
    };

    try {
      await dispatch(createEventPayment(paymentData)).unwrap();
    } catch (error) {
      console.error('Payment creation failed:', error);
    }
  };

  const formatCurrency = (amount, currency = 'PKR') => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Show access denied message if user cannot purchase
  if (paymentAccess && !paymentAccess.canPurchase) {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-center mb-4">
            {paymentAccess.type === 'info' ? (
              <FiInfo className="h-12 w-12 text-blue-500" />
            ) : (
              <FiAlertCircle className="h-12 w-12 text-yellow-500" />
            )}
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
            {paymentAccess.title}
          </h3>
          
          <p className="text-gray-600 text-center mb-6">
            {paymentAccess.message}
          </p>

          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (!event || !currentUser || !paymentAccess) {
    return (
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading payment options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Purchase Tickets</h2>
          <FiShield className="h-6 w-6" />
        </div>
        <p className="text-blue-100">
          Secure ticket purchase for {event.title}
        </p>
      </div>

      <div className="p-6">
        {/* Event Info */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-900 mb-2">{event.title}</h3>
          <div className="space-y-1 text-sm text-gray-600">
            {event.start_datetime && (
              <div className="flex items-center">
                <FiCalendar className="w-4 h-4 mr-2" />
                {new Date(event.start_datetime).toLocaleDateString()}
              </div>
            )}
            {event.location && (
              <div className="flex items-center">
                <FiMapPin className="w-4 h-4 mr-2" />
                {event.location}
              </div>
            )}
            {event.max_attendees && (
              <div className="flex items-center">
                <FiUsers className="w-4 h-4 mr-2" />
                Max {event.max_attendees} attendees
              </div>
            )}
          </div>
        </div>

        {/* Ticket Selection */}
        {event.tickets && event.tickets.length > 0 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Ticket Type
            </label>
            <div className="space-y-2">
              {event.tickets.map((ticket) => (
                <div
                  key={ticket.id}
                  onClick={() => setSelectedTicket(ticket)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedTicket?.id === ticket.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium text-gray-900">{ticket.name}</div>
                      {ticket.description && (
                        <div className="text-sm text-gray-600">{ticket.description}</div>
                      )}
                    </div>
                    <div className="text-lg font-semibold text-gray-900">
                      {formatCurrency(ticket.price, ticket.currency)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quantity Selection */}
        {selectedTicket && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity
            </label>
            <select
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {Array.from({ length: Math.min(selectedTicket.max_quantity_per_order || 10, 10) }, (_, i) => (
                <option key={i + 1} value={i + 1}>
                  {i + 1} ticket{i > 0 ? 's' : ''}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Payment Method Selection */}
        {availablePaymentMethods.length > 1 && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method
            </label>
            <div className="space-y-2">
              {availablePaymentMethods.map((method) => (
                <div
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedPaymentMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{method.name}</div>
                      <div className="text-sm text-gray-600">{method.description}</div>
                    </div>
                    <FiCreditCard className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Total Amount */}
        {selectedTicket && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-lg font-medium text-gray-900">Total Amount:</span>
              <span className="text-2xl font-bold text-blue-600">
                {formatCurrency(totalAmount, selectedTicket.currency)}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handlePurchase}
            disabled={paymentLoading || !selectedTicket}
            className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {paymentLoading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <FiCreditCard className="h-5 w-5 mr-2" />
            )}
            {paymentLoading ? 'Processing...' : 'Purchase Tickets'}
          </button>

          <button
            onClick={onClose}
            className="w-full px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 transition-colors duration-200"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default EventTicketPurchase;
