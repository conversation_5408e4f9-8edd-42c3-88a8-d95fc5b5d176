import React, { useState, useEffect } from 'react';
import { useThemeProvider } from '../../providers/ThemeContext';
import { formatDateTimeSync, formatExamDateTime, backendTimeToUTC } from '../../utils/timezone';
import useTimezone from '../../hooks/useTimezone';
import { FiClock, FiCalendar, FiPlay, FiAlertCircle, FiCheck } from 'react-icons/fi';

const ExamTimeTest = () => {
  const { currentTheme } = useThemeProvider();
  const { timezoneData, formatDateTime, loading: timezoneLoading } = useTimezone();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Sample exam data matching your example
  const sampleExam = {
    id: "10018fa6-1a85-47b6-a7e5-7de9b0e26e19",
    title: "TestExam",
    description: "TestExam",
    total_marks: 15,
    total_duration: 999,
    start_time: "2025-09-16T19:05:00", // This is UTC time from backend (7:05 PM UTC)
    end_time: "2025-09-17T15:44:00",
    status: "upcoming"
  };

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Calculate exam status
  const getExamStatus = (exam) => {
    const now = new Date();
    if (!exam.start_time) return "upcoming";

    // Backend sends UTC time, so convert it properly
    const startTime = backendTimeToUTC(exam.start_time);
    const endTime = new Date(startTime.getTime() + exam.total_duration * 60000);

    if (exam.completed) return "completed";
    if (now > endTime) return "missed";
    if (now >= startTime && now <= endTime) return "active";
    return "upcoming";
  };

  const examStatus = getExamStatus(sampleExam);

  // Calculate time differences
  const startTime = backendTimeToUTC(sampleExam.start_time);
  const endTime = new Date(startTime.getTime() + sampleExam.total_duration * 60000);
  
  const timeUntilStart = startTime.getTime() - currentTime.getTime();
  const timeUntilEnd = endTime.getTime() - currentTime.getTime();

  const formatTimeDifference = (milliseconds) => {
    if (milliseconds <= 0) return "Time passed";
    
    const totalSeconds = Math.floor(milliseconds / 1000);
    const days = Math.floor(totalSeconds / (24 * 3600));
    const hours = Math.floor((totalSeconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (days > 0) return `${days}d ${hours}h ${minutes}m ${seconds}s`;
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  };

  const themeClasses = {
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-white text-gray-900 border-gray-300"
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "upcoming": return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case "completed": return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      case "missed": return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className={`min-h-screen ${themeClasses.bg} ${themeClasses.text} p-8`}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-violet-700 dark:text-violet-400 text-center">
          Exam Time Calculation Test
        </h1>

        {/* Current Time Display */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6`}>
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FiClock className="w-5 h-5" />
            Current Time
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">UTC Time</p>
              <p className="text-lg font-mono">{currentTime.toISOString()}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Your Local Time</p>
              <p className="text-lg font-mono">
                {timezoneData ? formatDateTime(currentTime.toISOString(), true) : 'Loading...'}
              </p>
            </div>
          </div>
        </div>

        {/* Exam Details */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6`}>
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FiCalendar className="w-5 h-5" />
            Exam Information
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">{sampleExam.title}</h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(examStatus)}`}>
                {examStatus.charAt(0).toUpperCase() + examStatus.slice(1)}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Start Time (Backend UTC)</p>
                <p className="font-mono">{sampleExam.start_time}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Parsed UTC Date</p>
                <p className="font-mono">{startTime.toISOString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Your Local Time</p>
                <p className="font-mono">
                  {timezoneData ? formatExamDateTime(sampleExam.start_time) : 'Loading...'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Duration</p>
                <p className="font-mono">{sampleExam.total_duration} minutes</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Total Marks</p>
                <p className="font-mono">{sampleExam.total_marks} marks</p>
              </div>
            </div>
          </div>
        </div>

        {/* Time Calculations */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6`}>
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FiAlertCircle className="w-5 h-5" />
            Time Calculations
          </h2>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-600 dark:text-blue-400 mb-1">Time Until Start</p>
                <p className="text-lg font-mono text-blue-800 dark:text-blue-300">
                  {formatTimeDifference(timeUntilStart)}
                </p>
              </div>
              <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <p className="text-sm text-orange-600 dark:text-orange-400 mb-1">Time Until End</p>
                <p className="text-lg font-mono text-orange-800 dark:text-orange-300">
                  {formatTimeDifference(timeUntilEnd)}
                </p>
              </div>
            </div>

            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h3 className="font-medium mb-2">Debug Information</h3>
              <div className="text-sm space-y-1 font-mono">
                <p>Current Time (UTC): {currentTime.toISOString()}</p>
                <p>Exam Start (Backend): {sampleExam.start_time}</p>
                <p>Exam Start (UTC): {startTime.toISOString()}</p>
                <p>Exam End (UTC): {endTime.toISOString()}</p>
                <p>Can Start: {currentTime >= startTime ? '✅ Yes' : '❌ No'}</p>
                <p>Has Ended: {currentTime > endTime ? '✅ Yes' : '❌ No'}</p>
                <p>Status: {examStatus}</p>
                <p>Time Until Start: {timeUntilStart > 0 ? `${Math.floor(timeUntilStart / 1000)}s` : 'Started'}</p>
                <p>Time Until End: {timeUntilEnd > 0 ? `${Math.floor(timeUntilEnd / 1000)}s` : 'Ended'}</p>
                <p className="text-green-600 dark:text-green-400">✅ Now correctly handling backend UTC times!</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6`}>
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FiPlay className="w-5 h-5" />
            Exam Actions
          </h2>
          
          {examStatus === "active" && (
            <button className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2 font-medium">
              <FiPlay className="w-5 h-5" />
              Take Exam Now
            </button>
          )}
          
          {examStatus === "upcoming" && (
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-blue-800 dark:text-blue-300 font-medium">
                Exam starts in {formatTimeDifference(timeUntilStart)}
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                {timezoneData ? formatExamDateTime(sampleExam.start_time) : 'Loading...'}
              </p>
            </div>
          )}
          
          {examStatus === "missed" && (
            <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <p className="text-red-800 dark:text-red-300 font-medium">
                Exam has ended
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExamTimeTest;
