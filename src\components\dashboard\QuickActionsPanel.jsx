/**
 * Quick Actions Panel Component
 * 
 * Displays actionable items for students including:
 * - Overdue assignments
 * - Upcoming exams
 * - Pending tasks
 * - Important deadlines
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FiClock, 
  FiAlertTriangle, 
  FiBookOpen, 
  FiCalendar,
  FiPlay,
  FiEye,
  FiEdit,
  FiArrowRight
} from 'react-icons/fi';
import { Card } from '../ui/layout';
import { formatDistanceToNow } from 'date-fns';

const QuickActionsPanel = ({ quickActions = [], loading = false }) => {
  const navigate = useNavigate();

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (!quickActions || quickActions.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Quick Actions
        </h3>
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <FiClock className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>All caught up! No urgent actions needed.</p>
        </div>
      </Card>
    );
  }

  const getActionIcon = (actionType) => {
    switch (actionType) {
      case 'assignment':
      case 'task':
        return FiEdit;
      case 'exam':
        return FiCalendar;
      case 'submission':
        return FiPlay;
      case 'review':
        return FiEye;
      default:
        return FiBookOpen;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
      case 'urgent':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      case 'medium':
        return 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20';
      case 'low':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      default:
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  const handleActionClick = (action) => {
    if (action.url) {
      navigate(action.url);
    } else {
      // Default navigation based on action type
      switch (action.action_type) {
        case 'assignment':
        case 'task':
          navigate('/student/tasks');
          break;
        case 'exam':
          navigate('/student/exams');
          break;
        case 'submission':
          navigate('/student/tasks');
          break;
        default:
          console.log('Action clicked:', action);
      }
    }
  };

  const formatDueDate = (dueDate) => {
    if (!dueDate) return null;
    
    try {
      const date = new Date(dueDate);
      const now = new Date();
      
      if (date < now) {
        return {
          text: `Overdue by ${formatDistanceToNow(date)}`,
          isOverdue: true
        };
      } else {
        return {
          text: `Due in ${formatDistanceToNow(date)}`,
          isOverdue: false
        };
      }
    } catch (error) {
      return null;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Quick Actions
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {quickActions.length} item{quickActions.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-3">
        {quickActions.map((action) => {
          const IconComponent = getActionIcon(action.action_type);
          const priorityColor = getPriorityColor(action.priority);
          const dueDateInfo = formatDueDate(action.due_date);

          return (
            <div
              key={action.id}
              onClick={() => handleActionClick(action)}
              className="flex items-center gap-4 p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all duration-200 cursor-pointer group"
            >
              {/* Icon */}
              <div className={`p-2 rounded-lg ${priorityColor}`}>
                <IconComponent className="w-5 h-5" />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {action.title}
                    </h4>
                    {action.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                        {action.description}
                      </p>
                    )}
                  </div>

                  {/* Priority Badge */}
                  {action.priority && action.priority !== 'medium' && (
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${priorityColor} ml-3`}>
                      {action.priority}
                    </span>
                  )}
                </div>

                {/* Due Date */}
                {dueDateInfo && (
                  <div className="flex items-center gap-2 mt-2">
                    <FiClock className={`w-4 h-4 ${dueDateInfo.isOverdue ? 'text-red-500' : 'text-gray-400'}`} />
                    <span className={`text-sm ${dueDateInfo.isOverdue ? 'text-red-600 dark:text-red-400 font-medium' : 'text-gray-500 dark:text-gray-400'}`}>
                      {dueDateInfo.text}
                    </span>
                  </div>
                )}
              </div>

              {/* Arrow */}
              <FiArrowRight className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
            </div>
          );
        })}
      </div>

      {/* View All Actions */}
      {quickActions.length > 3 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => navigate('/student/tasks')}
            className="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors"
          >
            View All Tasks & Assignments
          </button>
        </div>
      )}
    </Card>
  );
};

export default QuickActionsPanel;
