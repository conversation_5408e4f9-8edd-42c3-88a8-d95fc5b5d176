import React from 'react';
import { FiMessageCircle, FiUser, FiClock } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';

/**
 * ConversationList Component
 * Displays a list of conversations with last message and unread count
 */
const ConversationList = ({
  conversations = [],
  selectedConversationId,
  onConversationSelect,
  loading = false,
  error = null,
  className = ''
}) => {
  const { currentTheme } = useThemeProvider();

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 168) { // 7 days
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const truncateMessage = (message, maxLength = 50) => {
    if (!message) return 'No messages yet';
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="space-y-3">
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg animate-pulse ${
                currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full ${
                  currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                }`} />
                <div className="flex-1 space-y-2">
                  <div className={`h-4 rounded ${
                    currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                  } w-3/4`} />
                  <div className={`h-3 rounded ${
                    currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                  } w-1/2`} />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-center ${className}`}>
        <div className={`text-red-500 mb-2`}>
          <FiMessageCircle className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">Failed to load conversations</p>
          <p className="text-xs opacity-75">{error}</p>
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className={`p-8 text-center ${className}`}>
        <FiMessageCircle className={`w-12 h-12 mx-auto mb-4 ${
          currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-400'
        }`} />
        <h3 className={`text-lg font-medium mb-2 ${
          currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
        }`}>
          No conversations yet
        </h3>
        <p className={`text-sm ${
          currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-500'
        }`}>
          Start a conversation by messaging someone you follow
        </p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="space-y-1">
        {conversations.map((conversation) => {
          const { other_user, last_message, unread_count, last_activity } = conversation;
          const isSelected = selectedConversationId === other_user.id;
          const hasUnread = unread_count > 0;

          return (
            <div
              key={other_user.id}
              onClick={() => onConversationSelect(other_user.id)}
              className={`
                p-3 rounded-lg cursor-pointer transition-all duration-200 border
                ${isSelected
                  ? currentTheme === 'dark'
                    ? 'bg-blue-900/30 border-blue-500/50'
                    : 'bg-blue-50 border-blue-200'
                  : currentTheme === 'dark'
                    ? 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }
                ${hasUnread ? 'ring-2 ring-blue-500/20' : ''}
              `}
            >
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <div className="relative flex-shrink-0">
                  {other_user.profile_picture ? (
                    <img
                      src={other_user.profile_picture}
                      alt={other_user.username}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className={`
                      w-10 h-10 rounded-full flex items-center justify-center
                      ${currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'}
                    `}>
                      <FiUser className="w-5 h-5 text-gray-500" />
                    </div>
                  )}
                  
                  {/* Online indicator */}
                  {other_user.is_online && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={`font-medium truncate ${
                      currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-900'
                    }`}>
                      {other_user.username}
                    </h4>
                    
                    <div className="flex items-center space-x-2 flex-shrink-0">
                      {/* Time */}
                      <span className={`text-xs flex items-center ${
                        currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-500'
                      }`}>
                        <FiClock className="w-3 h-3 mr-1" />
                        {formatTime(last_activity)}
                      </span>
                      
                      {/* Unread count */}
                      {hasUnread && (
                        <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                          {unread_count > 99 ? '99+' : unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Last message */}
                  <p className={`text-sm truncate ${
                    hasUnread
                      ? currentTheme === 'dark' ? 'text-gray-300 font-medium' : 'text-gray-700 font-medium'
                      : currentTheme === 'dark' ? 'text-gray-500' : 'text-gray-500'
                  }`}>
                    {last_message?.is_deleted 
                      ? '🗑️ Message deleted'
                      : truncateMessage(last_message?.message)
                    }
                  </p>
                  
                  {/* User type badge */}
                  <div className="mt-1">
                    <span className={`inline-block text-xs px-2 py-0.5 rounded-full ${
                      other_user.user_type === 'teacher'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : other_user.user_type === 'student'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400'
                    }`}>
                      {other_user.user_type}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ConversationList;
