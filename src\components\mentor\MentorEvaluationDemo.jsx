import React, { useState } from 'react';
import SubmissionMarkingInterface from './SubmissionMarkingInterface';
import CompetitionSubmissionsList from './CompetitionSubmissionsList';

const MentorEvaluationDemo = () => {
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'marking'

  // Sample data matching your structure
  const sampleSubmissionData = {
    "attempt_id": "8248daaa-6970-494d-a3c0-584bfc869eca",
    "student": {
        "id": "3be0f4e1-cde3-4a67-9f17-3a77941f43cd",
        "name": "Student User",
        "email": "<EMAIL>"
    },
    "exam": {
        "id": "f5734af5-2e74-485a-b185-63223698478d",
        "title": "Exam1",
        "description": "Desc",
        "total_marks": 15
    },
    "competition": {
        "id": "dd2b355c-949d-4b7c-accd-cc0095191de7",
        "title": "EventCompetitionTest"
    },
    "submission": {
        "submitted_at": "2025-09-20T06:04:02.480676",
        "duration_seconds": null,
        "status": "registered"
    },
    "answers": [
        {
            "question_id": "f6d08028-2f18-4431-bda9-2494b2ee04a2",
            "question_text": "Simplify the algebraic expression: `(3x^2y^3)^2 / (9xy^4)`",
            "question_type": "MCQS",
            "marks": 1,
            "correct_answer": "To simplify: `(3x^2y^3)^2 = 9x^4y^6`. So, the expression becomes `(9x^4y^6) / (9xy^4)`. Divide the coefficients: `9/9 = 1`. Divide the x-terms: `x^4/x = x^(4-1) = x^3`. Divide the y-terms: `y^6/y^4 = y^(6-4) = y^2`. Combining these, the simplified expression is `x^3y^2`.",
            "student_answer": "x^3y^2",
            "answer_id": "d94cc158-aa76-4712-a8e4-5720ecff44de"
        },
        {
            "question_id": "42fb6e46-934e-44cc-b89a-009f5aceb5ce",
            "question_text": "Solve for x in the equation: `(2x - 3) / 5 + (x + 1) / 2 = 4`",
            "question_type": "SHORT",
            "marks": 3,
            "correct_answer": "To solve the equation, first find the least common denominator (LCD) of 5 and 2, which is 10. Multiply every term by the LCD:\n`10 * ((2x - 3) / 5) + 10 * ((x + 1) / 2) = 10 * 4`\n`2(2x - 3) + 5(x + 1) = 40`\nDistribute the coefficients:\n`4x - 6 + 5x + 5 = 40`\nCombine like terms:\n`9x - 1 = 40`\nAdd 1 to both sides:\n`9x = 41`\nDivide by 9:\n`x = 41/9`",
            "student_answer": "123",
            "answer_id": "09175110-aa06-48cf-8c91-84d56ff973cf"
        },
        {
            "question_id": "34d68a05-f390-491d-bd23-df2889e59f3d",
            "question_text": "A rectangular garden has an area of 120 square meters. If its length is 2 meters more than twice its width, determine the dimensions (length and width) of the garden. Provide your answer to two decimal places if necessary.",
            "question_type": "LONG",
            "marks": 5,
            "correct_answer": "Let the width of the garden be `w` meters.\nAccording to the problem, the length `L` is 2 meters more than twice its width, so `L = 2w + 2`.\nThe area of a rectangle is given by `Area = Length × Width`.\nWe are given the area is 120 square meters.\nSo, `w(2w + 2) = 120`\n`2w^2 + 2w = 120`\nSubtract 120 from both sides to form a quadratic equation:\n`2w^2 + 2w - 120 = 0`\nDivide the entire equation by 2 to simplify:\n`w^2 + w - 60 = 0`\nFactor the quadratic equation. We need two numbers that multiply to -60 and add to 1. These numbers are 6 and -5.\n`(w + 6)(w - 5) = 0`\nThis gives two possible solutions for `w`: `w = -6` or `w = 5`.\nSince width cannot be a negative value, we take `w = 5` meters.\nNow, substitute `w = 5` back into the expression for length:\n`L = 2(5) + 2 = 10 + 2 = 12` meters.\nThus, the dimensions of the garden are: Width = 5 meters, Length = 12 meters.",
            "student_answer": "1234",
            "answer_id": "13936e0c-4ed1-4779-80d1-8405b5575f82"
        },
        {
            "question_id": "e430819a-4df1-40c3-827e-a0d82cf83835",
            "question_text": "Which of the following is the inverse function of `f(x) = 3x - 7`?",
            "question_type": "MCQS",
            "marks": 2,
            "correct_answer": "To find the inverse function, follow these steps:\n1. Replace `f(x)` with `y`: `y = 3x - 7`\n2. Swap `x` and `y`: `x = 3y - 7`\n3. Solve for `y`:\n   `x + 7 = 3y`\n   `y = (x + 7) / 3`\n4. Replace `y` with `f^-1(x)`: `f^-1(x) = (x + 7) / 3`",
            "student_answer": "(x + 7) / 3",
            "answer_id": "abfabd80-0db5-4cf5-bff6-dcc027180812"
        },
        {
            "question_id": "a3257474-5421-4622-9439-ced374162612",
            "question_text": "For what value(s) of `k` does the quadratic equation `x^2 - (k+2)x + 9 = 0` have exactly one real solution?",
            "question_type": "SHORT",
            "marks": 4,
            "correct_answer": "For a quadratic equation `ax^2 + bx + c = 0` to have exactly one real solution, its discriminant (`Δ = b^2 - 4ac`) must be equal to zero.\nIn the given equation `x^2 - (k+2)x + 9 = 0`:\n`a = 1`\n`b = -(k+2)`\n`c = 9`\nSet the discriminant to zero:\n`(-(k+2))^2 - 4(1)(9) = 0`\n`(k+2)^2 - 36 = 0`\nRearrange the equation:\n`(k+2)^2 = 36`\nTake the square root of both sides:\n`k+2 = ±√36`\n`k+2 = ±6`\nThis gives two possible cases:\nCase 1: `k+2 = 6`\n`k = 6 - 2`\n`k = 4`\nCase 2: `k+2 = -6`\n`k = -6 - 2`\n`k = -8`\nSo, the values of `k` for which the equation has exactly one real solution are `k = 4` and `k = -8`.",
            "student_answer": "123",
            "answer_id": "d0f4f887-b62e-4103-b4e9-c0606cfbbf5a"
        }
    ],
    "mentor_evaluation": {
        "is_evaluated": false,
        "score": null,
        "feedback": null,
        "evaluated_at": null
    }
  };

  // Mock submissions list data
  const sampleSubmissionsList = [sampleSubmissionData];

  // Mock rankings data for testing certificate generation
  const sampleRankingsData = [
    {
      participant_id: "3be0f4e1-cde3-4a67-9f17-3a77941f43cd",
      student_id: "3be0f4e1-cde3-4a67-9f17-3a77941f43cd",
      participant_name: "Student User",
      participant_email: "<EMAIL>",
      rank: 1,
      position: 1,
      total_score: 12,
      max_score: 15,
      percentage: 80,
      status: "completed"
    },
    {
      participant_id: "another-student-id",
      student_id: "another-student-id",
      participant_name: "Another Student",
      participant_email: "<EMAIL>",
      rank: 2,
      position: 2,
      total_score: 10,
      max_score: 15,
      percentage: 67,
      status: "completed"
    }
  ];

  // Mock certificate data for testing certificate viewer
  const sampleCertificateData = {
    certificate_id: "cert-123",
    student_id: "3be0f4e1-cde3-4a67-9f17-3a77941f43cd",
    student_name: "Student User",
    competition_id: "dd2b355c-949d-4b7c-accd-cc0095191de7",
    competition_title: "EventCompetitionTest",
    position: 1,
    certificate_type: "winner",
    custom_message: "Congratulations on achieving 1st place in this competition! Your outstanding performance demonstrates exceptional skill and dedication.",
    issued_at: "2025-09-20T10:30:00Z",
    created_at: "2025-09-20T10:30:00Z"
  };

  // Mock service functions
  const mockGetSubmissionDetails = async (attemptId) => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(sampleSubmissionData), 500);
    });
  };

  const mockMarkSubmission = async (markingData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Marking submitted:', markingData);
        resolve({ success: true });
      }, 1000);
    });
  };

  const handleSubmissionSelect = (submission) => {
    setSelectedSubmission(submission);
    setViewMode('marking');
  };

  const handleBackToList = () => {
    setSelectedSubmission(null);
    setViewMode('list');
  };

  const handleSubmissionMarked = () => {
    alert('Submission marked successfully!');
    handleBackToList();
  };



  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Mentor Evaluation System Demo
        </h1>
        <p className="text-gray-600">
          This demo shows how the mentor evaluation system renders your submission data structure.
        </p>
      </div>

      {viewMode === 'list' ? (
        <CompetitionSubmissionsList
          competitionId="demo"
          submissions={sampleSubmissionsList}
          onSubmissionSelect={handleSubmissionSelect}
        />
      ) : (
        <SubmissionMarkingInterface
          attemptId={selectedSubmission?.attempt_id}
          onBack={handleBackToList}
          onSubmissionMarked={handleSubmissionMarked}
          submissionData={sampleSubmissionData}
        />
      )}
    </div>
  );
};

export default MentorEvaluationDemo;
