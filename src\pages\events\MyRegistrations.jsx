/**
 * My Registrations Page
 * 
 * Shows user's event registrations and allows payment for unpaid tickets.
 * This is where users complete payment for paid events after registering.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiMapPin,
  FiCreditCard,
  FiCheck,
  FiClock,
  FiX,
  FiRefreshCw,
  FiEye,
  FiDownload,
  FiAlertCircle,
  FiDollarSign,
  FiPlay
} from 'react-icons/fi';
import { format } from 'date-fns';
import newEventService from '../../services/newEventService';
import { useNotification } from '../../contexts/NotificationContext';
import { LoadingSpinner } from '../../components/ui';
import { TicketViewer } from '../../components/tickets';
import { BASE_API } from '../../utils/api/API_URL';
import { downloadTicketPDF } from '../../utils/ticketPdfGenerator';
import EventTimingBadge from '../../components/events/EventTimingBadge';
import competitionExamService from '../../services/competitionExamService';

const MyRegistrations = () => {
  const navigate = useNavigate();
  const [registrations, setRegistrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [payingFor, setPayingFor] = useState(null);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketViewer, setShowTicketViewer] = useState(false);

  const { showSuccess, showError, showInfo } = useNotification();

  // Load user registrations
  const loadRegistrations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await newEventService.getMyRegistrations();
      console.log('Raw registrations response:', response);
      // API returns array directly, not wrapped in object
      const registrationsData = Array.isArray(response) ? response : response.registrations || [];
      console.log('Processed registrations data:', registrationsData);
      setRegistrations(registrationsData);

    } catch (error) {
      console.error('Failed to load registrations:', error);
      setError(error.message || 'Failed to load registrations');
      showError(error.message || 'Failed to load registrations');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadRegistrations();
  }, []);

  // Handle payment
  const handlePayment = async (registration) => {
    try {
      setPayingFor(registration.registration_id);
      showInfo('Creating payment link...');

      const paymentResult = await newEventService.payForRegistration(registration.registration_id);

      if (paymentResult.success) {
        showInfo('Redirecting to payment...');
        // The service will automatically handle the PayFast redirect
      }

    } catch (error) {
      console.error('Payment failed:', error);
      showError(error.message || 'Failed to create payment link');
    } finally {
      setPayingFor(null);
    }
  };

  // Handle viewing ticket
  const handleViewTicket = (registration) => {
    console.log('View ticket clicked for registration:', registration);
    alert(`View ticket clicked for: ${registration.event?.title || 'Unknown Event'}`);
    setSelectedTicket(registration);
    setShowTicketViewer(true);
  };

  // Handle downloading ticket
  const handleDownloadTicket = async (registration) => {
    try {
      console.log('Download ticket clicked for registration:', registration);
      showInfo('Generating ticket PDF...');

      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      console.log('Using token:', token ? 'Token found' : 'No token');

      // Fetch ticket data from backend
      const response = await fetch(`${BASE_API}/api/tickets/registrations/${registration.registration_id}/ticket`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Ticket data response status:', response.status);

      if (!response.ok) {
        throw new Error(`Failed to fetch ticket data: ${response.status} ${response.statusText}`);
      }

      const ticketData = await response.json();
      console.log('Ticket data received:', ticketData);

      // Generate and download PDF
      showInfo('Creating PDF...');
      const result = downloadTicketPDF(ticketData);

      if (result.success) {
        showSuccess('Ticket PDF downloaded successfully!');
      } else {
        throw new Error(result.error || 'Failed to generate PDF');
      }
    } catch (error) {
      console.error('Download error:', error);
      showError(`Failed to download ticket: ${error.message}`);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy \'at\' h:mm a');
    } catch (error) {
      return dateString;
    }
  };

  // Get status badge
  const getStatusBadge = (registration) => {
    const { status, payment } = registration;
    
    if (status === 'CONFIRMED') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <FiCheck className="w-3 h-3 mr-1" />
          Confirmed
        </span>
      );
    }
    
    if (status === 'PENDING_PAYMENT') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <FiClock className="w-3 h-3 mr-1" />
          Payment Required
        </span>
      );
    }
    
    if (status === 'CANCELLED') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <FiX className="w-3 h-3 mr-1" />
          Cancelled
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {status}
      </span>
    );
  };

  // Render loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Registrations</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadRegistrations}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Event Registrations</h1>
            <p className="text-gray-600 mt-2">
              Manage your event registrations and complete payments
            </p>
          </div>
          <button
            onClick={loadRegistrations}
            disabled={loading}
            className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <FiRefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>

        {/* Registrations List */}
        {registrations.length === 0 ? (
          <div className="text-center py-12">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Registrations Found</h3>
            <p className="text-gray-600 mb-4">
              You haven't registered for any events yet.
            </p>
            <a
              href="/events/new"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Browse Events
            </a>
          </div>
        ) : (
          <div className="space-y-6">
            {registrations.map((registration) => (
              <div
                key={registration.registration_id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    {/* Event Info */}
                    <div className="flex-1">
                      <div className="flex items-start space-x-4">
                        {registration.event.banner_image_url && (
                          <img
                            src={registration.event.banner_image_url}
                            alt={registration.event.title}
                            className="w-20 h-20 object-cover rounded-lg"
                          />
                        )}
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-xl font-semibold text-gray-900">
                              {registration.event.title}
                            </h3>
                            <EventTimingBadge event={registration.event} variant="inline" size="default" />
                          </div>

                          <div className="space-y-2 text-sm text-gray-600">
                            <div className="flex items-center">
                              <FiCalendar className="w-4 h-4 mr-2" />
                              {formatDate(registration.event.start_datetime)}
                            </div>
                            <div className="flex items-center">
                              <FiMapPin className="w-4 h-4 mr-2" />
                              {registration.event.location}
                            </div>
                          </div>

                          {/* Registration Details */}
                          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-900">Registration #:</span>
                                <p className="text-gray-600">{registration.registration_number}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Ticket:</span>
                                <p className="text-gray-600">{registration.ticket?.name || 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium text-gray-900">Quantity:</span>
                                <p className="text-gray-600">{registration.ticket?.quantity || 1}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Status and Actions */}
                    <div className="ml-6 text-right">
                      <div className="mb-4">
                        {getStatusBadge(registration)}
                      </div>

                      {/* Price */}
                      <div className="mb-4">
                        {!registration.ticket?.total_amount || registration.ticket.total_amount === 0 ? (
                          <span className="text-lg font-bold text-green-600">FREE</span>
                        ) : (
                          <div className="text-right">
                            <div className="flex items-center text-lg font-bold text-gray-900">
                              <FiDollarSign className="w-4 h-4" />
                              {(registration.ticket.total_amount || 0).toFixed(2)} {registration.ticket.currency || 'ZAR'}
                            </div>
                            {registration.status === 'PENDING_PAYMENT' && (
                              <div className="text-xs text-orange-600 mt-1">
                                Payment required
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="space-y-2">
                        {registration.status === 'PENDING_PAYMENT' && (
                          <button
                            onClick={() => handlePayment(registration)}
                            disabled={payingFor === registration.registration_id}
                            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                          >
                            {payingFor === registration.registration_id ? (
                              <>
                                <LoadingSpinner size="sm" className="mr-2" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <FiCreditCard className="w-4 h-4 mr-2" />
                                Pay Now
                              </>
                            )}
                          </button>
                        )}

                        {registration.status === 'CONFIRMED' && (
                          <button
                            onClick={() => {
                              console.log('Download button clicked!');
                              alert('Download button clicked!');
                              handleDownloadTicket(registration);
                            }}
                            className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                          >
                            <FiDownload className="w-4 h-4 mr-2" />
                            Download Ticket
                          </button>
                        )}

                        <button
                          onClick={() => {
                            console.log('Button clicked!');
                            alert('Button clicked!');
                            handleViewTicket(registration);
                          }}
                          className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                        >
                          <FiEye className="w-4 h-4 mr-2" />
                          View Ticket
                        </button>

                        {/* Competition Exam Button - ALWAYS VISIBLE FOR TESTING */}
                        <button
                          onClick={async () => {
                            const eventId = registration.event?.id || registration.event_id;
                            console.log('🏆 Competition exam clicked!', { eventId, event: registration.event });

                            try {
                              // Validate competition exam first
                              const validation = await competitionExamService.validateCompetitionExam(eventId);
                              console.log('🔍 Competition validation:', validation);

                              if (!validation.success) {
                                alert(validation.message || 'This event is not a valid competition exam');
                                return;
                              }

                              // Navigate to competition exam with event ID
                              navigate(`/student/take-competition-exam/${eventId}`, {
                                state: {
                                  event: registration.event,
                                  registration: registration,
                                  examId: validation.exam_id
                                }
                              });

                            } catch (error) {
                              console.error('❌ Competition validation failed:', error);
                              alert(error.message || 'Failed to validate competition exam');
                            }
                          }}
                          className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center font-medium shadow-lg"
                        >
                          <FiPlay className="w-4 h-4 mr-2" />
                          🏆 TAKE COMPETITION EXAM (TEST)
                        </button>

                        {/* Test button */}
                        <button
                          onClick={() => alert('Test button works!')}
                          className="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center"
                        >
                          TEST BUTTON
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Backend Notice */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <FiAlertCircle className="w-5 h-5 text-green-600 mr-2 flex-shrink-0" />
            <div className="text-sm text-green-800">
              <strong>✅ Registration APIs Working:</strong> Both registration (<code>POST /api/events/registrations/</code>) and
              user registrations (<code>GET /api/events/registrations/my-registrations</code>) are connected!
              Payment endpoints coming soon.
            </div>
          </div>
        </div>
      </div>

      {/* Ticket Viewer Modal */}
      {showTicketViewer && selectedTicket && (
        <TicketViewer
          registrationId={selectedTicket.registration_id}
          eventData={selectedTicket.event}
          onClose={() => {
            setShowTicketViewer(false);
            setSelectedTicket(null);
          }}
        />
      )}
    </div>
  );
};

export default MyRegistrations;
