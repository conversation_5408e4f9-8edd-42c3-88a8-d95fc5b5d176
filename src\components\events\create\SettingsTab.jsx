import React from 'react';

// Helper component for required field labels
const RequiredFieldLabel = ({ children, required = false, className = "" }) => (
  <label className={`block text-sm font-medium text-gray-700 mb-2 ${className}`}>
    {children}
    {required && <span className="text-red-500 ml-1">*</span>}
  </label>
);

const SettingsTab = ({ 
  formData, 
  touchedFields, 
  hasAttemptedSubmit, 
  onChange,
  onFieldTouch 
}) => {
  // Helper to determine if we should show validation styling
  const shouldShowValidation = (fieldName, value) => {
    return (touchedFields[fieldName] || hasAttemptedSubmit) && (!value || value.toString().trim() === '');
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Settings</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <RequiredFieldLabel required>Status</RequiredFieldLabel>
          <select
            value={formData.status}
            onChange={(e) => onChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="DRAFT">Draft</option>
            <option value="PUBLISHED">Published</option>
          </select>
          <p className="text-sm text-gray-500 mt-1">Only published events are visible to users</p>
        </div>

        <div>
          <RequiredFieldLabel required>Maximum Attendees</RequiredFieldLabel>
          <input
            type="number"
            value={formData.max_attendees}
            onChange={(e) => onChange('max_attendees', e.target.value)}
            onBlur={() => onFieldTouch('max_attendees')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('max_attendees', formData.max_attendees) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
            placeholder="Enter maximum number of attendees"
            min="1"
          />
          {shouldShowValidation('max_attendees', formData.max_attendees) && (
            <p className="text-red-500 text-xs mt-1">Maximum attendees is required for publishing</p>
          )}
        </div>
      </div>

      {/* Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_featured_settings"
            checked={formData.is_featured}
            onChange={(e) => onChange('is_featured', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_featured_settings" className="ml-2 block text-sm text-gray-900">
            Featured Event (appears prominently on homepage)
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_public"
            checked={formData.is_public}
            onChange={(e) => onChange('is_public', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_public" className="ml-2 block text-sm text-gray-900">
            Public Event (visible to all users)
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="requires_approval"
            checked={formData.requires_approval}
            onChange={(e) => onChange('requires_approval', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="requires_approval" className="ml-2 block text-sm text-gray-900">
            Requires Approval (registrations need manual approval)
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_competition_settings"
            checked={formData.is_competition}
            onChange={(e) => onChange('is_competition', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="is_competition_settings" className="ml-2 block text-sm text-gray-900">
            Competition Event (includes prizes and rules)
          </label>
        </div>
      </div>

      {/* Requirements */}
      <div>
        <RequiredFieldLabel>Requirements</RequiredFieldLabel>
        <textarea
          value={formData.requirements}
          onChange={(e) => onChange('requirements', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Any prerequisites or requirements for attendees"
        />
      </div>

      {/* Tags */}
      <div>
        <RequiredFieldLabel>Tags</RequiredFieldLabel>
        <input
          type="text"
          value={formData.tags}
          onChange={(e) => onChange('tags', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="e.g., technology, workshop, beginner"
        />
      </div>

      {/* Competition Fields */}
      {formData.is_competition && (
        <div>
          <RequiredFieldLabel>Competition Rules</RequiredFieldLabel>
          <div className="space-y-4">
            <textarea
              value={formData.competition_rules}
              onChange={(e) => onChange('competition_rules', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Rules and guidelines for the competition"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsTab;
