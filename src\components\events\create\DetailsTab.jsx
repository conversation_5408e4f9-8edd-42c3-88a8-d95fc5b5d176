import React from 'react';
import { EVENT_CATEGORIES } from '../../../constants/eventCategories';

// Helper component for required field labels
const RequiredFieldLabel = ({ children, required = false, className = "" }) => (
  <label className={`block text-sm font-medium text-gray-700 mb-2 ${className}`}>
    {children}
    {required && <span className="text-red-500 ml-1">*</span>}
  </label>
);

const DetailsTab = ({ 
  formData, 
  touchedFields, 
  hasAttemptedSubmit, 
  onChange,
  onFieldTouch 
}) => {
  // Helper to determine if we should show validation styling
  const shouldShowValidation = (fieldName, value) => {
    return (touchedFields[fieldName] || hasAttemptedSubmit) && (!value || value.toString().trim() === '');
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Event Details</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <RequiredFieldLabel required>Registration Start</RequiredFieldLabel>
          <input
            type="datetime-local"
            value={formData.registration_start}
            onChange={(e) => onChange('registration_start', e.target.value)}
            onBlur={() => onFieldTouch('registration_start')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('registration_start', formData.registration_start) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
          />
          {shouldShowValidation('registration_start', formData.registration_start) && (
            <p className="text-red-500 text-xs mt-1">Registration start is required</p>
          )}
          {formData.registration_start && formData.start_datetime && 
           new Date(formData.registration_start) >= new Date(formData.start_datetime) && (
            <p className="text-red-500 text-xs mt-1">
              Registration must start before event start time
            </p>
          )}
          <p className="text-xs text-gray-500 mt-1">When registration opens (also used for ticket sales)</p>
        </div>

        <div>
          <RequiredFieldLabel required>Registration Deadline</RequiredFieldLabel>
          <input
            type="datetime-local"
            value={formData.registration_end}
            onChange={(e) => onChange('registration_end', e.target.value)}
            onBlur={() => onFieldTouch('registration_end')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('registration_end', formData.registration_end) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
          />
          {shouldShowValidation('registration_end', formData.registration_end) && (
            <p className="text-red-500 text-xs mt-1">Registration deadline is required</p>
          )}
          {formData.registration_end && formData.start_datetime && 
           new Date(formData.registration_end) >= new Date(formData.start_datetime) && (
            <p className="text-red-500 text-xs mt-1">
              Registration must end before event start time
            </p>
          )}
          <p className="text-xs text-gray-500 mt-1">Must be before event start time (also used for ticket sales)</p>
        </div>

        {/* Registration Period Info */}
        {formData.registration_start && formData.registration_end && (
          <div className="md:col-span-2 bg-blue-50 border border-blue-200 rounded-md p-3">
            <p className="text-sm text-blue-800">
              <strong>📅 Registration & Ticket Sales Period:</strong> These dates will be used for both event registration and ticket sales
            </p>
          </div>
        )}

        <div className="md:col-span-2">
          <RequiredFieldLabel>Location</RequiredFieldLabel>
          <div className="relative">
            <input
              type="text"
              value={formData.category === EVENT_CATEGORIES.COMPETITION ? 'OnWebsite' : formData.location}
              onChange={(e) => onChange('location', e.target.value)}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                formData.category === EVENT_CATEGORIES.COMPETITION
                  ? 'bg-blue-50 border-blue-300 text-blue-800'
                  : ''
              }`}
              placeholder={formData.category === EVENT_CATEGORIES.COMPETITION ? 'OnWebsite' : 'Full address or online meeting link'}
              readOnly={formData.category === EVENT_CATEGORIES.COMPETITION}
            />
            {formData.category === EVENT_CATEGORIES.COMPETITION && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Auto-set
                </span>
              </div>
            )}
          </div>
          {formData.category === EVENT_CATEGORIES.COMPETITION && (
            <p className="text-blue-600 text-xs mt-1">
              🌐 Competition events are automatically set to be held online
            </p>
          )}
        </div>

        <div className="md:col-span-2">
          <RequiredFieldLabel>Banner Image URL</RequiredFieldLabel>
          <input
            type="url"
            value={formData.banner_image}
            onChange={(e) => onChange('banner_image', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="https://example.com/banner.jpg"
          />
        </div>

        {/* Competition Details */}
        {formData.category === EVENT_CATEGORIES.COMPETITION && (
          <>
            <div className="md:col-span-2">
              <RequiredFieldLabel>Competition Rules</RequiredFieldLabel>
              <textarea
                value={formData.competition_rules}
                onChange={(e) => onChange('competition_rules', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Competition rules and guidelines"
              />
            </div>

            <div className="md:col-span-2">
              <RequiredFieldLabel>Prizes</RequiredFieldLabel>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">1st Place</label>
                  <input
                    type="text"
                    value={formData.first_prize}
                    onChange={(e) => onChange('first_prize', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="1st place prize"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">2nd Place</label>
                  <input
                    type="text"
                    value={formData.second_prize}
                    onChange={(e) => onChange('second_prize', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="2nd place prize"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">3rd Place</label>
                  <input
                    type="text"
                    value={formData.third_prize}
                    onChange={(e) => onChange('third_prize', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="3rd place prize"
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DetailsTab;
