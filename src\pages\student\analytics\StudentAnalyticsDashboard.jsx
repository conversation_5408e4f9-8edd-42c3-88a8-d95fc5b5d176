import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FluidPageContainer } from '../../../components/ui/layout';
import { PerformanceOverview, AnalyticsInsights } from '../../../components/analytics';
import { useStudentAnalytics } from '../../../hooks/useStudentAnalytics';
import {
  FiAward,
  FiBookOpen,
  FiUsers,
  FiArrowRight
} from 'react-icons/fi';

// Chart components
import { D3<PERSON><PERSON><PERSON><PERSON>, D3Bar<PERSON>hart, D3Radar<PERSON>hart } from '../../../components/charts';

/**
 * Student Analytics Dashboard - Main overview page
 * Displays comprehensive analytics with key metrics and visualizations
 */
const StudentAnalyticsDashboard = () => {
  const navigate = useNavigate();

  // Use the analytics hook
  const {
    data: { comprehensive: comprehensiveAnalytics },
    loading: { comprehensive: comprehensiveLoading },
    errors: { comprehensive: comprehensiveError },
    fetch: { comprehensive: fetchComprehensiveData },
    refresh: { comprehensive: refreshData },
    clearErrors
  } = useStudentAnalytics({
    autoFetch: false, // Disabled to prevent double API calls
    fetchAll: false
  });

  // Fetch data on mount and clear errors on unmount
  useEffect(() => {
    // Only fetch if we don't have data already
    if (!comprehensiveAnalytics && !comprehensiveLoading) {
      fetchComprehensiveData();
    }

    return () => {
      clearErrors();
    };
  }, [fetchComprehensiveData, comprehensiveAnalytics, comprehensiveLoading, clearErrors]);

  // Handle date range change
  const handleDateRangeChange = (newRange) => {
    setDateRange(prev => ({ ...prev, ...newRange }));
  };

  // Loading state
  if (comprehensiveLoading || isLoading) {
    return (
      <FluidPageContainer>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </FluidPageContainer>
    );
  }

  // Error state
  if (comprehensiveError) {
    return (
      <FluidPageContainer>
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">
            <FiActivity />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
            Unable to Load Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {comprehensiveError}
          </p>
          <button
            onClick={refreshData}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </FluidPageContainer>
    );
  }

  const analytics = comprehensiveAnalytics;
  if (!analytics) return null;

  return (
    <FluidPageContainer>
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Comprehensive view of your academic performance and progress
            </p>
          </div>

          {/* Quick Navigation */}
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <button
              onClick={() => navigate('/student/analytics/subjects')}
              className="px-4 py-2 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors flex items-center"
            >
              <FiBookOpen className="w-4 h-4 mr-2" />
              Subjects
              <FiArrowRight className="w-4 h-4 ml-2" />
            </button>
            <button
              onClick={() => navigate('/student/analytics/classroom')}
              className="px-4 py-2 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors flex items-center"
            >
              <FiUsers className="w-4 h-4 mr-2" />
              Classroom
              <FiArrowRight className="w-4 h-4 ml-2" />
            </button>
            <button
              onClick={() => navigate('/student/analytics/competitions')}
              className="px-4 py-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-900/40 transition-colors flex items-center"
            >
              <FiAward className="w-4 h-4 mr-2" />
              Competitions
              <FiArrowRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        </div>
      </div>

      {/* Performance Overview */}
      <PerformanceOverview
        data={analytics}
        loading={comprehensiveLoading}
        className="mb-8"
      />

      {/* Analytics Insights */}
      <AnalyticsInsights
        data={analytics}
        loading={comprehensiveLoading}
        className="mb-8"
      />

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <button
          onClick={() => navigate('/student/analytics/subjects')}
          className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 text-left hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <FiBookOpen className="w-8 h-8 text-blue-600 dark:text-blue-400 mb-3" />
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-2">
            Subject Analytics
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Detailed performance analysis by subject
          </p>
        </button>

        <button
          onClick={() => navigate('/student/analytics/classroom')}
          className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6 text-left hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <FiUsers className="w-8 h-8 text-green-600 dark:text-green-400 mb-3" />
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-2">
            Classroom Engagement
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Participation and engagement metrics
          </p>
        </button>

        <button
          onClick={() => navigate('/student/analytics/competitions')}
          className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6 text-left hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
        >
          <FiAward className="w-8 h-8 text-yellow-600 dark:text-yellow-400 mb-3" />
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-2">
            Competition Results
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Track your competition performance
          </p>
        </button>
      </div>
    </FluidPageContainer>
  );
};

export default StudentAnalyticsDashboard;
