/**
 * Upcoming Schedule Component
 * 
 * Displays student's upcoming schedule including:
 * - Classes and lectures
 * - Exams and tests
 * - Assignment deadlines
 * - Lab sessions
 */

import React from 'react';
import { 
  FiCalendar, 
  FiClock, 
  FiMapPin, 
  FiBookOpen,
  FiUsers,
  FiFileText,
  FiAlertCircle
} from 'react-icons/fi';
import { Card } from '../ui/layout';
import { format, isToday, isTomorrow, parseISO } from 'date-fns';

const UpcomingSchedule = ({ schedule = [], loading = false }) => {
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (!schedule || schedule.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Upcoming Schedule
        </h3>
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <FiCalendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No upcoming events scheduled</p>
        </div>
      </Card>
    );
  }

  const getEventIcon = (type) => {
    switch (type) {
      case 'class':
      case 'lecture':
        return FiUsers;
      case 'exam':
      case 'test':
        return FiFileText;
      case 'lab':
      case 'practical':
        return FiBookOpen;
      case 'assignment':
      case 'deadline':
        return FiAlertCircle;
      default:
        return FiCalendar;
    }
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'class':
      case 'lecture':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'exam':
      case 'test':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'lab':
      case 'practical':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'assignment':
      case 'deadline':
        return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const formatEventDate = (startTime) => {
    try {
      const date = parseISO(startTime);
      
      if (isToday(date)) {
        return 'Today';
      } else if (isTomorrow(date)) {
        return 'Tomorrow';
      } else {
        return format(date, 'MMM d');
      }
    } catch (error) {
      return 'TBD';
    }
  };

  const formatEventTime = (startTime, endTime) => {
    try {
      const start = parseISO(startTime);
      const timeFormat = 'h:mm a';
      
      if (endTime) {
        const end = parseISO(endTime);
        return `${format(start, timeFormat)} - ${format(end, timeFormat)}`;
      } else {
        return format(start, timeFormat);
      }
    } catch (error) {
      return 'Time TBD';
    }
  };

  const sortedSchedule = [...schedule].sort((a, b) => {
    try {
      return new Date(a.start_time) - new Date(b.start_time);
    } catch (error) {
      return 0;
    }
  });

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Upcoming Schedule
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Next {sortedSchedule.length} events
        </span>
      </div>

      <div className="space-y-4">
        {sortedSchedule.map((event, index) => {
          const IconComponent = getEventIcon(event.type);
          const colorClasses = getEventColor(event.type);
          const eventDate = formatEventDate(event.start_time);
          const eventTime = formatEventTime(event.start_time, event.end_time);

          return (
            <div key={event.id || index} className="flex items-start gap-4 p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm transition-all duration-200">
              {/* Date/Time Column */}
              <div className="flex-shrink-0 text-center min-w-[4rem]">
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {eventDate}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {eventTime}
                </div>
              </div>

              {/* Icon */}
              <div className={`p-2 rounded-lg ${colorClasses} flex-shrink-0`}>
                <IconComponent className="w-5 h-5" />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                      {event.title}
                    </h4>
                    
                    {event.subject && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {event.subject}
                      </p>
                    )}

                    {/* Location */}
                    {event.location && (
                      <div className="flex items-center gap-1 mt-2">
                        <FiMapPin className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {event.location}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Event Type Badge */}
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorClasses} ml-3 capitalize`}>
                    {event.type}
                  </span>
                </div>

                {/* Additional Info */}
                {(event.classroom_id || event.instructor) && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {event.instructor && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                        {event.instructor}
                      </span>
                    )}
                    {event.classroom_id && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                        Classroom: {event.classroom_id}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* View Full Calendar */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button className="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors flex items-center justify-center gap-2">
          <FiCalendar className="w-4 h-4" />
          View Full Calendar
        </button>
      </div>
    </Card>
  );
};

export default UpcomingSchedule;
