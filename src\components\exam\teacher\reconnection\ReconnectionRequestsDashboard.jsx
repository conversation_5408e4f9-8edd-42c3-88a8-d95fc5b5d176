/**
 * Reconnection Requests Dashboard
 * Allows teachers to view and manage student reconnection requests
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../../../providers/ThemeContext';
import { useNotification } from '../../../../contexts/NotificationContext';
import {
  getPendingReconnectionRequests,
  approveReconnectionRequest,
  selectPendingRequests,
  selectReconnectionState
} from '../../../../store/slices/exam/examReconnectionSlice';
import {
  FiClock,
  FiUser,
  FiWifi,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiEye,
  FiAlertCircle,
  FiCalendar
} from 'react-icons/fi';

const ReconnectionRequestsDashboard = () => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { showSuccess, showError } = useNotification();
  
  const pendingRequests = useSelector(selectPendingRequests);
  const reconnectionState = useSelector(selectReconnectionState);
  
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [approvalReason, setApprovalReason] = useState('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalAction, setApprovalAction] = useState(null); // 'approve' or 'deny'
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Theme-based styling
  const isDark = currentTheme === 'dark';
  const bgColor = isDark ? 'bg-gray-800' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-gray-900';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';

  // Load pending requests on mount
  useEffect(() => {
    dispatch(getPendingReconnectionRequests());
  }, [dispatch]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        dispatch(getPendingReconnectionRequests());
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, dispatch]);

  // Handle manual refresh
  const handleRefresh = () => {
    dispatch(getPendingReconnectionRequests());
  };

  // Handle approve/deny action
  const handleAction = (request, action) => {
    setSelectedRequest(request);
    setApprovalAction(action);
    setApprovalReason('');
    setShowApprovalModal(true);
  };

  // Submit approval/denial
  const handleSubmitAction = async () => {
    if (!selectedRequest || !approvalAction) return;

    try {
      await dispatch(approveReconnectionRequest({
        requestId: selectedRequest.request_id,
        approved: approvalAction === 'approve',
        reason: approvalReason.trim() || (approvalAction === 'approve' ? 'Approved' : 'Denied')
      })).unwrap();

      showSuccess(`Reconnection request ${approvalAction}d successfully`);
      setShowApprovalModal(false);
      setSelectedRequest(null);
      setApprovalReason('');
      
      // Refresh the list
      dispatch(getPendingReconnectionRequests());
    } catch (error) {
      showError(error.message || `Failed to ${approvalAction} request`);
    }
  };

  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const requestTime = new Date(timestamp);
    const diffMs = now - requestTime;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className={`${bgColor} rounded-xl shadow-sm border ${borderColor}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FiWifi className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className={`text-xl font-semibold ${textColor}`}>
                Reconnection Requests
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage student reconnection requests
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <label className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded"
              />
              Auto-refresh
            </label>
            
            <button
              onClick={handleRefresh}
              disabled={reconnectionState.loading}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <FiRefreshCw className={`w-4 h-4 ${reconnectionState.loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {reconnectionState.loading && pendingRequests.length === 0 ? (
          <div className="text-center py-8">
            <FiRefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">Loading requests...</p>
          </div>
        ) : pendingRequests.length === 0 ? (
          <div className="text-center py-8">
            <FiCheck className="w-8 h-8 text-green-500 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No pending reconnection requests</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingRequests.map((request) => (
              <div
                key={request.request_id}
                className={`border ${borderColor} rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <FiUser className="w-4 h-4 text-gray-500" />
                      <span className={`font-medium ${textColor}`}>
                        Student ID: {request.student_id}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {request.exam_id}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300 mb-3">
                      <div className="flex items-center gap-1">
                        <FiClock className="w-3 h-3" />
                        {formatTimeAgo(request.requested_at)}
                      </div>
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3" />
                        {new Date(request.requested_at).toLocaleString()}
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        <span className="font-medium">Reason:</span> {request.reason}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-1 text-xs text-yellow-600 dark:text-yellow-400">
                      <FiAlertCircle className="w-3 h-3" />
                      Status: {request.status}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => handleAction(request, 'approve')}
                      className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center gap-1 text-sm"
                    >
                      <FiCheck className="w-3 h-3" />
                      Approve
                    </button>
                    <button
                      onClick={() => handleAction(request, 'deny')}
                      className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 flex items-center gap-1 text-sm"
                    >
                      <FiX className="w-3 h-3" />
                      Deny
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Approval Modal */}
      {showApprovalModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${bgColor} rounded-xl shadow-2xl max-w-md w-full border ${borderColor}`}>
            <div className="p-6">
              <h3 className={`text-lg font-semibold ${textColor} mb-4`}>
                {approvalAction === 'approve' ? 'Approve' : 'Deny'} Reconnection Request
              </h3>
              
              <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">Student:</span> {selectedRequest.student_id}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">Reason:</span> {selectedRequest.reason}
                </p>
              </div>
              
              <div className="mb-4">
                <label className={`block text-sm font-medium ${textColor} mb-2`}>
                  {approvalAction === 'approve' ? 'Approval' : 'Denial'} Reason (Optional)
                </label>
                <textarea
                  value={approvalReason}
                  onChange={(e) => setApprovalReason(e.target.value)}
                  placeholder={`Provide a reason for ${approvalAction === 'approve' ? 'approving' : 'denying'} this request...`}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    isDark 
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                  rows={3}
                />
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={handleSubmitAction}
                  disabled={reconnectionState.loading}
                  className={`flex-1 px-4 py-2 text-white rounded-lg disabled:opacity-50 ${
                    approvalAction === 'approve' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {reconnectionState.loading ? 'Processing...' : `${approvalAction === 'approve' ? 'Approve' : 'Deny'} Request`}
                </button>
                <button
                  onClick={() => setShowApprovalModal(false)}
                  className={`px-4 py-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 ${borderColor} ${textColor}`}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReconnectionRequestsDashboard;
