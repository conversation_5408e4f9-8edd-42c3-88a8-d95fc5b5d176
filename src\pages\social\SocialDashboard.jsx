import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiU<PERSON><PERSON>, <PERSON><PERSON>ser<PERSON><PERSON><PERSON>, <PERSON>H<PERSON>t, <PERSON><PERSON><PERSON>dingUp, FiSearch } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';
import FollowSuggestions from '../../components/social/FollowSuggestions';
import UserSocialStats from '../../components/social/UserSocialStats';
import { PageContainer, PageHeader } from '../../components/ui/layout';

/**
 * SocialDashboard Component
 * Main social hub with follow suggestions, stats, and social features
 */
const SocialDashboard = () => {
  const navigate = useNavigate();
  const [userStats, setUserStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const currentUserId = localStorage.getItem('userId');

  useEffect(() => {
    if (currentUserId) {
      loadUserStats();
    }
  }, [currentUserId]);

  const loadUserStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const stats = await socialFollowService.getFollowStats(currentUserId);
      setUserStats(stats);
    } catch (err) {
      console.error('Failed to load user stats:', err);
      setError(err.message || 'Failed to load social data');
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionFollowed = (user, userId) => {
    // Update stats optimistically
    if (userStats) {
      setUserStats(prev => ({
        ...prev,
        following_count: prev.following_count + 1
      }));
    }
  };

  const navigateToFollowers = () => {
    const userRole = localStorage.getItem('role') || 'student';
    navigate(`/${userRole}/social/followers`);
  };

  const navigateToFollowing = () => {
    const userRole = localStorage.getItem('role') || 'student';
    navigate(`/${userRole}/social/following`);
  };

  const navigateToSearch = () => {
    const userRole = localStorage.getItem('role') || 'student';
    navigate(`/${userRole}/social/search`);
  };

  return (
    <PageContainer>
      <PageHeader
        title="Social Hub"
        subtitle="Connect with other learners and educators"
        actions={[
          {
            label: 'Search People',
            icon: FiSearch,
            onClick: navigateToSearch,
            variant: 'primary'
          }
        ]}
      />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Your Social Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Your Social Network
              </h2>
              
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-pulse flex space-x-8">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded"></div>
                      <div className="space-y-1">
                        <div className="w-12 h-4 bg-gray-300 rounded"></div>
                        <div className="w-16 h-3 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded"></div>
                      <div className="space-y-1">
                        <div className="w-12 h-4 bg-gray-300 rounded"></div>
                        <div className="w-16 h-3 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-red-600 dark:text-red-400">{error}</p>
                  <button
                    onClick={loadUserStats}
                    className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : userStats ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {/* Followers */}
                  <button
                    onClick={navigateToFollowers}
                    className="text-center p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <FiUsers className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userStats.followers_count || 0}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Followers
                    </div>
                  </button>

                  {/* Following */}
                  <button
                    onClick={navigateToFollowing}
                    className="text-center p-4 rounded-lg bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                  >
                    <FiUserCheck className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userStats.following_count || 0}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Following
                    </div>
                  </button>

                  {/* Mutual Follows */}
                  <div className="text-center p-4 rounded-lg bg-purple-50 dark:bg-purple-900/20">
                    <FiHeart className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userStats.mutual_follows_count || 0}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Mutual
                    </div>
                  </div>
                </div>
              ) : null}
            </div>

            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={navigateToSearch}
                  className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <FiSearch className="w-6 h-6 text-blue-600" />
                  <div className="text-left">
                    <div className="font-semibold text-gray-900 dark:text-white">
                      Find People
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Search for users to follow
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => navigate('/social/trending')}
                  className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <FiTrendingUp className="w-6 h-6 text-green-600" />
                  <div className="text-left">
                    <div className="font-semibold text-gray-900 dark:text-white">
                      Trending
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      See what's popular
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Follow Suggestions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <FollowSuggestions
                limit={5}
                layout="card"
                showHeader={true}
                showRefresh={true}
                onSuggestionFollowed={handleSuggestionFollowed}
              />
            </div>

            {/* Social Tips */}
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                💡 Social Tips
              </h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Follow educators in your field of interest</li>
                <li>• Connect with classmates and study partners</li>
                <li>• Engage with content from people you follow</li>
                <li>• Share your learning journey with others</li>
              </ul>
            </div>
          </div>
        </div>
    </PageContainer>
  );
};

export default SocialDashboard;
