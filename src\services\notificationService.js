/**
 * Notification Service
 * 
 * Handles all notification-related API operations including:
 * - Fetching user notifications with pagination and filtering
 * - Marking notifications as read (single and bulk)
 * - Deleting notifications (single and bulk)
 * - Creating notifications (admin/teacher only)
 * - Getting notification statistics and dashboard data
 */

import axios from 'axios';
import { BASE_API } from '../utils/api/API_URL';
import { handleApiError } from '../utils/helpers/errorHandler';
import logger from '../utils/helpers/logger';

// Base URL for notification endpoints
const NOTIFICATION_API_BASE = `${BASE_API}/api/notifications`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Helper function to create auth headers
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${getAuthToken()}`,
  'Content-Type': 'application/json'
});

/**
 * Notification Service Class
 */
class NotificationService {
  constructor() {
    this.baseUrl = NOTIFICATION_API_BASE;
  }

  /**
   * Get user notifications with pagination and filtering
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (default: 1)
   * @param {number} params.page_size - Items per page (default: 20)
   * @param {boolean} params.unread_only - Show only unread notifications
   * @param {string} params.notification_type - Filter by notification type
   * @returns {Promise<Object>} Paginated notifications response
   */
  async getNotifications(params = {}) {
    try {
      const queryParams = new URLSearchParams({
        page: params.page || 1,
        page_size: params.page_size || 20,
        unread_only: params.unread_only || false,
        ...(params.notification_type && { notification_type: params.notification_type })
      });

      logger.info('Fetching notifications', { params }, 'NotificationService');
      
      const response = await axios.get(
        `${this.baseUrl}?${queryParams}`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notifications fetched successfully', { 
        count: response.data.notifications?.length,
        total: response.data.total_count,
        unread: response.data.unread_count
      }, 'NotificationService');
      
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch notifications', { error: error.message, params }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Get a specific notification by ID
   * @param {string} notificationId - Notification UUID
   * @returns {Promise<Object>} Notification details
   */
  async getNotificationById(notificationId) {
    try {
      logger.info('Fetching notification by ID', { notificationId }, 'NotificationService');
      
      const response = await axios.get(
        `${this.baseUrl}/${notificationId}`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification fetched successfully', { notificationId }, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch notification', { error: error.message, notificationId }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Mark a notification as read
   * @param {string} notificationId - Notification UUID
   * @returns {Promise<string>} Success message
   */
  async markAsRead(notificationId) {
    try {
      logger.info('Marking notification as read', { notificationId }, 'NotificationService');
      
      const response = await axios.put(
        `${this.baseUrl}/${notificationId}/read`,
        {},
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification marked as read', { notificationId }, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to mark notification as read', { error: error.message, notificationId }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Mark all notifications as read
   * @returns {Promise<string>} Success message
   */
  async markAllAsRead() {
    try {
      logger.info('Marking all notifications as read', {}, 'NotificationService');
      
      const response = await axios.put(
        `${this.baseUrl}/read-all`,
        {},
        { headers: getAuthHeaders() }
      );
      
      logger.info('All notifications marked as read', {}, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to mark all notifications as read', { error: error.message }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Delete a notification
   * @param {string} notificationId - Notification UUID
   * @returns {Promise<string>} Success message
   */
  async deleteNotification(notificationId) {
    try {
      logger.info('Deleting notification', { notificationId }, 'NotificationService');
      
      const response = await axios.delete(
        `${this.baseUrl}/${notificationId}`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification deleted successfully', { notificationId }, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to delete notification', { error: error.message, notificationId }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Create a notification (admin/teacher only)
   * @param {Object} notificationData - Notification data
   * @param {string} notificationData.title - Notification title
   * @param {string} notificationData.message - Notification message
   * @param {string} notificationData.notification_type - Notification type
   * @param {string} notificationData.user_id - Target user ID
   * @param {string} notificationData.related_id - Related entity ID (optional)
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(notificationData) {
    try {
      logger.info('Creating notification', { notificationData }, 'NotificationService');
      
      const response = await axios.post(
        `${this.baseUrl}/create`,
        notificationData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification created successfully', { 
        notificationId: response.data.id 
      }, 'NotificationService');
      
      return response.data;
    } catch (error) {
      logger.error('Failed to create notification', { error: error.message, notificationData }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Bulk mark notifications as read
   * @param {string[]} notificationIds - Array of notification UUIDs
   * @returns {Promise<string>} Success message
   */
  async bulkMarkAsRead(notificationIds) {
    try {
      logger.info('Bulk marking notifications as read', { count: notificationIds.length }, 'NotificationService');
      
      const response = await axios.post(
        `${this.baseUrl}/bulk/read`,
        { notification_ids: notificationIds },
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notifications bulk marked as read', { count: notificationIds.length }, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to bulk mark notifications as read', { error: error.message, count: notificationIds.length }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Bulk delete notifications
   * @param {string[]} notificationIds - Array of notification UUIDs
   * @returns {Promise<string>} Success message
   */
  async bulkDeleteNotifications(notificationIds) {
    try {
      logger.info('Bulk deleting notifications', { count: notificationIds.length }, 'NotificationService');
      
      const response = await axios.post(
        `${this.baseUrl}/bulk/delete`,
        { notification_ids: notificationIds },
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notifications bulk deleted', { count: notificationIds.length }, 'NotificationService');
      return response.data;
    } catch (error) {
      logger.error('Failed to bulk delete notifications', { error: error.message, count: notificationIds.length }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Create bulk notifications for multiple users (admin/teacher only)
   * @param {Object} bulkData - Bulk notification data
   * @param {string[]} bulkData.user_ids - Array of user UUIDs
   * @param {string} bulkData.title - Notification title
   * @param {string} bulkData.message - Notification message
   * @param {string} bulkData.notification_type - Notification type
   * @param {string} bulkData.related_id - Related entity ID (optional)
   * @returns {Promise<Object>} Bulk operation result
   */
  async createBulkNotifications(bulkData) {
    try {
      logger.info('Creating bulk notifications', { userCount: bulkData.user_ids.length }, 'NotificationService');
      
      const response = await axios.post(
        `${this.baseUrl}/bulk/create`,
        bulkData,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Bulk notifications created', { 
        total: response.data.total_processed,
        success: response.data.success_count,
        failed: response.data.failure_count
      }, 'NotificationService');
      
      return response.data;
    } catch (error) {
      logger.error('Failed to create bulk notifications', { error: error.message, userCount: bulkData.user_ids.length }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Get notification statistics
   * @returns {Promise<Object>} Notification statistics
   */
  async getNotificationStats() {
    try {
      logger.info('Fetching notification statistics', {}, 'NotificationService');
      
      const response = await axios.get(
        `${this.baseUrl}/stats/summary`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification statistics fetched', { 
        total: response.data.total_notifications,
        unread: response.data.unread_notifications
      }, 'NotificationService');
      
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch notification statistics', { error: error.message }, 'NotificationService');
      throw handleApiError(error);
    }
  }

  /**
   * Get notification dashboard data
   * @returns {Promise<Object>} Dashboard data with overview and recent notifications
   */
  async getNotificationDashboard() {
    try {
      logger.info('Fetching notification dashboard data', {}, 'NotificationService');
      
      const response = await axios.get(
        `${this.baseUrl}/dashboard`,
        { headers: getAuthHeaders() }
      );
      
      logger.info('Notification dashboard data fetched', { 
        total: response.data.total_notifications,
        unread: response.data.unread_notifications,
        recent: response.data.recent_notifications?.length
      }, 'NotificationService');
      
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch notification dashboard data', { error: error.message }, 'NotificationService');
      throw handleApiError(error);
    }
  }
}

// Create and export a singleton instance
const notificationService = new NotificationService();
export default notificationService;

// Export the class for testing purposes
export { NotificationService };
