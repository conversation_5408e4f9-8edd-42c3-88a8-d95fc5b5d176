// Event category constants matching backend EventCategoryEnum
export const EVENT_CATEGORIES = {
  WORKSHOP: 'WORKSHOP',
  CONFERENCE: 'CONFERENCE', 
  WEBINAR: 'WEBINAR',
  COMPETITION: 'COMPETITION'
};

// Array for dropdown options
export const EVENT_CATEGORY_OPTIONS = [
  { value: EVENT_CATEGORIES.WORKSHOP, label: 'Workshop' },
  { value: EVENT_CATEGORIES.CONFERENCE, label: 'Conference' },
  { value: EVENT_CATEGORIES.WEBINAR, label: 'Webinar' },
  { value: EVENT_CATEGORIES.COMPETITION, label: 'Competition' }
];

// Helper function to get category label
export const getCategoryLabel = (category) => {
  const option = EVENT_CATEGORY_OPTIONS.find(opt => opt.value === category);
  return option ? option.label : category;
};

// Helper function to validate category
export const isValidCategory = (category) => {
  return Object.values(EVENT_CATEGORIES).includes(category);
};
