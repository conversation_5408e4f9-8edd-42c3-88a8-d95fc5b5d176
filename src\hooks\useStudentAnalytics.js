import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchSubjectAnalytics,
  fetchClassGradeAnalytics,
  fetchClassroomAnalytics,
  fetchCompetitionAnalytics,
  fetchComprehensiveAnalytics,
  fetchAllAnalytics,
  clearAllErrors,
  clearAllAnalytics
} from '../store/slices/StudentAnalyticsSlice';

/**
 * Custom hook for managing student analytics data
 * Provides easy access to analytics data and actions
 */
export const useStudentAnalytics = (options = {}) => {
  const {
    autoFetch = true,
    fetchAll = false,
    dateRange = {
      start_date: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1).toISOString(),
      end_date: new Date().toISOString(),
      period_type: 'monthly'
    }
  } = options;

  const dispatch = useDispatch();

  // Get analytics state from Redux
  const {
    // Subject Analytics
    subjectAnalytics,
    subjectLoading,
    subjectError,
    subjectLastUpdated,

    // Class/Grade Analytics
    classGradeAnalytics,
    classGradeLoading,
    classGradeError,
    classGradeLastUpdated,

    // Classroom Analytics
    classroomAnalytics,
    classroomLoading,
    classroomError,
    classroomLastUpdated,

    // Competition Analytics
    competitionAnalytics,
    competitionLoading,
    competitionError,
    competitionLastUpdated,

    // Comprehensive Analytics
    comprehensiveAnalytics,
    comprehensiveLoading,
    comprehensiveError,
    comprehensiveLastUpdated,

    // Global state
    isLoading,
    hasErrors,
    lastGlobalUpdate
  } = useSelector(state => state.studentAnalytics);

  // Fetch functions
  const fetchSubjectData = useCallback((params = dateRange) => {
    return dispatch(fetchSubjectAnalytics(params));
  }, [dispatch, dateRange]);

  const fetchClassGradeData = useCallback((params = dateRange) => {
    return dispatch(fetchClassGradeAnalytics(params));
  }, [dispatch, dateRange]);

  const fetchClassroomData = useCallback((params = dateRange) => {
    return dispatch(fetchClassroomAnalytics(params));
  }, [dispatch, dateRange]);

  const fetchCompetitionData = useCallback((params = dateRange) => {
    return dispatch(fetchCompetitionAnalytics(params));
  }, [dispatch, dateRange]);

  const fetchComprehensiveData = useCallback((params = dateRange) => {
    return dispatch(fetchComprehensiveAnalytics(params));
  }, [dispatch, dateRange]);

  const fetchAllData = useCallback((params = dateRange) => {
    return dispatch(fetchAllAnalytics(params));
  }, [dispatch, dateRange]);

  // Refresh functions
  const refreshSubjectData = useCallback(() => {
    return fetchSubjectData();
  }, [fetchSubjectData]);

  const refreshClassGradeData = useCallback(() => {
    return fetchClassGradeData();
  }, [fetchClassGradeData]);

  const refreshClassroomData = useCallback(() => {
    return fetchClassroomData();
  }, [fetchClassroomData]);

  const refreshCompetitionData = useCallback(() => {
    return fetchCompetitionData();
  }, [fetchCompetitionData]);

  const refreshComprehensiveData = useCallback(() => {
    return fetchComprehensiveData();
  }, [fetchComprehensiveData]);

  const refreshAllData = useCallback(() => {
    return fetchAllData();
  }, [fetchAllData]);

  // Clear functions
  const clearErrors = useCallback(() => {
    dispatch(clearAllErrors());
  }, [dispatch]);

  const clearData = useCallback(() => {
    dispatch(clearAllAnalytics());
  }, [dispatch]);

  // Auto-fetch data on mount - FIXED to prevent infinite calls
  useEffect(() => {
    if (autoFetch) {
      if (fetchAll) {
        fetchAllData();
      } else {
        // Fetch comprehensive analytics by default
        fetchComprehensiveData();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoFetch, fetchAll]); // Removed function dependencies to prevent infinite calls

  // Computed values
  const loading = {
    subject: subjectLoading,
    classGrade: classGradeLoading,
    classroom: classroomLoading,
    competition: competitionLoading,
    comprehensive: comprehensiveLoading,
    any: isLoading,
    all: subjectLoading && classGradeLoading && classroomLoading && competitionLoading && comprehensiveLoading
  };

  const errors = {
    subject: subjectError,
    classGrade: classGradeError,
    classroom: classroomError,
    competition: competitionError,
    comprehensive: comprehensiveError,
    any: hasErrors,
    list: [subjectError, classGradeError, classroomError, competitionError, comprehensiveError].filter(Boolean)
  };

  const lastUpdated = {
    subject: subjectLastUpdated,
    classGrade: classGradeLastUpdated,
    classroom: classroomLastUpdated,
    competition: competitionLastUpdated,
    comprehensive: comprehensiveLastUpdated,
    global: lastGlobalUpdate,
    mostRecent: [
      subjectLastUpdated,
      classGradeLastUpdated,
      classroomLastUpdated,
      competitionLastUpdated,
      comprehensiveLastUpdated
    ].filter(Boolean).sort().pop()
  };

  const hasData = {
    subject: !!subjectAnalytics,
    classGrade: !!classGradeAnalytics,
    classroom: !!classroomAnalytics,
    competition: !!competitionAnalytics,
    comprehensive: !!comprehensiveAnalytics,
    any: !!(subjectAnalytics || classGradeAnalytics || classroomAnalytics || competitionAnalytics || comprehensiveAnalytics),
    all: !!(subjectAnalytics && classGradeAnalytics && classroomAnalytics && competitionAnalytics && comprehensiveAnalytics)
  };

  // Helper functions
  const getOverallPerformance = useCallback(() => {
    if (comprehensiveAnalytics) {
      return comprehensiveAnalytics.overall_performance_score || 0;
    }
    if (subjectAnalytics) {
      const subjects = subjectAnalytics.subjects || [];
      if (subjects.length > 0) {
        const totalScore = subjects.reduce((sum, subject) => sum + (subject.average_score || 0), 0);
        return totalScore / subjects.length;
      }
    }
    return 0;
  }, [comprehensiveAnalytics, subjectAnalytics]);

  const getTopSubject = useCallback(() => {
    if (comprehensiveAnalytics?.subject_analytics?.strongest_subject) {
      return comprehensiveAnalytics.subject_analytics.strongest_subject;
    }
    if (subjectAnalytics?.strongest_subject) {
      return subjectAnalytics.strongest_subject;
    }
    return null;
  }, [comprehensiveAnalytics, subjectAnalytics]);

  const getClassRank = useCallback(() => {
    if (comprehensiveAnalytics?.class_grade_analytics?.class_performance?.[0]) {
      return comprehensiveAnalytics.class_grade_analytics.class_performance[0].student_rank;
    }
    if (classGradeAnalytics?.class_performance?.[0]) {
      return classGradeAnalytics.class_performance[0].student_rank;
    }
    return null;
  }, [comprehensiveAnalytics, classGradeAnalytics]);

  return {
    // Data
    data: {
      subject: subjectAnalytics,
      classGrade: classGradeAnalytics,
      classroom: classroomAnalytics,
      competition: competitionAnalytics,
      comprehensive: comprehensiveAnalytics
    },

    // Status
    loading,
    errors,
    lastUpdated,
    hasData,

    // Fetch functions
    fetch: {
      subject: fetchSubjectData,
      classGrade: fetchClassGradeData,
      classroom: fetchClassroomData,
      competition: fetchCompetitionData,
      comprehensive: fetchComprehensiveData,
      all: fetchAllData
    },

    // Refresh functions
    refresh: {
      subject: refreshSubjectData,
      classGrade: refreshClassGradeData,
      classroom: refreshClassroomData,
      competition: refreshCompetitionData,
      comprehensive: refreshComprehensiveData,
      all: refreshAllData
    },

    // Actions
    clearErrors,
    clearData,

    // Helper functions
    getOverallPerformance,
    getTopSubject,
    getClassRank
  };
};

export default useStudentAnalytics;
