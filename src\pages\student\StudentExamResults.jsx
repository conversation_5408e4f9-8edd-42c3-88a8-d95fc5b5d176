import React, { useEffect, useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useThemeProvider } from "../../providers/ThemeContext";
import {
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiArrowLeft,
  FiDownload,
  FiShare2,
  FiEye,
  FiEyeOff,
  FiStar,
  FiMessageSquare,
  FiTarget,
  FiBookOpen
} from "react-icons/fi";
import { getAIResults, checkExamWithAI, waitForAIResults, selectAIResults, selectAIChecking } from "../../store/slices/exam/aiCheckingSlice";

function StudentExamResults() {
  const { examId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  const [showAnswers, setShowAnswers] = useState(false);
  const [loading, setLoading] = useState(true);
  const [examResult, setExamResult] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [generatingAI, setGeneratingAI] = useState(false);
  const [aiGenerationAttempted, setAiGenerationAttempted] = useState(false);
  const [isDisqualified, setIsDisqualified] = useState(false);
  const [disqualificationReason, setDisqualificationReason] = useState(null);

  // Get data from navigation state
  const { aiResults, attemptId, submissionResult, examId: stateExamId } = location.state || {};

  // Extract exam ID from URL path as fallback (similar to StudentTakeExam.jsx)
  const pathParts = location.pathname.split('/');

  // Look for exam ID in the URL pattern: /student/exam-results/{examId}
  let finalExamId = examId;
  if (!finalExamId && pathParts[1] === 'student' && pathParts[2] === 'exam-results' && pathParts[3]) {
    finalExamId = pathParts[3];
  }

  // Use examId from state, URL params, or extracted from path
  const currentExamId = stateExamId || finalExamId;

  // Validate exam ID before proceeding
  if (!currentExamId || currentExamId === 'undefined' || currentExamId === 'null') {

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FiXCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Exam ID</h2>
          <p className="text-gray-600 mb-4">
            Unable to load exam results. The exam ID is missing or invalid.
          </p>
          <button
            onClick={() => navigate('/student/dashboard')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Get AI results from Redux store
  const aiResultsFromStore = useSelector(selectAIResults(currentExamId));
  const aiCheckingState = useSelector(selectAIChecking);
  const isLoadingAI = aiCheckingState.loading;

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";

  // Auto-generate AI results if not found
  const autoGenerateAIResults = async () => {
    if (!currentExamId || generatingAI || aiGenerationAttempted) {
      return null;
    }

    setGeneratingAI(true);
    setAiGenerationAttempted(true);

    try {
      // Step 1: Trigger AI checking
      const checkResult = await dispatch(checkExamWithAI({
        examId: currentExamId,
        studentId: null // null for student endpoint
      })).unwrap();

      // Check if student is disqualified
      if (checkResult.result && checkResult.result.is_disqualified) {
        setGeneratingAI(false);
        return { isDisqualified: true, disqualificationReason: checkResult.result.disqualification_reason };
      }

      // Step 2: Wait for AI results with polling
      const aiResults = await dispatch(waitForAIResults({
        examId: currentExamId,
        studentId: null,
        maxWaitTime: 120000, // 2 minutes
        pollInterval: 3000 // 3 seconds
      })).unwrap();

      setGeneratingAI(false);
      return aiResults;

    } catch (error) {
      setGeneratingAI(false);
      return null;
    }
  };

  // Function to refresh AI results
  const refreshAIResults = async () => {
    if (!currentExamId) return;

    setRefreshing(true);
    try {
      const fetchedResults = await dispatch(getAIResults({
        examId: currentExamId,
        studentId: null // null for student endpoint
      })).unwrap();
    } catch (error) {
      // Handle error silently
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    // Load real exam results and AI feedback
    const loadResults = async () => {
      setLoading(true);

      try {
        // First, try to fetch AI results from API if not already available
        let finalAIResults = aiResults || aiResultsFromStore;

        if (!finalAIResults && currentExamId) {
          try {
            // Dispatch action to fetch AI results for this exam
            const fetchedResults = await dispatch(getAIResults({
              examId: currentExamId,
              studentId: null // null for student endpoint
            })).unwrap();
            finalAIResults = fetchedResults;
          } catch (error) {
            // AUTO-GENERATE AI RESULTS IF NOT FOUND (404 or no data)
            if (error.includes('404') || error.includes('not found') || error.includes('No results found')) {
              try {
                const generatedResults = await autoGenerateAIResults();
                if (generatedResults) {
                  finalAIResults = generatedResults;
                }
              } catch (generateError) {
                // Handle error silently
              }
            }
          }
        }

        let examResult;

        if (finalAIResults && finalAIResults.result) {
          // Check if student is disqualified
          const aiData = finalAIResults.result;

          if (aiData.is_disqualified || finalAIResults.is_disqualified) {
            setIsDisqualified(true);
            setDisqualificationReason(aiData.disqualification_reason || finalAIResults.disqualification_reason || 'Student was disqualified from this exam');

            // Create minimal exam result for disqualified student
            examResult = {
              id: currentExamId,
              title: location.state?.examTitle || aiData.exam_title || "Exam",
              subject: aiData.subject || "Unknown Subject",
              totalMarks: aiData.max_total_score || 0,
              obtainedMarks: 0,
              percentage: 0,
              grade: "DQ", // Disqualified
              status: "disqualified",
              submittedAt: aiData.checked_at || new Date().toISOString(),
              timeTaken: aiData.time_taken || "Unknown",
              autoSubmitted: location.state?.autoSubmitted || false,
              questions: [],
              classAverage: 0,
              rank: 0,
              totalStudents: 0,
              isDisqualified: true,
              disqualificationReason: aiData.disqualification_reason || finalAIResults.disqualification_reason
            };
          } else {
            // Map the new AI results structure
            const questionFeedbacks = aiData.question_feedbacks || [];

            // Transform question_feedbacks into questions format
            const transformedQuestions = questionFeedbacks.map(qf => ({
              id: qf.question_id,
              number: qf.question_number,
              text: qf.question_text,
              aiScore: qf.ai_score,
              maxScore: qf.max_score,
              feedback: qf.feedback,
              suggestions: qf.suggestions,
              isCorrect: qf.ai_score === qf.max_score,
              studentAnswer: qf.student_attempted_answer
            }));

            // Use AI results if available and student is not disqualified
            examResult = {
              id: currentExamId,
              title: location.state?.examTitle || aiData.exam_title || "Exam",
              subject: aiData.subject || "Unknown Subject",
              totalMarks: aiData.max_total_score || 0,
              obtainedMarks: aiData.total_score || 0,
              percentage: Math.round(aiData.percentage || 0),
              grade: calculateGrade(aiData.percentage || 0),
              status: aiData.percentage >= 50 ? "passed" : "failed",
              submittedAt: aiData.checked_at || new Date().toISOString(),
              timeTaken: aiData.time_taken || "Unknown",
              autoSubmitted: location.state?.autoSubmitted || false,
              // AI-specific data
              aiEvaluation: {
                overallScore: aiData.percentage,
                strengths: aiData.strengths || [],
                improvements: aiData.improvements || [],
                feedback: aiData.feedback || "AI evaluation completed successfully.",
                detailedAnalysis: aiData.detailed_analysis || {},
                questionFeedbacks: questionFeedbacks
              },
              // Use transformed questions from AI results
              questions: transformedQuestions,
              classAverage: aiData.class_average || 0,
              rank: aiData.rank || 0,
              totalStudents: aiData.total_students || 0
            };
          }
        } else {
          // No AI results available - check if we should auto-generate
          if (!aiGenerationAttempted && currentExamId) {
            // Trigger auto-generation in the background
            autoGenerateAIResults().then((generatedResults) => {
              if (generatedResults) {
                // The useEffect will trigger a reload when aiResultsFromStore changes
              }
            });
          }

          // Fallback to basic exam data with generation status
          examResult = {
            id: currentExamId,
            title: location.state?.examTitle || "Exam",
            subject: location.state?.subject || "Unknown Subject",
            totalMarks: 100,
            obtainedMarks: 0,
            percentage: 0,
            grade: generatingAI ? "Generating..." : "N/A",
            status: generatingAI ? "generating" : "pending",
            submittedAt: location.state?.submissionTime || new Date().toISOString(),
            timeTaken: "Unknown",
            autoSubmitted: location.state?.autoSubmitted || false,
            questions: [],
            classAverage: 0,
            rank: 0,
            totalStudents: 0,
            aiEvaluation: generatingAI ? {
              overallScore: 0,
              strengths: [],
              improvements: [],
              feedback: "🤖 AI is evaluating your exam... This may take up to 2 minutes.",
              detailedAnalysis: {}
            } : null,
            isGenerating: generatingAI
          };
        }

        setExamResult(examResult);
        setLoading(false);
      } catch (error) {
        // Set a basic error state
        setExamResult({
          id: currentExamId,
          title: location.state?.examTitle || "Exam",
          subject: "Unknown",
          totalMarks: 0,
          obtainedMarks: 0,
          percentage: 0,
          grade: "N/A",
          status: "error",
          submittedAt: new Date().toISOString(),
          timeTaken: "Unknown",
          autoSubmitted: false,
          questions: [],
          classAverage: 0,
          rank: 0,
          totalStudents: 0,
          aiEvaluation: null,
          error: "Failed to load exam results"
        });
        setLoading(false);
      }
    };

    loadResults();
  }, [currentExamId, location.state, aiResults, aiResultsFromStore, dispatch]);

  // Re-process data when AI results are updated
  useEffect(() => {
    if (aiResultsFromStore && examResult) {
      // Trigger a re-load to update the display with new AI data
      const updateWithNewAI = () => {
        const aiData = aiResultsFromStore.result || aiResultsFromStore;
        if (aiData) {
          const questionFeedbacks = aiData.question_feedbacks || [];

          // Transform question_feedbacks into questions format
          const transformedQuestions = questionFeedbacks.map(qf => ({
            id: qf.question_id,
            number: qf.question_number,
            text: qf.question_text,
            aiScore: qf.ai_score,
            maxScore: qf.max_score,
            feedback: qf.feedback,
            suggestions: qf.suggestions,
            isCorrect: qf.ai_score === qf.max_score,
            studentAnswer: qf.student_attempted_answer
          }));

          setExamResult(prev => ({
            ...prev,
            obtainedMarks: aiData.total_score || prev.obtainedMarks,
            totalMarks: aiData.max_total_score || prev.totalMarks,
            percentage: Math.round(aiData.percentage || 0),
            grade: calculateGrade(aiData.percentage || 0),
            status: aiData.percentage >= 50 ? "passed" : "failed",
            // Update questions array with transformed data
            questions: transformedQuestions,
            aiEvaluation: {
              overallScore: aiData.percentage || 0,
              strengths: aiData.strengths || [],
              improvements: aiData.improvements || [],
              feedback: aiData.feedback || "AI evaluation completed successfully.",
              detailedAnalysis: aiData.detailed_analysis || {},
              questionFeedbacks: questionFeedbacks
            }
          }));
        }
      };
      updateWithNewAI();
    }
  }, [aiResultsFromStore]); // Removed examResult from dependency array to prevent infinite loop

  // Calculate grade based on percentage
  const calculateGrade = (percentage) => {
    if (percentage >= 90) return "A+";
    if (percentage >= 80) return "A";
    if (percentage >= 70) return "B+";
    if (percentage >= 60) return "B";
    if (percentage >= 50) return "C+";
    if (percentage >= 40) return "C";
    if (percentage >= 30) return "D";
    return "F";
  };

  const getGradeColor = (grade) => {
    switch (grade) {
      case "A+":
      case "A": return "text-green-600 dark:text-green-400";
      case "B+":
      case "B": return "text-blue-600 dark:text-blue-400";
      case "C+":
      case "C": return "text-yellow-600 dark:text-yellow-400";
      case "D":
      case "F": return "text-red-600 dark:text-red-400";
      default: return "text-gray-600 dark:text-gray-400";
    }
  };

  const getPerformanceIcon = (percentage) => {
    if (percentage >= 80) return <FiTrendingUp className="w-5 h-5 text-green-600" />;
    if (percentage >= 60) return <FiMinus className="w-5 h-5 text-yellow-600" />;
    return <FiTrendingDown className="w-5 h-5 text-red-600" />;
  };

  const getCorrectAnswersCount = () => {
    return examResult?.questions.filter(q => q.isCorrect).length || 0;
  };

  const handleDownloadResult = () => {
    // Implement PDF download functionality
  };

  const handleShareResult = () => {
    // Implement share functionality
    if (navigator.share) {
      navigator.share({
        title: `Exam Result - ${examResult.title}`,
        text: `I scored ${examResult.percentage}% in ${examResult.title}`,
        url: window.location.href
      });
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p>Loading your results...</p>
        </div>
      </div>
    );
  }

  if (!examResult) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiXCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Results Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Unable to load exam results.
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeBg} ${themeText} p-4 sm:p-8`}>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={() => navigate('/student/exams')}
            className="flex items-center gap-2 text-violet-600 hover:text-violet-700 transition-colors"
          >
            <FiArrowLeft className="w-4 h-4" />
            Back to Exams
          </button>
          
          {!isDisqualified && (
            <div className="flex gap-2">
              <button
                onClick={refreshAIResults}
                disabled={refreshing || isLoadingAI || generatingAI}
                className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiClock className={`w-4 h-4 ${refreshing || generatingAI ? 'animate-spin' : ''}`} />
                {generatingAI ? 'Generating...' : refreshing ? 'Refreshing...' : 'Refresh AI Results'}
              </button>
              <button
                onClick={handleDownloadResult}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <FiDownload className="w-4 h-4" />
                Download
              </button>
              <button
                onClick={handleShareResult}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FiShare2 className="w-4 h-4" />
                Share
              </button>
            </div>
          )}
        </div>

        {/* Disqualification Notice */}
        {isDisqualified && (
          <div className={`${cardBg} rounded-xl p-8 shadow-lg mb-8 border-2 border-red-200 dark:border-red-800`}>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
                <FiXCircle className="w-10 h-10 text-red-600" />
              </div>

              <h1 className="text-2xl font-bold mb-2 text-red-600">Exam Disqualified</h1>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{examResult?.title || 'Exam'}</p>

              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <p className="text-red-800 dark:text-red-200 font-medium">
                  {disqualificationReason || 'You were disqualified from this exam.'}
                </p>
              </div>

              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Results are not available for disqualified exams. Please contact your instructor for more information.
              </p>
            </div>
          </div>
        )}

        {/* Result Summary Card - Only show if not disqualified */}
        {!isDisqualified && (
        <>
        <div className={`${cardBg} rounded-xl p-8 shadow-lg mb-8`}>
          <div className="text-center mb-8">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${
              examResult.percentage >= 60 ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
            }`}>
              {examResult.percentage >= 60 ? (
                <FiCheckCircle className="w-10 h-10 text-green-600" />
              ) : (
                <FiXCircle className="w-10 h-10 text-red-600" />
              )}
            </div>
            
            <h1 className="text-2xl font-bold mb-2">{examResult.title}</h1>
            <p className="text-gray-600 dark:text-gray-400">{examResult.subject}</p>
            
            {examResult.autoSubmitted && (
              <div className="mt-4 inline-flex items-center gap-2 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full text-sm">
                <FiClock className="w-4 h-4" />
                Auto-submitted (Time expired)
              </div>
            )}
          </div>

          {/* Score Display */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-violet-600 mb-1">
                {examResult.obtainedMarks}/{examResult.totalMarks}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Score</div>
            </div>
            
            <div className="text-center">
              <div className={`text-3xl font-bold mb-1 ${getGradeColor(examResult.grade)}`}>
                {examResult.percentage}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Percentage</div>
            </div>
            
            <div className="text-center">
              <div className={`text-3xl font-bold mb-1 ${getGradeColor(examResult.grade)}`}>
                {examResult.grade}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Grade</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                {getPerformanceIcon(examResult.percentage)}
                <span className="text-2xl font-bold">#{examResult.rank}</span>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Rank</div>
            </div>
          </div>

          {/* Additional Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <div className="text-lg font-semibold">{getCorrectAnswersCount()}/{examResult.questions.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Correct Answers</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{examResult.timeTaken}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Time Taken</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{examResult.classAverage}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Class Average</div>
            </div>
          </div>
        </div>
        </>
        )}

        {/* AI Results Section - Show for all students */}
        {examResult.aiEvaluation ? (
          <div className={`${cardBg} rounded-xl p-6 shadow-lg mb-8`}>
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <FiStar className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">AI Evaluation Results</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Powered by AI
                </p>
              </div>
            </div>

            {/* AI Score and Feedback */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* AI Score */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <FiTarget className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-semibold">AI Assessment</h3>
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {examResult.aiEvaluation.overallScore}%
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  AI-generated score based on comprehensive analysis
                </p>
              </div>

              {/* Overall Feedback */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <FiMessageSquare className="w-6 h-6 text-green-600" />
                  <h3 className="text-lg font-semibold">Overall Feedback</h3>
                </div>
                <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                  {examResult.aiEvaluation.feedback || "Great work! Your understanding of the concepts is evident in your answers."}
                </p>
              </div>
            </div>

            {/* Strengths and Improvements */}
            {(examResult.aiEvaluation.strengths?.length > 0 || examResult.aiEvaluation.improvements?.length > 0) && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                {/* Strengths */}
                {examResult.aiEvaluation.strengths?.length > 0 && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <FiTrendingUp className="w-5 h-5 text-green-600" />
                      <h4 className="font-semibold text-green-800 dark:text-green-200">Strengths</h4>
                    </div>
                    <ul className="space-y-2">
                      {examResult.aiEvaluation.strengths.map((strength, index) => (
                        <li key={index} className="text-sm text-green-700 dark:text-green-300 flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Areas for Improvement */}
                {examResult.aiEvaluation.improvements?.length > 0 && (
                  <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <FiBookOpen className="w-5 h-5 text-orange-600" />
                      <h4 className="font-semibold text-orange-800 dark:text-orange-200">Areas for Improvement</h4>
                    </div>
                    <ul className="space-y-2">
                      {examResult.aiEvaluation.improvements.map((improvement, index) => (
                        <li key={index} className="text-sm text-orange-700 dark:text-orange-300 flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                          {improvement}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Detailed Analysis */}
            {examResult.aiEvaluation.detailedAnalysis && Object.keys(examResult.aiEvaluation.detailedAnalysis).length > 0 && (
              <div className="mt-6 bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <FiBookOpen className="w-5 h-5 text-purple-600" />
                  <h4 className="font-semibold text-purple-800 dark:text-purple-200">Detailed Analysis</h4>
                </div>
                <div className="text-sm text-purple-700 dark:text-purple-300 leading-relaxed">
                  {typeof examResult.aiEvaluation.detailedAnalysis === 'string'
                    ? examResult.aiEvaluation.detailedAnalysis
                    : JSON.stringify(examResult.aiEvaluation.detailedAnalysis, null, 2)}
                </div>
              </div>
            )}
          </div>
        ) : (
          /* No AI Results Available */
          <div className={`${cardBg} rounded-xl p-6 shadow-lg mb-8`}>
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-gray-400 to-gray-500 rounded-lg flex items-center justify-center">
                <FiClock className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">AI Evaluation</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  AI evaluation is pending or not available
                </p>
              </div>
            </div>

            <div className={`${generatingAI ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-yellow-50 dark:bg-yellow-900/20'} rounded-lg p-6`}>
              <div className="flex items-center gap-3 mb-2">
                {generatingAI ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                ) : (
                  <FiClock className="w-5 h-5 text-yellow-600" />
                )}
                <h3 className={`font-semibold ${generatingAI ? 'text-blue-800 dark:text-blue-200' : 'text-yellow-800 dark:text-yellow-200'}`}>
                  {generatingAI ? '🤖 Generating AI Evaluation...' : 'AI Evaluation Pending'}
                </h3>
              </div>
              <p className={`text-sm ${generatingAI ? 'text-blue-700 dark:text-blue-300' : 'text-yellow-700 dark:text-yellow-300'}`}>
                {generatingAI
                  ? 'AI is currently analyzing your exam responses and generating personalized feedback. This process may take up to 2 minutes.'
                  : 'Your exam is being processed by our AI system. This usually takes a few minutes. Please refresh the page or check back later to see your AI-generated feedback and detailed analysis.'
                }
              </p>
              {generatingAI && (
                <div className="mt-4 bg-blue-100 dark:bg-blue-800/30 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
                    <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Processing your answers with Google Gemini AI...</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Question-wise Analysis */}
        <div className={`${cardBg} rounded-xl p-6 shadow-lg`}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Question Analysis</h2>
            <button
              onClick={() => setShowAnswers(!showAnswers)}
              className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
            >
              {showAnswers ? <FiEyeOff className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
              {showAnswers ? 'Hide' : 'Show'} Answers
            </button>
          </div>

          <div className="space-y-4">
            {examResult.questions.map((question, index) => (
              <div
                key={question.id}
                className={`p-4 rounded-lg border-2 ${
                  question.isCorrect
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Q{question.number || index + 1}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      question.isCorrect
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                    }`}>
                      {question.isCorrect ? (
                        <FiCheckCircle className="w-3 h-3" />
                      ) : (
                        <FiXCircle className="w-3 h-3" />
                      )}
                      {question.isCorrect ? 'Correct' : 'Partial/Incorrect'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {question.aiScore || 0}/{question.maxScore || 0} marks
                  </div>
                </div>

                <p className="text-gray-800 dark:text-gray-200 mb-3">{question.text}</p>

                {/* AI Feedback Section */}
                {question.feedback && (
                  <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-2 mb-2">
                      <FiMessageSquare className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800 dark:text-blue-200">AI Feedback</span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">{question.feedback}</p>
                    {question.suggestions && (
                      <div className="mt-2">
                        <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Suggestions: </span>
                        <span className="text-xs text-blue-600 dark:text-blue-400">{question.suggestions}</span>
                      </div>
                    )}
                  </div>
                )}

                {showAnswers && (
                  <div className="space-y-2 mt-3">
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Your Answer: </span>
                      <span className={question.isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
                        {question.studentAnswer || 'Not answered'}
                      </span>
                    </div>
                    {!question.isCorrect && question.correctAnswer && (
                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Correct Answer: </span>
                        <span className="text-green-700 dark:text-green-300">{question.correctAnswer}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className={`${cardBg} rounded-xl p-6 shadow-lg mt-8`}>
          <h2 className="text-xl font-semibold mb-4">Performance Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Strengths</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Strong performance in multiple choice questions</li>
                <li>• Good understanding of basic concepts</li>
                <li>• Completed exam within time limit</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Areas for Improvement</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Focus more on descriptive answers</li>
                <li>• Review calculation methods</li>
                <li>• Practice time management</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StudentExamResults;
