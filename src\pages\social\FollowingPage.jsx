import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Fi<PERSON>rrow<PERSON><PERSON>t, FiUser<PERSON>heck, FiSearch, <PERSON>Loader, FiAlertCircle } from 'react-icons/fi';
import socialFollowService from '../../services/socialFollowService';
import SocialUserListItem from '../../components/social/UserListItem';

/**
 * FollowingPage Component
 * Displays paginated list of users that the specified user is following
 */
const FollowingPage = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [following, setFollowing] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrevious, setHasPrevious] = useState(false);
  const pageSize = 20;

  const currentUserId = localStorage.getItem('userId');
  const isOwnProfile = userId === currentUserId;

  useEffect(() => {
    if (userId) {
      loadFollowing();
    }
  }, [userId, currentPage]);

  const loadFollowing = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await socialFollowService.getUserFollowing(userId, currentPage, pageSize);
      
      setFollowing(response.users || []);
      setTotalCount(response.total_count || 0);
      setHasNext(response.has_next || false);
      setHasPrevious(response.has_previous || false);
    } catch (err) {
      console.error('Failed to load following:', err);
      setError(err.message || 'Failed to load following');
      setFollowing([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing, targetUserId, user) => {
    // Update the following list optimistically
    setFollowing(prev => prev.map(followedUser => 
      followedUser.id === targetUserId 
        ? { ...followedUser, isFollowing } 
        : followedUser
    ));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredFollowing = following.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePreviousPage = () => {
    if (hasPrevious && currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (hasNext) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const goBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={goBack}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <FiArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          
          <div className="flex items-center gap-3">
            <FiUserCheck className="w-6 h-6 text-green-600" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {isOwnProfile ? 'Following' : 'Following'}
            </h1>
            {totalCount > 0 && (
              <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm font-medium">
                {totalCount}
              </span>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search following..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-10 pr-4 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <FiLoader className="w-8 h-8 animate-spin text-green-600" />
            <span className="ml-3 text-gray-600 dark:text-gray-400">Loading following...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Failed to Load Following
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
              <button
                onClick={loadFollowing}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredFollowing.length === 0 && (
          <div className="text-center py-12">
            <FiUserCheck className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {searchTerm ? 'No users found' : 'Not following anyone yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : isOwnProfile 
                  ? 'Start following people to see them here'
                  : 'This user isn\'t following anyone yet'
              }
            </p>
          </div>
        )}

        {/* Following List */}
        {!loading && !error && filteredFollowing.length > 0 && (
          <div className="space-y-4">
            {filteredFollowing.map((user) => (
              <SocialUserListItem
                key={user.id}
                user={user}
                showFollowButton={true}
                onFollowChange={handleFollowChange}
              />
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && !error && following.length > 0 && (hasPrevious || hasNext) && (
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handlePreviousPage}
              disabled={!hasPrevious}
              className="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} • {totalCount} total following
            </span>
            
            <button
              onClick={handleNextPage}
              disabled={!hasNext}
              className="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FollowingPage;
