import React from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiDollarSign,
  FiSettings
} from 'react-icons/fi';

const tabs = [
  { id: 'basic', label: 'Basic Info', icon: FiCalendar },
  { id: 'details', label: 'Details', icon: FiMapPin },
  { id: 'people', label: 'People', icon: FiUsers },
  { id: 'settings', label: 'Settings', icon: FiSettings },
  { id: 'tickets', label: 'Tickets', icon: FiDollarSign }
];

const TabNavigation = ({ activeTab, onTabChange }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                type="button"
                onClick={() => onTabChange(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 inline mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default TabNavigation;
