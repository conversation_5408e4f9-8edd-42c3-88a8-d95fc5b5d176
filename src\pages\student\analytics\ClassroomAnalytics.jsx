import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudentAnalytics } from '../../../hooks/useStudentAnalytics';
import { FluidPageContainer } from '../../../components/ui/layout';
import { 
  FiUsers, 
  FiMessageCircle,
  FiClock,
  FiCheckCircle,
  FiTrendingUp,
  FiArrowLeft,
  FiActivity,
  FiHelpCircle,
  FiThumbsUp
} from 'react-icons/fi';

// Chart components
import { D3Bar<PERSON>hart, D3RadarChart, D3LineChart } from '../../../components/charts';

/**
 * Classroom Analytics Page
 * Detailed engagement and participation analytics
 */
const ClassroomAnalytics = () => {
  const navigate = useNavigate();

  // Use the analytics hook
  const {
    data: { classroom: classroomAnalytics },
    loading: { classroom: classroomLoading },
    errors: { classroom: classroomError },
    fetch: { classroom: fetchClassroomData },
    clearErrors
  } = useStudentAnalytics({
    autoFetch: false // We'll fetch manually with specific date range
  });

  const [selectedClassroom, setSelectedClassroom] = useState(null);
  const [dateRange, setDateRange] = useState({
    start_date: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1).toISOString(),
    end_date: new Date().toISOString(),
    period_type: 'monthly'
  });

  // Fetch classroom analytics
  useEffect(() => {
    fetchClassroomData(dateRange);
  }, [fetchClassroomData, dateRange]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearErrors();
    };
  }, [clearErrors]);

  // Set default selected classroom
  useEffect(() => {
    if (classroomAnalytics?.classrooms?.length > 0 && !selectedClassroom) {
      setSelectedClassroom(classroomAnalytics.classrooms[0]);
    }
  }, [classroomAnalytics, selectedClassroom]);

  // Loading state
  if (classroomLoading) {
    return (
      <FluidPageContainer>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </FluidPageContainer>
    );
  }

  // Error state
  if (classroomError) {
    return (
      <FluidPageContainer>
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">
            <FiUsers />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
            Unable to Load Classroom Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {classroomError}
          </p>
          <button
            onClick={() => fetchClassroomData(dateRange)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </FluidPageContainer>
    );
  }

  if (!classroomAnalytics || !selectedClassroom) return null;

  // Prepare chart data
  const engagementData = classroomAnalytics.classrooms.map(classroom => ({
    x: classroom.classroom_name,
    y: classroom.participation_score,
    attendance: classroom.attendance_rate,
    rank: classroom.classroom_rank
  }));

  const radarData = [{
    name: selectedClassroom.classroom_name,
    'Attendance': selectedClassroom.attendance_rate,
    'Participation': selectedClassroom.participation_score,
    'Assignment Completion': selectedClassroom.assignment_submission_rate,
    'On-time Submission': selectedClassroom.on_time_submission_rate,
    'Performance': selectedClassroom.student_average,
    'Engagement': selectedClassroom.participation_score
  }];

  // Mock time-based engagement data
  const timeEngagementData = [
    { x: 'Week 1', y: selectedClassroom.participation_score - 10 },
    { x: 'Week 2', y: selectedClassroom.participation_score - 5 },
    { x: 'Week 3', y: selectedClassroom.participation_score },
    { x: 'Week 4', y: selectedClassroom.participation_score + 3 }
  ];

  return (
    <FluidPageContainer>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <button
            onClick={() => navigate('/student/analytics')}
            className="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            Classroom Analytics
          </h1>
        </div>
        
        {/* Classroom Selector */}
        <div className="flex flex-wrap gap-2">
          {classroomAnalytics.classrooms.map((classroom) => (
            <button
              key={classroom.classroom_id}
              onClick={() => setSelectedClassroom(classroom)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedClassroom.classroom_id === classroom.classroom_id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {classroom.classroom_name}
            </button>
          ))}
        </div>
      </div>

      {/* Classroom Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Attendance Rate
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.attendance_rate}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Rank: #{selectedClassroom.classroom_rank}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiCheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Participation Score
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.participation_score}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Questions: {selectedClassroom.questions_asked}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiMessageCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Time Spent
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.time_spent_in_classroom}h
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Active: {selectedClassroom.active_learning_time}h
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiClock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Assignment Score
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.average_assignment_score}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {selectedClassroom.assignments_completed}/{selectedClassroom.assignments_total} completed
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiTrendingUp className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Engagement Comparison */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Engagement Across Classrooms
          </h3>
          <D3BarChart
            data={engagementData}
            width={500}
            height={300}
            xKey="x"
            yKey="y"
            showValues={true}
            animate={true}
            xAxisLabel="Classrooms"
            yAxisLabel="Participation Score"
          />
        </div>

        {/* Engagement Trend */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Engagement Trend
          </h3>
          <D3LineChart
            data={timeEngagementData}
            width={500}
            height={300}
            xKey="x"
            yKey="y"
            showDots={true}
            showArea={true}
            animate={true}
            xAxisLabel="Time Period"
            yAxisLabel="Engagement Score"
          />
        </div>
      </div>

      {/* Engagement Radar Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
          Engagement Profile - {selectedClassroom.classroom_name}
        </h3>
        <div className="flex justify-center">
          <D3RadarChart
            data={radarData}
            width={600}
            height={400}
            animate={true}
            showLabels={true}
          />
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Interaction Metrics */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Interaction Metrics
          </h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FiMessageCircle className="w-4 h-4 text-blue-500 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Questions Asked</span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-100">
                {selectedClassroom.questions_asked}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FiUsers className="w-4 h-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Discussions Participated</span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-100">
                {selectedClassroom.discussions_participated}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FiActivity className="w-4 h-4 text-purple-500 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Peer Interactions</span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-100">
                {selectedClassroom.peer_interactions}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FiHelpCircle className="w-4 h-4 text-orange-500 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Help Requests</span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-100">
                {selectedClassroom.help_requests}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FiThumbsUp className="w-4 h-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Positive Feedback</span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-100">
                {selectedClassroom.positive_feedback_count}
              </span>
            </div>
          </div>
        </div>

        {/* Performance Comparison */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Performance vs Class
          </h4>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Your Average</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedClassroom.student_average}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${selectedClassroom.student_average}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Class Average</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedClassroom.classroom_average}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gray-400 h-2 rounded-full" 
                  style={{ width: `${selectedClassroom.classroom_average}%` }}
                ></div>
              </div>
            </div>
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Performance Gap: 
                <span className={`ml-1 font-medium ${
                  selectedClassroom.performance_vs_classroom > 0 
                    ? 'text-green-600' 
                    : 'text-red-600'
                }`}>
                  {selectedClassroom.performance_vs_classroom > 0 ? '+' : ''}
                  {selectedClassroom.performance_vs_classroom}%
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Improvement Suggestions */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
        <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-4">
          Improvement Suggestions
        </h4>
        <ul className="space-y-2">
          {selectedClassroom.improvement_suggestions?.map((suggestion, index) => (
            <li key={index} className="flex items-start text-blue-700 dark:text-blue-300">
              <FiTrendingUp className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
              {suggestion}
            </li>
          ))}
        </ul>
      </div>
    </FluidPageContainer>
  );
};

export default ClassroomAnalytics;
