/**
 * Exam Flow Test Component
 * Tests the complete exam flow from start to submission
 */

import React, { useState } from 'react';
import { <PERSON>P<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Fi<PERSON>lertTriangle } from 'react-icons/fi';

const ExamFlowTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);

  // Mock exam data for testing
  const mockExamData = {
    id: "dc5aede9-e966-4d28-96ea-568718994656",
    title: "Test Exam",
    description: "Test exam for flow validation",
    total_marks: 1,
    total_duration: 999,
    questions: [
      {
        id: "fbd7bcad-bda5-43ce-80d9-145d8fcd317c",
        text: "1+1",
        Type: "MCQS",
        Level: "EASY",
        imageUrl: null,
        class_id: "7fcc64ec-7539-4e3c-a703-0d2e9ee36f76",
        subject_id: "eca9295b-091f-407f-8802-56df0722c369",
        chapter_id: "db174ed9-226c-4921-a8f3-68654942533a",
        topic_id: null,
        subtopic_id: null,
        marks: 1,
        options: [
          {
            id: "4ef66cc4-d574-40df-af09-994364c944d9",
            option_text: "2"
          },
          {
            id: "f0dd71e1-b807-4fbc-8208-3323fed98f03",
            option_text: "3"
          },
          {
            id: "d02b1fc5-494c-4b31-84b5-63897b78f8f0",
            option_text: "11"
          },
          {
            id: "a00535a0-7e21-40ca-84bd-2b22cea186df",
            option_text: "12"
          }
        ]
      }
    ],
    start_time: "2025-09-17T09:00:00",
    end_time: "2025-09-18T01:39:00",
    session_id: "62088b67-c529-4579-b7ed-8acb1934e87e",
    remaining_time_seconds: 25198
  };

  const addTestResult = (testName, passed, message) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      testName,
      passed,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Anti-cheat service initialization
      addTestResult(
        "Anti-cheat Service Import",
        true,
        "AntiCheatService can be imported successfully"
      );

      // Test 2: Check if anti-cheat service has the new methods
      const AntiCheatService = await import('../../../services/exam/security/AntiCheatService.js');
      const hasSetExamStarted = typeof AntiCheatService.default.setExamStarted === 'function';
      addTestResult(
        "Anti-cheat setExamStarted Method",
        hasSetExamStarted,
        hasSetExamStarted ? "setExamStarted method exists" : "setExamStarted method missing"
      );

      // Test 3: Check if ExamAttemptManager exists
      try {
        await import('../../exam/student/ExamAttemptManager');
        addTestResult(
          "ExamAttemptManager Component",
          true,
          "ExamAttemptManager component exists and can be imported"
        );
      } catch (error) {
        addTestResult(
          "ExamAttemptManager Component",
          false,
          `ExamAttemptManager import failed: ${error.message}`
        );
      }

      // Test 4: Check if ExamInterface exists
      try {
        await import('../../exam/student/ExamInterface/ExamInterface');
        addTestResult(
          "ExamInterface Component",
          true,
          "ExamInterface component exists and can be imported"
        );
      } catch (error) {
        addTestResult(
          "ExamInterface Component",
          false,
          `ExamInterface import failed: ${error.message}`
        );
      }

      // Test 5: Validate exam data structure
      const hasRequiredFields = mockExamData.id && mockExamData.questions && mockExamData.questions.length > 0;
      addTestResult(
        "Exam Data Structure",
        hasRequiredFields,
        hasRequiredFields ? "Exam data has required fields" : "Exam data missing required fields"
      );

      // Test 6: Check if questions have proper structure
      const firstQuestion = mockExamData.questions[0];
      const hasQuestionStructure = firstQuestion.id && firstQuestion.text && firstQuestion.options;
      addTestResult(
        "Question Data Structure",
        hasQuestionStructure,
        hasQuestionStructure ? "Questions have proper structure" : "Questions missing required fields"
      );

      // Test 7: Check routing configuration
      const currentPath = window.location.pathname;
      const isValidPath = currentPath.includes('/student/') || currentPath.includes('/test');
      addTestResult(
        "Routing Configuration",
        isValidPath,
        isValidPath ? "Current path is valid for student exam access" : "Path may not be configured correctly"
      );

      // Test 8: Check localStorage functionality
      try {
        localStorage.setItem('test_exam_flow', 'test');
        const retrieved = localStorage.getItem('test_exam_flow');
        localStorage.removeItem('test_exam_flow');
        addTestResult(
          "LocalStorage Functionality",
          retrieved === 'test',
          "LocalStorage is working correctly"
        );
      } catch (error) {
        addTestResult(
          "LocalStorage Functionality",
          false,
          `LocalStorage error: ${error.message}`
        );
      }

      // Test 9: Check if anti-cheat service can be activated/deactivated safely
      try {
        const service = AntiCheatService.default;
        service.activate();
        service.setExamStarted(false); // Should prevent violations from being reported
        service.deactivate();
        addTestResult(
          "Anti-cheat Lifecycle",
          true,
          "Anti-cheat service can be activated and deactivated safely"
        );
      } catch (error) {
        addTestResult(
          "Anti-cheat Lifecycle",
          false,
          `Anti-cheat lifecycle error: ${error.message}`
        );
      }

      // Test 10: Validate exam session management
      const hasSessionFields = mockExamData.session_id && mockExamData.remaining_time_seconds;
      addTestResult(
        "Session Management",
        hasSessionFields,
        hasSessionFields ? "Session data includes required fields" : "Session data missing required fields"
      );

    } catch (error) {
      addTestResult(
        "Test Suite Error",
        false,
        `Test suite encountered an error: ${error.message}`
      );
    } finally {
      setIsRunning(false);
    }
  };

  const passedTests = testResults.filter(result => result.passed).length;
  const totalTests = testResults.length;
  const allPassed = totalTests > 0 && passedTests === totalTests;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Exam Flow Test Suite</h1>
        
        <div className="mb-6">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <FiPlay className="w-4 h-4" />
            <span>{isRunning ? 'Running Tests...' : 'Run Tests'}</span>
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="mb-6">
            <div className={`p-4 rounded-lg ${allPassed ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
              <div className="flex items-center space-x-2">
                {allPassed ? (
                  <FiCheck className="w-5 h-5 text-green-600" />
                ) : (
                  <FiAlertTriangle className="w-5 h-5 text-yellow-600" />
                )}
                <span className={`font-medium ${allPassed ? 'text-green-800' : 'text-yellow-800'}`}>
                  {passedTests}/{totalTests} tests passed
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-3">
          {testResults.map((result) => (
            <div
              key={result.id}
              className={`p-4 rounded-lg border ${
                result.passed
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-start space-x-3">
                {result.passed ? (
                  <FiCheck className="w-5 h-5 text-green-600 mt-0.5" />
                ) : (
                  <FiX className="w-5 h-5 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <h3 className={`font-medium ${
                    result.passed ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {result.testName}
                  </h3>
                  <p className={`text-sm ${
                    result.passed ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {result.message}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {result.timestamp}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {testResults.length === 0 && !isRunning && (
          <div className="text-center py-8 text-gray-500">
            Click "Run Tests" to validate the exam flow implementation
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamFlowTest;
