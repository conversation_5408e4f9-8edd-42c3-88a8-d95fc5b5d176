import React from 'react';
import { FiUsers, FiUser, FiInfo } from 'react-icons/fi';

const AssignmentTypeSelector = ({
  assignmentType = 'classroom',
  onAssignmentTypeChange,
  userType = 'teacher',
  themeClasses = {
    bg: 'bg-white',
    text: 'text-gray-900',
    input: 'bg-gray-50 text-gray-900 border-gray-300',
    label: 'text-gray-700'
  }
}) => {
  // Only show for teachers
  if (userType !== 'teacher') {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
          <FiUsers className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Assignment Type</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Choose how to assign this exam</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Classroom Assignment Option */}
          <label className="relative cursor-pointer">
            <input
              type="radio"
              name="assignmentType"
              value="classroom"
              checked={assignmentType === 'classroom'}
              onChange={(e) => {
                console.log('🎯 Assignment type changed to:', e.target.value);
                onAssignmentTypeChange(e.target.value);
              }}
              className="sr-only"
            />
            <div className={`
              p-4 rounded-lg border-2 transition-all duration-200
              ${assignmentType === 'classroom' 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-blue-300'
              }
            `}>
              <div className="flex items-center gap-3">
                <div className={`
                  w-8 h-8 rounded-lg flex items-center justify-center
                  ${assignmentType === 'classroom' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }
                `}>
                  <FiUsers className="w-4 h-4" />
                </div>
                <div>
                  <h4 className={`font-medium ${assignmentType === 'classroom' ? 'text-blue-700 dark:text-blue-300' : 'text-gray-900 dark:text-gray-100'}`}>
                    Entire Classroom
                  </h4>
                  <p className={`text-sm ${assignmentType === 'classroom' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`}>
                    Assign to all students in a classroom
                  </p>
                </div>
              </div>
            </div>
          </label>

          {/* Individual Students Assignment Option */}
          <label className="relative cursor-pointer">
            <input
              type="radio"
              name="assignmentType"
              value="students"
              checked={assignmentType === 'students'}
              onChange={(e) => {
                console.log('🎯 Assignment type changed to:', e.target.value);
                onAssignmentTypeChange(e.target.value);
              }}
              className="sr-only"
            />
            <div className={`
              p-4 rounded-lg border-2 transition-all duration-200
              ${assignmentType === 'students' 
                ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-green-300'
              }
            `}>
              <div className="flex items-center gap-3">
                <div className={`
                  w-8 h-8 rounded-lg flex items-center justify-center
                  ${assignmentType === 'students' 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }
                `}>
                  <FiUser className="w-4 h-4" />
                </div>
                <div>
                  <h4 className={`font-medium ${assignmentType === 'students' ? 'text-green-700 dark:text-green-300' : 'text-gray-900 dark:text-gray-100'}`}>
                    Specific Students
                  </h4>
                  <p className={`text-sm ${assignmentType === 'students' ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}`}>
                    Choose individual students
                  </p>
                </div>
              </div>
            </div>
          </label>
        </div>

        {/* Info message based on selection */}
        {assignmentType === 'students' && (
          <div className="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
            <FiInfo className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                Individual Student Assignment
              </p>
              <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                You'll be able to select specific students in the review step after adding questions. 
                No classroom selection is required for this option.
              </p>
            </div>
          </div>
        )}

        {assignmentType === 'classroom' && (
          <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <FiInfo className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                Classroom Assignment
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                Please select a classroom below. All students in the selected classroom will receive this exam.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssignmentTypeSelector;
