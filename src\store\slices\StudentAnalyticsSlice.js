import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import studentAnalyticsService from '../../services/studentAnalyticsService';

// Initial state
const initialState = {
  // Subject Analytics
  subjectAnalytics: null,
  subjectLoading: false,
  subjectError: null,
  subjectLastUpdated: null,

  // Class/Grade Analytics
  classGradeAnalytics: null,
  classGradeLoading: false,
  classGradeError: null,
  classGradeLastUpdated: null,

  // Classroom Analytics
  classroomAnalytics: null,
  classroomLoading: false,
  classroomError: null,
  classroomLastUpdated: null,

  // Competition Analytics
  competitionAnalytics: null,
  competitionLoading: false,
  competitionError: null,
  competitionLastUpdated: null,

  // Comprehensive Analytics
  comprehensiveAnalytics: null,
  comprehensiveLoading: false,
  comprehensiveError: null,
  comprehensiveLastUpdated: null,

  // Global state
  isLoading: false,
  hasErrors: false,
  lastGlobalUpdate: null,
};

// Async thunks for analytics API calls

// Fetch Subject Analytics
export const fetchSubjectAnalytics = createAsyncThunk(
  'studentAnalytics/fetchSubjectAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const data = await studentAnalyticsService.getSubjectAnalytics(params);
      return data;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Fetch Class/Grade Analytics
export const fetchClassGradeAnalytics = createAsyncThunk(
  'studentAnalytics/fetchClassGradeAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const data = await studentAnalyticsService.getClassGradeAnalytics(params);
      return data;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Fetch Classroom Analytics
export const fetchClassroomAnalytics = createAsyncThunk(
  'studentAnalytics/fetchClassroomAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const data = await studentAnalyticsService.getClassroomAnalytics(params);
      return data;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Fetch Competition Analytics
export const fetchCompetitionAnalytics = createAsyncThunk(
  'studentAnalytics/fetchCompetitionAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const data = await studentAnalyticsService.getCompetitionAnalytics(params);
      return data;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Fetch Comprehensive Analytics
export const fetchComprehensiveAnalytics = createAsyncThunk(
  'studentAnalytics/fetchComprehensiveAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const data = await studentAnalyticsService.getComprehensiveAnalytics(params);
      return data;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Fetch All Analytics
export const fetchAllAnalytics = createAsyncThunk(
  'studentAnalytics/fetchAllAnalytics',
  async (params = {}, thunkAPI) => {
    try {
      const [
        subjectData,
        classGradeData,
        classroomData,
        competitionData,
        comprehensiveData
      ] = await Promise.all([
        studentAnalyticsService.getSubjectAnalytics(params),
        studentAnalyticsService.getClassGradeAnalytics(params),
        studentAnalyticsService.getClassroomAnalytics(params),
        studentAnalyticsService.getCompetitionAnalytics(params),
        studentAnalyticsService.getComprehensiveAnalytics(params)
      ]);

      return {
        subjectAnalytics: subjectData,
        classGradeAnalytics: classGradeData,
        classroomAnalytics: classroomData,
        competitionAnalytics: competitionData,
        comprehensiveAnalytics: comprehensiveData
      };
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Create the slice
const studentAnalyticsSlice = createSlice({
  name: 'studentAnalytics',
  initialState,
  reducers: {
    // Clear specific analytics data
    clearSubjectAnalytics: (state) => {
      state.subjectAnalytics = null;
      state.subjectError = null;
      state.subjectLastUpdated = null;
    },
    clearClassGradeAnalytics: (state) => {
      state.classGradeAnalytics = null;
      state.classGradeError = null;
      state.classGradeLastUpdated = null;
    },
    clearClassroomAnalytics: (state) => {
      state.classroomAnalytics = null;
      state.classroomError = null;
      state.classroomLastUpdated = null;
    },
    clearCompetitionAnalytics: (state) => {
      state.competitionAnalytics = null;
      state.competitionError = null;
      state.competitionLastUpdated = null;
    },
    clearComprehensiveAnalytics: (state) => {
      state.comprehensiveAnalytics = null;
      state.comprehensiveError = null;
      state.comprehensiveLastUpdated = null;
    },
    
    // Clear all analytics data
    clearAllAnalytics: (state) => {
      return { ...initialState };
    },
    
    // Clear all errors
    clearAllErrors: (state) => {
      state.subjectError = null;
      state.classGradeError = null;
      state.classroomError = null;
      state.competitionError = null;
      state.comprehensiveError = null;
      state.hasErrors = false;
    },
    
    // Clear specific errors
    clearSubjectError: (state) => {
      state.subjectError = null;
    },
    clearClassGradeError: (state) => {
      state.classGradeError = null;
    },
    clearClassroomError: (state) => {
      state.classroomError = null;
    },
    clearCompetitionError: (state) => {
      state.competitionError = null;
    },
    clearComprehensiveError: (state) => {
      state.comprehensiveError = null;
    },
  },
  extraReducers: (builder) => {
    // Subject Analytics
    builder
      .addCase(fetchSubjectAnalytics.pending, (state) => {
        state.subjectLoading = true;
        state.subjectError = null;
        state.isLoading = true;
      })
      .addCase(fetchSubjectAnalytics.fulfilled, (state, action) => {
        state.subjectLoading = false;
        state.subjectAnalytics = action.payload;
        state.subjectLastUpdated = new Date().toISOString();
        state.isLoading = false;
        state.lastGlobalUpdate = new Date().toISOString();
      })
      .addCase(fetchSubjectAnalytics.rejected, (state, action) => {
        state.subjectLoading = false;
        state.subjectError = action.payload;
        state.isLoading = false;
        state.hasErrors = true;
      });

    // Class/Grade Analytics
    builder
      .addCase(fetchClassGradeAnalytics.pending, (state) => {
        state.classGradeLoading = true;
        state.classGradeError = null;
        state.isLoading = true;
      })
      .addCase(fetchClassGradeAnalytics.fulfilled, (state, action) => {
        state.classGradeLoading = false;
        state.classGradeAnalytics = action.payload;
        state.classGradeLastUpdated = new Date().toISOString();
        state.isLoading = false;
        state.lastGlobalUpdate = new Date().toISOString();
      })
      .addCase(fetchClassGradeAnalytics.rejected, (state, action) => {
        state.classGradeLoading = false;
        state.classGradeError = action.payload;
        state.isLoading = false;
        state.hasErrors = true;
      });

    // Classroom Analytics
    builder
      .addCase(fetchClassroomAnalytics.pending, (state) => {
        state.classroomLoading = true;
        state.classroomError = null;
        state.isLoading = true;
      })
      .addCase(fetchClassroomAnalytics.fulfilled, (state, action) => {
        state.classroomLoading = false;
        state.classroomAnalytics = action.payload;
        state.classroomLastUpdated = new Date().toISOString();
        state.isLoading = false;
        state.lastGlobalUpdate = new Date().toISOString();
      })
      .addCase(fetchClassroomAnalytics.rejected, (state, action) => {
        state.classroomLoading = false;
        state.classroomError = action.payload;
        state.isLoading = false;
        state.hasErrors = true;
      });

    // Competition Analytics
    builder
      .addCase(fetchCompetitionAnalytics.pending, (state) => {
        state.competitionLoading = true;
        state.competitionError = null;
        state.isLoading = true;
      })
      .addCase(fetchCompetitionAnalytics.fulfilled, (state, action) => {
        state.competitionLoading = false;
        state.competitionAnalytics = action.payload;
        state.competitionLastUpdated = new Date().toISOString();
        state.isLoading = false;
        state.lastGlobalUpdate = new Date().toISOString();
      })
      .addCase(fetchCompetitionAnalytics.rejected, (state, action) => {
        state.competitionLoading = false;
        state.competitionError = action.payload;
        state.isLoading = false;
        state.hasErrors = true;
      });

    // Comprehensive Analytics
    builder
      .addCase(fetchComprehensiveAnalytics.pending, (state) => {
        state.comprehensiveLoading = true;
        state.comprehensiveError = null;
        state.isLoading = true;
      })
      .addCase(fetchComprehensiveAnalytics.fulfilled, (state, action) => {
        state.comprehensiveLoading = false;
        state.comprehensiveAnalytics = action.payload;
        state.comprehensiveLastUpdated = new Date().toISOString();
        state.isLoading = false;
        state.lastGlobalUpdate = new Date().toISOString();
      })
      .addCase(fetchComprehensiveAnalytics.rejected, (state, action) => {
        state.comprehensiveLoading = false;
        state.comprehensiveError = action.payload;
        state.isLoading = false;
        state.hasErrors = true;
      });

    // Fetch All Analytics
    builder
      .addCase(fetchAllAnalytics.pending, (state) => {
        state.isLoading = true;
        state.hasErrors = false;
        // Set all individual loading states
        state.subjectLoading = true;
        state.classGradeLoading = true;
        state.classroomLoading = true;
        state.competitionLoading = true;
        state.comprehensiveLoading = true;
      })
      .addCase(fetchAllAnalytics.fulfilled, (state, action) => {
        const {
          subjectAnalytics,
          classGradeAnalytics,
          classroomAnalytics,
          competitionAnalytics,
          comprehensiveAnalytics
        } = action.payload;

        // Update all analytics data
        state.subjectAnalytics = subjectAnalytics;
        state.classGradeAnalytics = classGradeAnalytics;
        state.classroomAnalytics = classroomAnalytics;
        state.competitionAnalytics = competitionAnalytics;
        state.comprehensiveAnalytics = comprehensiveAnalytics;

        // Update timestamps
        const now = new Date().toISOString();
        state.subjectLastUpdated = now;
        state.classGradeLastUpdated = now;
        state.classroomLastUpdated = now;
        state.competitionLastUpdated = now;
        state.comprehensiveLastUpdated = now;
        state.lastGlobalUpdate = now;

        // Clear loading states
        state.isLoading = false;
        state.subjectLoading = false;
        state.classGradeLoading = false;
        state.classroomLoading = false;
        state.competitionLoading = false;
        state.comprehensiveLoading = false;
      })
      .addCase(fetchAllAnalytics.rejected, (state, action) => {
        // Clear loading states
        state.isLoading = false;
        state.subjectLoading = false;
        state.classGradeLoading = false;
        state.classroomLoading = false;
        state.competitionLoading = false;
        state.comprehensiveLoading = false;

        // Set error state
        state.hasErrors = true;

        // Set error message for all analytics types
        const errorMessage = action.payload;
        state.subjectError = errorMessage;
        state.classGradeError = errorMessage;
        state.classroomError = errorMessage;
        state.competitionError = errorMessage;
        state.comprehensiveError = errorMessage;
      });
  },
});

// Note: fetchAllAnalytics is already exported above as a named export
// Other async thunks are exported individually where they are defined

// Export actions
export const {
  clearSubjectAnalytics,
  clearClassGradeAnalytics,
  clearClassroomAnalytics,
  clearCompetitionAnalytics,
  clearComprehensiveAnalytics,
  clearAllAnalytics,
  clearAllErrors,
  clearSubjectError,
  clearClassGradeError,
  clearClassroomError,
  clearCompetitionError,
  clearComprehensiveError,
} = studentAnalyticsSlice.actions;

// Export reducer
export default studentAnalyticsSlice.reducer;
