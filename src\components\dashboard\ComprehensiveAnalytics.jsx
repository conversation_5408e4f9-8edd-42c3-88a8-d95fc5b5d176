import React, { useState, useEffect } from 'react';
import { useStudentAnalytics } from '../../hooks/useStudentAnalytics';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiMinus,
  FiBookOpen,
  FiUsers,
  FiAward,
  FiTarget,
  FiClock,
  FiCheckCircle,
  FiBarChart2,
  FiActivity,
  FiStar,
  FiCalendar,
  FiChevronRight
} from 'react-icons/fi';

// Chart components
import { D3Bar<PERSON><PERSON>, D3Line<PERSON><PERSON> } from '../charts';
import LoadingSpinner, { ChartSkeleton } from '../ui/LoadingSpinner';

/**
 * Comprehensive Analytics Component for Student Dashboard
 * Combines Subject, Classroom, and Competition analytics in organized sections
 */
const ComprehensiveAnalytics = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState({
    start_date: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1).toISOString(),
    end_date: new Date().toISOString(),
    period_type: 'monthly'
  });

  // Use the analytics hook to fetch all data - DISABLED auto-fetch to prevent API spam
  const {
    data: {
      comprehensive: comprehensiveAnalytics,
      subject: subjectAnalytics,
      classroom: classroomAnalytics,
      competition: competitionAnalytics
    },
    loading,
    errors,
    fetch: { all: fetchAllData },
    clearErrors
  } = useStudentAnalytics({
    autoFetch: false, // DISABLED to prevent infinite API calls
    fetchAll: false
  });

  // Clear errors on unmount only - DO NOT FETCH DATA HERE
  // Data fetching is handled by the parent component or other analytics components
  useEffect(() => {
    return () => {
      clearErrors();
    };
  }, [clearErrors]);

  const getTrendIcon = (trend) => {
    if (trend > 0) return <FiTrendingUp className="w-4 h-4 text-green-500" />;
    if (trend < 0) return <FiTrendingDown className="w-4 h-4 text-red-500" />;
    return <FiMinus className="w-4 h-4 text-gray-500" />;
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiBarChart2 },
    { id: 'subjects', label: 'Subjects', icon: FiBookOpen },
    { id: 'classroom', label: 'Classroom', icon: FiUsers },
    { id: 'competitions', label: 'Competitions', icon: FiAward }
  ];

  // Show loading state
  if (loading.any) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div className="space-y-6">
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner
              size="lg"
              color="blue"
              text="Loading analytics data..."
              currentTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
            ))}
          </div>
          <ChartSkeleton height="300px" />
        </div>
      </div>
    );
  }

  // Show error state if there are errors
  if (errors.comprehensive || errors.subject || errors.classroom || errors.competition) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <FiActivity className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
            Unable to Load Analytics
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            There was an issue loading your analytics data. Please try refreshing the page.
          </p>
          <button
            onClick={() => {
              clearErrors();
              fetchAllData();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
              Learning Analytics
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Comprehensive insights into your academic performance
            </p>
          </div>
          
          {/* Date Range Selector */}
          <div className="mt-4 md:mt-0">
            <select
              value={dateRange.period_type}
              onChange={(e) => setDateRange(prev => ({ ...prev, period_type: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm"
            >
              <option value="weekly">Last 4 Weeks</option>
              <option value="monthly">Last 3 Months</option>
              <option value="quarterly">Last Quarter</option>
              <option value="yearly">This Year</option>
            </select>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mt-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <OverviewSection
            comprehensiveAnalytics={comprehensiveAnalytics}
            subjectAnalytics={subjectAnalytics}
            classroomAnalytics={classroomAnalytics}
            competitionAnalytics={competitionAnalytics}
          />
        )}

        {activeTab === 'subjects' && (
          <SubjectsSection
            subjectAnalytics={subjectAnalytics}
            getTrendIcon={getTrendIcon}
          />
        )}

        {activeTab === 'classroom' && (
          <ClassroomSection
            classroomAnalytics={classroomAnalytics}
          />
        )}

        {activeTab === 'competitions' && (
          <CompetitionsSection
            competitionAnalytics={competitionAnalytics}
          />
        )}

        {/* Fallback when no data is available */}
        {!comprehensiveAnalytics && !subjectAnalytics && !classroomAnalytics && !competitionAnalytics && (
          <div className="text-center py-12">
            <FiBarChart2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              No Analytics Data Available
            </h3>
            <p className="text-gray-500 dark:text-gray-500 mb-4">
              Analytics data will appear here once you start participating in classes and competitions.
            </p>
            <button
              onClick={() => fetchAllData()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Load Analytics
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Overview Section Component
const OverviewSection = ({ comprehensiveAnalytics, subjectAnalytics, classroomAnalytics, competitionAnalytics }) => {
  const overallScore = comprehensiveAnalytics?.overall_performance_score || 0;
  const strongestSubject = comprehensiveAnalytics?.academic_strength_areas?.[0] || 'N/A';
  const totalCompetitions = competitionAnalytics?.competitions?.length || 0;
  const avgClassroomRank = classroomAnalytics?.classrooms?.reduce((sum, c) => sum + c.classroom_rank, 0) / (classroomAnalytics?.classrooms?.length || 1) || 0;

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-600 dark:text-blue-400 mb-1">Overall Performance</p>
              <p className="text-3xl font-bold text-blue-800 dark:text-blue-200">{overallScore.toFixed(1)}%</p>
              <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2 mt-3">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${overallScore}%` }}
                ></div>
              </div>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <FiTarget className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-600 dark:text-green-400 mb-1">Strongest Area</p>
              <p className="text-xl font-bold text-green-800 dark:text-green-200 truncate">{strongestSubject}</p>
              <div className="flex items-center mt-2">
                <FiTrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" />
                <span className="text-xs text-green-600 dark:text-green-400 font-medium">Top Subject</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <FiStar className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-1">Avg Class Rank</p>
              <p className="text-3xl font-bold text-purple-800 dark:text-purple-200">#{Math.round(avgClassroomRank)}</p>
              <div className="flex items-center mt-2">
                <FiUsers className="w-4 h-4 text-purple-600 dark:text-purple-400 mr-1" />
                <span className="text-xs text-purple-600 dark:text-purple-400 font-medium">Class Position</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
              <FiUsers className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-700 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-600 dark:text-yellow-400 mb-1">Competitions</p>
              <p className="text-3xl font-bold text-yellow-800 dark:text-yellow-200">{totalCompetitions}</p>
              <div className="flex items-center mt-2">
                <FiAward className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-1" />
                <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">Participated</span>
              </div>
            </div>
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl">
              <FiAward className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Overall Grade Performance */}
      {comprehensiveAnalytics && (
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
          <h3 className="text-xl font-bold text-blue-800 dark:text-blue-200 mb-6 flex items-center">
            <FiTrendingUp className="w-6 h-6 mr-3 text-blue-600 dark:text-blue-400" />
            Overall Grade Performance
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Subject Mastery */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <FiBookOpen className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {comprehensiveAnalytics.subject_analytics?.overall_average || 0}%
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Subject Mastery</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${comprehensiveAnalytics.subject_analytics?.overall_average || 0}%` }}
                ></div>
              </div>
            </div>

            {/* Classroom Engagement */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-green-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <FiUsers className="w-5 h-5 text-green-600 dark:text-green-400" />
                <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {classroomAnalytics?.classrooms?.[0]?.participation_score || 0}
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Class Engagement</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min((classroomAnalytics?.classrooms?.[0]?.participation_score || 0), 100)}%` }}
                ></div>
              </div>
            </div>

            {/* Competition Performance */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-yellow-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <FiAward className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                <span className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                  {competitionAnalytics?.overall_performance?.average_score || 0}%
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Competitions</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-yellow-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${competitionAnalytics?.overall_performance?.average_score || 0}%` }}
                ></div>
              </div>
            </div>

            {/* Assignment Completion */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <FiCheckCircle className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                <span className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {classroomAnalytics?.classrooms?.[0]?.assignment_submission_rate || 0}%
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Assignments</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${classroomAnalytics?.classrooms?.[0]?.assignment_submission_rate || 0}%` }}
                ></div>
              </div>
            </div>

            {/* Time Management */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-indigo-200 dark:border-gray-600">
              <div className="flex items-center justify-between mb-2">
                <FiClock className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                <span className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                  {classroomAnalytics?.classrooms?.[0]?.on_time_submission_rate || 0}%
                </span>
              </div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">On-Time Rate</p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-indigo-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${classroomAnalytics?.classrooms?.[0]?.on_time_submission_rate || 0}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Improvement Opportunities */}
      {comprehensiveAnalytics?.improvement_opportunities && (
        <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-700 rounded-xl p-6">
          <div className="flex items-center mb-6">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg mr-3">
              <FiTarget className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 className="text-xl font-bold text-orange-800 dark:text-orange-200">Improvement Opportunities</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {comprehensiveAnalytics.improvement_opportunities.slice(0, 6).map((opportunity, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start">
                  <div className="p-1 bg-orange-100 dark:bg-orange-900/30 rounded-full mr-3 mt-0.5">
                    <FiChevronRight className="w-3 h-3 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-800 dark:text-gray-200 text-sm font-medium leading-relaxed">
                      {opportunity}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {comprehensiveAnalytics.improvement_opportunities.length > 6 && (
            <div className="mt-4 text-center">
              <button className="text-orange-600 dark:text-orange-400 text-sm font-medium hover:text-orange-700 dark:hover:text-orange-300 transition-colors">
                View All Suggestions ({comprehensiveAnalytics.improvement_opportunities.length})
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Subjects Section Component
const SubjectsSection = ({ subjectAnalytics, getTrendIcon }) => {
  const [selectedSubject, setSelectedSubject] = useState(null);

  useEffect(() => {
    if (subjectAnalytics?.subjects?.length > 0 && !selectedSubject) {
      setSelectedSubject(subjectAnalytics.subjects[0]);
    }
  }, [subjectAnalytics, selectedSubject]);

  if (!subjectAnalytics || !selectedSubject) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No subject analytics data available
      </div>
    );
  }

  const subjectScoresData = subjectAnalytics.subjects.map(subject => ({
    x: subject.subject_name,
    y: subject.average_score,
    classAvg: subject.class_average
  }));

  return (
    <div className="space-y-6">
      {/* Subject Selector */}
      <div className="flex flex-wrap gap-2">
        {subjectAnalytics.subjects.map((subject) => (
          <button
            key={subject.subject_id}
            onClick={() => setSelectedSubject(subject)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedSubject.subject_id === subject.subject_id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            {subject.subject_name}
          </button>
        ))}
      </div>

      {/* Subject Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Score</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.average_score}%
              </p>
              <div className="flex items-center mt-2">
                {getTrendIcon(selectedSubject.ranking?.rank_trend)}
                <span className="text-xs text-gray-500 ml-1">
                  vs Class: {selectedSubject.class_average}%
                </span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiTarget className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Class Rank</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                #{selectedSubject.ranking?.current_rank}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                of {selectedSubject.ranking?.total_students} students
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiStar className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Assignments</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.assignments_completed}/{selectedSubject.assignments_total}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {((selectedSubject.assignments_completed / selectedSubject.assignments_total) * 100).toFixed(1)}% completed
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiCheckCircle className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Study Time</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedSubject.time_spent}h
              </p>
              <p className="text-xs text-gray-500 mt-2">
                This month
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiClock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Subject Performance Chart */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
          <FiBarChart2 className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
          Subject Performance Comparison
        </h3>
        <div className="overflow-x-auto">
          <D3BarChart
            data={subjectScoresData}
            width={Math.max(600, subjectScoresData.length * 80)}
            height={320}
            xKey="x"
            yKey="y"
            showValues={true}
            animate={true}
            gradient={true}
            cornerRadius={6}
            theme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
            xAxisLabel="Subjects"
            yAxisLabel="Score (%)"
          />
        </div>
      </div>
    </div>
  );
};

// Classroom Section Component
const ClassroomSection = ({ classroomAnalytics }) => {
  const [selectedClassroom, setSelectedClassroom] = useState(null);

  useEffect(() => {
    if (classroomAnalytics?.classrooms?.length > 0 && !selectedClassroom) {
      setSelectedClassroom(classroomAnalytics.classrooms[0]);
    }
  }, [classroomAnalytics, selectedClassroom]);

  if (!classroomAnalytics || !selectedClassroom) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No classroom analytics data available
      </div>
    );
  }

  const engagementData = classroomAnalytics.classrooms.map(classroom => ({
    x: classroom.classroom_name,
    y: classroom.participation_score,
    attendance: classroom.attendance_rate
  }));

  return (
    <div className="space-y-6">
      {/* Classroom Selector */}
      <div className="flex flex-wrap gap-2">
        {classroomAnalytics.classrooms.map((classroom) => (
          <button
            key={classroom.classroom_id}
            onClick={() => setSelectedClassroom(classroom)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedClassroom.classroom_id === classroom.classroom_id
                ? 'bg-green-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            {classroom.classroom_name}
          </button>
        ))}
      </div>

      {/* Classroom Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Attendance Rate</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.attendance_rate}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Rank: #{selectedClassroom.classroom_rank}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiCheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Participation</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.participation_score}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Questions: {selectedClassroom.questions_asked}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiActivity className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Assignment Score</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.average_assignment_score}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {selectedClassroom.assignments_completed}/{selectedClassroom.assignments_total} completed
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiTarget className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Time Spent</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {selectedClassroom.time_spent_in_classroom}h
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Active: {selectedClassroom.active_learning_time}h
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiClock className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Engagement Chart */}
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
          <FiActivity className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
          Classroom Engagement
        </h3>
        <div className="overflow-x-auto">
          <D3BarChart
            data={engagementData}
            width={Math.max(600, engagementData.length * 100)}
            height={320}
            xKey="x"
            yKey="y"
            showValues={true}
            animate={true}
            gradient={true}
            cornerRadius={6}
            theme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
            xAxisLabel="Classrooms"
            yAxisLabel="Participation Score"
          />
        </div>
      </div>
    </div>
  );
};

// Competitions Section Component
const CompetitionsSection = ({ competitionAnalytics }) => {
  if (!competitionAnalytics) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No competition analytics data available
      </div>
    );
  }

  const competitionScoresData = competitionAnalytics.competitions?.map(comp => ({
    x: comp.competition_name,
    y: comp.score,
    rank: comp.rank
  })) || [];

  const performanceData = competitionAnalytics.performance_trends?.map(trend => ({
    x: trend.month,
    y: trend.average_score
  })) || [];

  return (
    <div className="space-y-6">
      {/* Competition Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Competitions</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {competitionAnalytics.competitions?.length || 0}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                This period
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiAward className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Score</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {competitionAnalytics.overall_performance?.average_score || 0}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Best: {competitionAnalytics.overall_performance?.best_score || 0}%
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiTarget className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Rank</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                #{competitionAnalytics.overall_performance?.average_rank || 0}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Best: #{competitionAnalytics.overall_performance?.best_rank || 0}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiStar className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Medals Won</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {competitionAnalytics.achievements?.medals_won || 0}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Gold: {competitionAnalytics.achievements?.gold_medals || 0}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiAward className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Competition Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Competition Scores */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
            <FiAward className="w-5 h-5 mr-2 text-yellow-600 dark:text-yellow-400" />
            Competition Scores
          </h3>
          {competitionScoresData.length > 0 ? (
            <div className="overflow-x-auto">
              <D3BarChart
                data={competitionScoresData}
                width={Math.max(400, competitionScoresData.length * 60)}
                height={280}
                xKey="x"
                yKey="y"
                showValues={true}
                animate={true}
                gradient={true}
                cornerRadius={6}
                theme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                xAxisLabel="Competitions"
                yAxisLabel="Score (%)"
              />
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <FiAward className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No competition data available</p>
              </div>
            </div>
          )}
        </div>

        {/* Performance Trend */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
            <FiTrendingUp className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
            Performance Trend
          </h3>
          {performanceData.length > 0 ? (
            <div className="overflow-x-auto">
              <D3LineChart
                data={performanceData}
                width={400}
                height={280}
                xKey="x"
                yKey="y"
                animate={true}
                showPoints={true}
                gradient={true}
                theme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
                xAxisLabel="Month"
                yAxisLabel="Average Score (%)"
              />
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <FiTrendingUp className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No trend data available</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recent Competitions */}
      {competitionAnalytics.competitions && competitionAnalytics.competitions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">Recent Competitions</h3>
          <div className="space-y-3">
            {competitionAnalytics.competitions.slice(0, 5).map((competition, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/20 mr-3">
                    <FiAward className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-100">{competition.competition_name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{competition.subject}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-800 dark:text-gray-100">{competition.score}%</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Rank #{competition.rank}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ComprehensiveAnalytics;
