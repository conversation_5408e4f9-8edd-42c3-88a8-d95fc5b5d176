import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudentAnalytics } from '../../../hooks/useStudentAnalytics';
import { FluidPageContainer } from '../../../components/ui/layout';
import {
  FiAward,
  FiTarget,
  FiCalendar,
  FiTrendingUp,
  FiArrowLeft,
  FiStar,
  FiClock,
  FiUsers
} from 'react-icons/fi';

// Chart components
import { D3<PERSON><PERSON><PERSON><PERSON>, D3Bar<PERSON>hart, D3Radar<PERSON><PERSON> } from '../../../components/charts';

/**
 * Competition Analytics Page
 * Detailed competition performance and achievements
 */
const CompetitionAnalytics = () => {
  const navigate = useNavigate();

  // Use the analytics hook
  const {
    data: { competition: competitionAnalytics },
    loading: { competition: competitionLoading },
    errors: { competition: competitionError },
    fetch: { competition: fetchCompetitionData },
    clearErrors
  } = useStudentAnalytics({
    autoFetch: false // We'll fetch manually with specific date range
  });

  const [selectedCompetition, setSelectedCompetition] = useState(null);
  const [dateRange, setDateRange] = useState({
    start_date: new Date(new Date().getFullYear(), new Date().getMonth() - 6, 1).toISOString(),
    end_date: new Date().toISOString(),
    period_type: 'monthly'
  });

  // Fetch competition analytics
  useEffect(() => {
    fetchCompetitionData(dateRange);
  }, [fetchCompetitionData, dateRange]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearErrors();
    };
  }, [clearErrors]);

  // Set default selected competition
  useEffect(() => {
    if (competitionAnalytics?.competitions?.length > 0 && !selectedCompetition) {
      setSelectedCompetition(competitionAnalytics.competitions[0]);
    }
  }, [competitionAnalytics, selectedCompetition]);

  // Loading state
  if (competitionLoading) {
    return (
      <FluidPageContainer>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </FluidPageContainer>
    );
  }

  // Error state
  if (competitionError) {
    return (
      <FluidPageContainer>
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">
            <FiAward />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
            Unable to Load Competition Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {competitionError}
          </p>
          <button
            onClick={() => fetchCompetitionData(dateRange)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </FluidPageContainer>
    );
  }

  if (!competitionAnalytics) return null;

  const summary = competitionAnalytics.summary;

  // Prepare chart data
  const performanceTrendData = summary?.performance_trend?.map(item => ({
    x: item.period,
    y: item.value,
    change: item.change_percentage,
    trend: item.trend_direction
  })) || [];

  const competitionScoresData = competitionAnalytics.competitions?.map(comp => ({
    x: comp.competition_name.substring(0, 15) + '...',
    y: comp.percentage_score,
    rank: comp.rank,
    participants: comp.total_participants
  })) || [];

  // Radar data for selected competition
  const radarData = selectedCompetition ? [{
    name: selectedCompetition.competition_name,
    ...selectedCompetition.category_scores
  }] : [];

  return (
    <FluidPageContainer>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <button
            onClick={() => navigate('/student/analytics')}
            className="mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            Competition Analytics
          </h1>
        </div>
        
        {/* Competition Selector */}
        {competitionAnalytics.competitions?.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {competitionAnalytics.competitions.map((competition) => (
              <button
                key={competition.competition_id}
                onClick={() => setSelectedCompetition(competition)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedCompetition?.competition_id === competition.competition_id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {competition.competition_name}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Competitions
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {summary?.total_competitions_participated || 0}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {summary?.total_competitions_completed || 0} completed
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <FiCalendar className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Average Score
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {summary?.average_score?.toFixed(1) || 0}%
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Best: {summary?.best_performance_score || 0}%
              </p>
            </div>
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20">
              <FiTarget className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Best Rank
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                #{summary?.best_rank || 'N/A'}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Top 10: {summary?.top_10_finishes || 0}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
              <FiAward className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Awards
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">
                {summary?.total_awards || 0}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {summary?.total_certificates || 0} certificates
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <FiAward className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Performance Trend */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Performance Trend
          </h3>
          {performanceTrendData.length > 0 ? (
            <D3LineChart
              data={performanceTrendData}
              width={500}
              height={300}
              xKey="x"
              yKey="y"
              showDots={true}
              showArea={true}
              animate={true}
              xAxisLabel="Period"
              yAxisLabel="Score"
            />
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No trend data available
            </div>
          )}
        </div>

        {/* Competition Scores */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Competition Scores
          </h3>
          {competitionScoresData.length > 0 ? (
            <D3BarChart
              data={competitionScoresData}
              width={500}
              height={300}
              xKey="x"
              yKey="y"
              showValues={true}
              animate={true}
              xAxisLabel="Competitions"
              yAxisLabel="Score (%)"
            />
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              No competition data available
            </div>
          )}
        </div>
      </div>

      {/* Category Performance Radar */}
      {selectedCompetition && radarData.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-6">
            Category Performance - {selectedCompetition.competition_name}
          </h3>
          <div className="flex justify-center">
            <D3RadarChart
              data={radarData}
              width={600}
              height={400}
              animate={true}
              showLabels={true}
            />
          </div>
        </div>
      )}

      {/* Competition Details */}
      {selectedCompetition && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Competition Info */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Competition Details
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Type:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedCompetition.competition_type}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Score:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedCompetition.score_obtained}/{selectedCompetition.total_possible_score}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Rank:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  #{selectedCompetition.rank} of {selectedCompetition.total_participants}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Percentile:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedCompetition.percentile}th
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Time Taken:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedCompetition.time_taken} minutes
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Efficiency:</span>
                <span className="font-medium text-gray-800 dark:text-gray-100">
                  {selectedCompetition.time_efficiency}%
                </span>
              </div>
            </div>
          </div>

          {/* Awards & Achievements */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Awards & Achievements
            </h4>
            <div className="space-y-4">
              {selectedCompetition.awards_received?.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    Awards Received:
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedCompetition.awards_received.map((award, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded-full text-sm flex items-center"
                      >
                        <FiAward className="w-3 h-3 mr-1" />
                        {award}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedCompetition.certificates_earned?.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    Certificates:
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedCompetition.certificates_earned.map((cert, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-full text-sm flex items-center"
                      >
                        <FiStar className="w-3 h-3 mr-1" />
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {selectedCompetition.strongest_categories?.length > 0 && (
                <div>
                  <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    Strongest Categories:
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedCompetition.strongest_categories.map((category, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded-full text-sm"
                      >
                        {category}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Upcoming Competitions */}
      {competitionAnalytics.upcoming_competitions?.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
          <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-4">
            Upcoming Competitions
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {competitionAnalytics.upcoming_competitions.map((comp, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4">
                <h5 className="font-medium text-gray-800 dark:text-gray-100 mb-2">
                  {comp.competition_name}
                </h5>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <FiCalendar className="w-4 h-4 mr-2" />
                  Event: {new Date(comp.event_date).toLocaleDateString()}
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1">
                  <FiClock className="w-4 h-4 mr-2" />
                  Registration: {new Date(comp.registration_deadline).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </FluidPageContainer>
  );
};

export default CompetitionAnalytics;
