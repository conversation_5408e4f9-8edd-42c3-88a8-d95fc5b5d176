/**
 * Exam Session Data Service
 * Handles fetching exam data using session_id for security
 */

import { BASE_URL } from '../../../utils/api/API_URL';
import { getAuthToken } from '../../../utils/helpers/authHelpers';

class ExamSessionDataService {
  constructor() {
    this.baseUrl = BASE_URL;
    this.sessionDataCache = new Map();
    this.cacheTimeout = 300000; // 5 minutes
  }

  /**
   * Get exam data using session_id (secure method)
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Exam data with questions
   */
  async getExamDataBySession(sessionId) {
    try {
      console.log('🔐 Fetching exam data using session_id:', sessionId);

      const response = await fetch(`${this.baseUrl}/attempt/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch exam data');
      }

      const examData = await response.json();
      console.log('✅ Exam data fetched successfully:', examData);

      // Cache the exam data
      this.sessionDataCache.set(sessionId, {
        data: examData,
        timestamp: Date.now()
      });

      return examData;
    } catch (error) {
      console.error('❌ Failed to fetch exam data by session:', error);
      throw error;
    }
  }

  /**
   * Get session status and metadata
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Session status and metadata
   */
  async getSessionStatus(sessionId) {
    try {
      console.log('📊 Fetching session status:', sessionId);

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch session status');
      }

      const statusData = await response.json();
      console.log('📊 Session status:', statusData);

      return statusData;
    } catch (error) {
      console.error('❌ Failed to fetch session status:', error);
      throw error;
    }
  }

  /**
   * Get session with exam data (combined call)
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Combined session and exam data
   */
  async getSessionWithExamData(sessionId) {
    try {
      // Check cache first
      const cached = this.getCachedData(sessionId);
      if (cached) {
        return cached;
      }

      const response = await fetch(`${this.baseUrl}/attempt/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch session data');
      }

      const fullData = await response.json();
      console.log('✅ Full session data fetched:', fullData);

      // Cache the data
      this.sessionDataCache.set(sessionId, {
        data: fullData,
        timestamp: Date.now()
      });

      return fullData;
    } catch (error) {
      console.error('❌ Failed to fetch session with exam data:', error);
      throw error;
    }
  }

  /**
   * Validate session access
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Session validation result
   */
  async validateSessionAccess(sessionId) {
    try {
      console.log('🔐 Validating session access:', sessionId);

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/validate`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Session validation failed');
      }

      const validationResult = await response.json();
      console.log('✅ Session validation result:', validationResult);

      return validationResult;
    } catch (error) {
      console.error('❌ Session validation failed:', error);
      throw error;
    }
  }

  /**
   * Get student's current answers for session
   * @param {string} sessionId - The exam session ID
   * @returns {Promise<Object>} Current answers
   */
  async getSessionAnswers(sessionId) {
    try {
      console.log('📝 Fetching session answers:', sessionId);

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/answers`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to fetch session answers');
      }

      const answersData = await response.json();
      console.log('📝 Session answers:', answersData);

      return answersData;
    } catch (error) {
      console.error('❌ Failed to fetch session answers:', error);
      throw error;
    }
  }

  /**
   * Save answer for session
   * @param {string} sessionId - The exam session ID
   * @param {string} questionId - The question ID
   * @param {string} answer - The answer content
   * @returns {Promise<Object>} Save result
   */
  async saveSessionAnswer(sessionId, questionId, answer) {
    try {
      console.log('💾 Saving session answer:', { sessionId, questionId });

      const response = await fetch(`${this.baseUrl}/exams/session/exam-session/${sessionId}/answer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          question_id: questionId,
          answer: answer
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to save answer');
      }

      const saveResult = await response.json();
      console.log('✅ Answer saved successfully:', saveResult);

      return saveResult;
    } catch (error) {
      console.error('❌ Failed to save session answer:', error);
      throw error;
    }
  }

  /**
   * Get cached exam data if available and not expired
   * @param {string} sessionId - The session ID
   * @returns {*} Cached data or null
   */
  getCachedData(sessionId) {
    const cached = this.sessionDataCache.get(sessionId);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Clear cache for specific session
   * @param {string} sessionId - The session ID
   */
  clearSessionCache(sessionId) {
    this.sessionDataCache.delete(sessionId);
    console.log('🧹 Cleared cache for session:', sessionId);
  }

  /**
   * Clear all cached data
   */
  clearAllCache() {
    console.log('🧹 Clearing all session data cache');
    this.sessionDataCache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      cachedSessions: this.sessionDataCache.size,
      cacheKeys: Array.from(this.sessionDataCache.keys()),
      cacheTimeout: this.cacheTimeout
    };
  }

  /**
   * Cleanup service resources
   */
  cleanup() {
    console.log('🧹 Cleaning up ExamSessionDataService');
    this.clearAllCache();
  }
}

// Create singleton instance
const examSessionDataService = new ExamSessionDataService();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    examSessionDataService.cleanup();
  });
}

export default examSessionDataService;
