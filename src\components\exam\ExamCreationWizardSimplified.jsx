import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { FiChe<PERSON>, FiArrowLeft, FiArrowRight } from 'react-icons/fi';

// Import smaller components
import StepIndicator from './StepIndicator';
import ExamDetailsForm from './ExamDetailsForm';
import ExamAssignment from './ExamAssignment';
import QuestionFormManager from './QuestionFormManager';
import QuestionList from './QuestionList';

// Import Redux actions
import { fetchClasses } from '../../store/slices/ClassesSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchChaptersBySubject } from '../../store/slices/ChapterSlice';
import { fetchTopicsByChapter } from '../../store/slices/TopicSlice';
import { fetchSubtopicsByTopic } from '../../store/slices/SubtopicSlice';
import { createExamWithAssignment } from '../../store/slices/ExamSlice';

const ExamCreationWizardSimplified = ({ userType = 'teacher' }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Theme classes
  const themeClasses = {
    input: 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100',
    label: 'text-gray-700 dark:text-gray-300',
    button: 'bg-blue-600 hover:bg-blue-700 text-white',
    card: 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
  };

  // Wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const [questions, setQuestions] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Exam data state with proper initial values to prevent controlled/uncontrolled warnings
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    subjectId: '',
    classNumber: '',
    classroomId: '',
    startDate: '',
    startTime: '',
    duration: 60,
    totalMarks: 100,
    shuffleQuestions: false,
    showResults: true,
    // Additional fields that might be used by ExamDetailsForm
    total_duration: 60,
    start_time: '',
    total_marks: 100
  });

  // Question categorization state with proper initial values
  const [subjectId, setSubjectId] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [topicId, setTopicId] = useState('');
  const [subtopicId, setSubtopicId] = useState('');

  // Assignment type for teachers (classroom vs individual students)
  const [assignmentType, setAssignmentType] = useState('classroom');
  const [selectedStudentIds, setSelectedStudentIds] = useState([]);

  // Redux selectors with correct slice names and safe defaults
  const { classes = [], loading: classesLoading } = useSelector(state => state.classes || {});
  const { subjects = [], loading: subjectsLoading } = useSelector(state => state.subjects || {});
  const { classrooms = [], loading: classroomsLoading } = useSelector(state => state.classroom || {});
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector(state => state.chapters || {});
  const { topicsByChapter = [], loading: topicsLoading } = useSelector(state => state.topics || {});
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector(state => state.subtopics || {});

  // Debug Redux state
  console.log('🔍 Redux state debug:', {
    classroomState: useSelector(state => state.classroom),
    classrooms,
    classroomsLoading
  });

  // Event handlers
  const handleExamDataChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    setExamData(prev => {
      const updated = { ...prev, [name]: newValue };
      console.log('📝 Exam data updated:', { field: name, value: newValue, examData: updated });
      return updated;
    });
  }, []);

  const handleClassChange = useCallback((e) => {
    const classroomId = e.target.value;
    console.log('🏫 Classroom changed:', classroomId);
    setExamData(prev => ({ ...prev, classroomId }));
  }, []);

  const handleSubjectChange = useCallback((e) => {
    const subjectId = e.target.value;
    console.log('📚 Subject changed:', subjectId);
    setExamData(prev => ({ ...prev, subjectId }));
  }, []);

  const handleClassNumberChange = useCallback((e) => {
    const classNumber = e.target.value;
    console.log('🎯 Class number changed:', classNumber);
    setExamData(prev => ({ ...prev, classNumber }));
  }, []);

  const handleQuestionAdd = useCallback((newQuestion) => {
    setQuestions(prev => [...prev, newQuestion]);
  }, []);

  const handleQuestionsChange = useCallback((newQuestions) => {
    setQuestions(newQuestions);
  }, []);

  const handleAIGenerate = useCallback((aiParams) => {
    console.log('AI Generate called with:', aiParams);
    // TODO: Implement AI generation logic
  }, []);

  // Data fetching effects
  useEffect(() => {
    dispatch(fetchClasses());
    dispatch(fetchSubjects());
    dispatch(fetchAllOwnClasses());
  }, [dispatch]);

  useEffect(() => {
    if (subjectId) {
      dispatch(fetchChaptersBySubject({ subjectId }));
      setChapterId('');
      setTopicId('');
      setSubtopicId('');
    }
  }, [subjectId, dispatch]);

  useEffect(() => {
    if (chapterId) {
      dispatch(fetchTopicsByChapter({ chapterId }));
      setTopicId('');
      setSubtopicId('');
    }
  }, [chapterId, dispatch]);

  useEffect(() => {
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId }));
      setSubtopicId('');
    }
  }, [topicId, dispatch]);

  // Navigation handlers
  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Validate required fields
      if (!examData.title || !examData.classroomId || !questions.length) {
        setSubmitError('Please fill in all required fields and add at least one question');
        return;
      }

      // Validate questions have required fields
      const invalidQuestions = questions.filter(q =>
        !q.text || !q.Type || !q.answer || q.marks <= 0
      );

      if (invalidQuestions.length > 0) {
        setSubmitError('All questions must have text, type, answer, and valid marks');
        return;
      }

      // Calculate total marks
      const totalMarks = questions.reduce((sum, q) => sum + (q.marks || 1), 0);

      // Debug: Log available classes
      console.log('📚 Available classes:', classes);
      console.log('🎯 Selected class number:', examData.classNumber);

      // Find the selected class with safety checks
      const selectedClass = examData.classNumber && Array.isArray(classes) ?
        classes.find(c => c.class_number === parseInt(examData.classNumber) || c.ClassNo === examData.classNumber) :
        null;

      console.log('✅ Selected class object:', selectedClass);

      // Helper function to map question types to backend-accepted values
      const mapQuestionType = (type) => {
        const normalizedType = String(type || '').toUpperCase();

        const typeMap = {
          'MCQS': 'MCQS', 'SHORT': 'SHORT', 'LONG': 'LONG',
          'MCQ': 'MCQS', 'MULTIPLE_CHOICE': 'MCQS',
          'SHORT_ANSWER': 'SHORT', 'LONG_ANSWER': 'LONG',
          'DESCRIPTIVE': 'LONG', 'DESCRIPTION': 'LONG', 'ESSAY': 'LONG',
          'TEXT': 'SHORT', 'TEXTUAL': 'SHORT'
        };

        const mappedType = typeMap[normalizedType];
        if (normalizedType !== mappedType) {
          console.log(`🔄 Question type mapped: ${type} → ${mappedType}`);
        }
        return mappedType || 'MCQS';
      };

      // Format questions to match API requirements
      const formattedQuestions = questions.map(question => ({
        text: question.text,
        answer: question.answer || question.correct_answer || '',
        Type: mapQuestionType(question.type || question.Type || 'MCQS'), // ✅ Use mapped type
        Level: question.Level || 'EASY',
        imageUrl: question.imageUrl || '',
        class_id: selectedClass?.id || null,
        subject_id: examData.subjectId,
        chapter_id: question.chapter_id || null,
        topic_id: question.topic_id || null,
        subtopic_id: question.subtopic_id || null,
        marks: question.marks || 1,
        options: question.options || []
      }));

      // Prepare exam payload
      const examPayload = {
        title: examData.title,
        description: examData.description,
        total_marks: totalMarks,
        total_duration: examData.duration,
        // Use start_time from ExamDetailsForm (already in UTC) or fallback to current time
        start_time: examData.start_time || new Date().toISOString(),
        questions: formattedQuestions,
        assignment: assignmentType === 'classroom'
          ? { classroom_id: examData.classroomId }
          : { student_ids: selectedStudentIds }
      };

      console.log('🚀 Creating exam with payload:', JSON.stringify(examPayload, null, 2));
      console.log('📋 Questions data:', JSON.stringify(formattedQuestions, null, 2));

      const result = await dispatch(createExamWithAssignment(examPayload)).unwrap();
      console.log('✅ Exam created successfully:', result);

      // Navigate to exams page on success
      navigate('/teacher/exams');
    } catch (error) {
      console.error('❌ Failed to create exam:', error);
      setSubmitError(error.message || 'Failed to create exam. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validation
  const isStepValid = (step) => {
    switch (step) {
      case 1:
        // Step 1: Basic exam details (title, subject, grade, timing)
        const step1Valid = examData.title && examData.subjectId && examData.classNumber && examData.start_time;
        console.log('🔍 Step 1 validation:', {
          title: examData.title,
          subjectId: examData.subjectId,
          classNumber: examData.classNumber,
          startTime: examData.start_time,
          step1Valid
        });
        return step1Valid;
      case 2:
        // Step 2: Assignment (classroom or students)
        const step2Valid = assignmentType === 'classroom'
          ? examData.classroomId
          : selectedStudentIds.length > 0;
        console.log('🔍 Step 2 validation:', {
          assignmentType,
          classroomId: examData.classroomId,
          selectedStudentsCount: selectedStudentIds.length,
          step2Valid
        });
        return step2Valid;
      case 3:
        // Step 3: Questions
        return questions.length > 0;
      case 4:
        // Step 4: Final review - all previous steps must be valid
        const step4Valid = examData.title && questions.length > 0 &&
          (assignmentType === 'classroom' ? examData.classroomId : selectedStudentIds.length > 0);
        console.log('🔍 Step 4 validation:', {
          title: examData.title,
          questionsCount: questions.length,
          assignmentType,
          classroomId: examData.classroomId,
          selectedStudentsCount: selectedStudentIds.length,
          step4Valid
        });
        return step4Valid;
      default:
        return false;
    }
  };

  const steps = [
    { number: 1, title: 'Exam Details', description: 'Basic information' },
    { number: 2, title: 'Assignment', description: 'Choose who takes the exam' },
    { number: 3, title: 'Questions', description: 'Add questions' },
    { number: 4, title: 'Review', description: 'Final review' }
  ];

  // Show loading state while data is being fetched
  if (classesLoading || subjectsLoading || classroomsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading exam creation form...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Create New Exam</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Set up your exam step by step</p>
        </div>

        {/* Step Indicator */}
        <StepIndicator steps={steps} currentStep={currentStep} />

        {/* Content */}
        <div className="mt-8">
          {currentStep === 1 && (
            <ExamDetailsForm
              exam={examData}
              onExamChange={handleExamDataChange}
              subjects={subjects || []}
              classes={classes || []}
              subjectId={examData.subjectId || ''}
              classNumber={examData.classNumber || ''}
              onSubjectChange={handleSubjectChange}
              onClassNumberChange={handleClassNumberChange}
            />
          )}

          {currentStep === 2 && (
            <ExamAssignment
              assignmentType={assignmentType}
              onAssignmentTypeChange={setAssignmentType}
              classId={examData.classroomId}
              onClassChange={handleClassChange}
              selectedStudentIds={selectedStudentIds}
              onSelectedStudentsChange={setSelectedStudentIds}
            />
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <QuestionFormManager
                subjects={subjects}
                subjectId={subjectId}
                setSubjectId={setSubjectId}
                chaptersBySubject={chaptersBySubject}
                topicsByChapter={topicsByChapter}
                subtopicsByTopic={subtopicsByTopic}
                chapterId={chapterId}
                topicId={topicId}
                subtopicId={subtopicId}
                setChapterId={setChapterId}
                setTopicId={setTopicId}
                setSubtopicId={setSubtopicId}
                chaptersLoading={chaptersLoading}
                topicsLoading={topicsLoading}
                subtopicsLoading={subtopicsLoading}
                themeClasses={themeClasses}
                userType={userType}
                gradeClasses={classes}
                onQuestionAdd={handleQuestionAdd}
                onAIGenerate={handleAIGenerate}
              />
              <QuestionList
                questions={questions}
                onQuestionsChange={handleQuestionsChange}
                onEditQuestion={() => {}}
                onDeleteQuestion={(index) => {
                  setQuestions(prev => prev.filter((_, i) => i !== index));
                }}
                themeClasses={themeClasses}
              />
            </div>
          )}

          {currentStep === 4 && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Review Your Exam</h3>

              {submitError && (
                <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-red-600 dark:text-red-400 text-sm">{submitError}</p>
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Exam Title:</h4>
                  <p className="text-gray-600 dark:text-gray-400">{examData.title}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Questions:</h4>
                  <p className="text-gray-600 dark:text-gray-400">{questions.length} questions added</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Duration:</h4>
                  <p className="text-gray-600 dark:text-gray-400">{examData.duration} minutes</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Total Marks:</h4>
                  <p className="text-gray-600 dark:text-gray-400">{questions.reduce((sum, q) => sum + (q.marks || 1), 0)} marks</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Assigned to:</h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {assignmentType === 'classroom'
                      ? (Array.isArray(classrooms) && classrooms.find(c => c.id === examData.classroomId)?.name || 'Selected classroom')
                      : `${selectedStudentIds.length} selected students`
                    }
                  </p>
                </div>
              </div>

              {/* Student Assignment Selector for individual student assignments */}
              {assignmentType === 'students' && (
                <div className="mt-6">
                  <StudentAssignmentSelector
                    assignmentType={assignmentType}
                    onAssignmentTypeChange={setAssignmentType}
                    selectedStudentIds={selectedStudentIds}
                    onSelectedStudentsChange={setSelectedStudentIds}
                    classId={examData.classroomId}
                    onClassIdChange={(e) => setExamData(prev => ({ ...prev, classroomId: e.target.value }))}
                    classrooms={classrooms}
                    themeClasses={themeClasses}
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FiArrowLeft className="w-4 h-4" />
            Previous
          </button>

          {currentStep < 4 ? (
            <div className="flex flex-col items-end gap-2">
              {!isStepValid(currentStep) && (
                <div className="text-xs text-red-500 dark:text-red-400">
                  Please complete all required fields to continue
                </div>
              )}
              <button
                onClick={handleNext}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <FiArrowRight className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting || !isStepValid(3)}
              className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Creating...
                </>
              ) : (
                <>
                  <FiCheck className="w-4 h-4" />
                  Create Exam
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExamCreationWizardSimplified;
