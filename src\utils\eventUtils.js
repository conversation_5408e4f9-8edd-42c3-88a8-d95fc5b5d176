// Event category color mapping - Only 4 categories supported
export const getCategoryColor = (category) => {
  const colors = {
    workshop: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',
    conference: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',
    webinar: 'bg-gradient-to-r from-green-500 to-green-600 text-white',
    competition: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white'
  };

  return colors[category?.toLowerCase()] || colors.workshop; // Default to workshop
};

// Event status badge color mapping
export const getStatusBadgeColor = (status) => {
  const colors = {
    published: 'bg-gradient-to-r from-green-500 to-green-600 text-white',
    draft: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',
    cancelled: 'bg-gradient-to-r from-red-500 to-red-600 text-white',
    completed: 'bg-gradient-to-r from-gray-500 to-gray-600 text-white',
    ongoing: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
  };

  return colors[status?.toLowerCase()] || colors.draft;
};

// Get event timing status based on current time vs start/end times
export const getEventTimingStatus = (event) => {
  const now = new Date();
  const startTime = event.start_datetime ? new Date(event.start_datetime) : null;
  const endTime = event.end_datetime ? new Date(event.end_datetime) : null;

  // If no start time, consider it upcoming
  if (!startTime) {
    return {
      status: 'upcoming',
      label: 'Upcoming',
      color: 'bg-blue-100 text-blue-800',
      icon: 'clock'
    };
  }

  // If event has ended
  if (endTime && now > endTime) {
    return {
      status: 'ended',
      label: 'Ended',
      color: 'bg-gray-100 text-gray-800',
      icon: 'check-circle'
    };
  }

  // If event is currently ongoing
  if (now >= startTime && (!endTime || now <= endTime)) {
    return {
      status: 'ongoing',
      label: 'Live Now',
      color: 'bg-green-100 text-green-800',
      icon: 'play-circle'
    };
  }

  // If event is upcoming
  if (now < startTime) {
    return {
      status: 'upcoming',
      label: 'Upcoming',
      color: 'bg-blue-100 text-blue-800',
      icon: 'clock'
    };
  }

  // Fallback
  return {
    status: 'upcoming',
    label: 'Upcoming',
    color: 'bg-blue-100 text-blue-800',
    icon: 'clock'
  };
};

// Get time remaining until event starts or ends
export const getEventTimeRemaining = (event) => {
  const now = new Date();
  const startTime = event.start_datetime ? new Date(event.start_datetime) : null;
  const endTime = event.end_datetime ? new Date(event.end_datetime) : null;

  if (!startTime) return null;

  // If event hasn't started yet
  if (now < startTime) {
    const diffMs = startTime - now;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffDays > 0) {
      return `Starts in ${diffDays} day${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `Starts in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    } else if (diffMinutes > 0) {
      return `Starts in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      return 'Starting soon';
    }
  }

  // If event is ongoing
  if (endTime && now >= startTime && now <= endTime) {
    const diffMs = endTime - now;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `Ends in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    } else if (diffMinutes > 0) {
      return `Ends in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      return 'Ending soon';
    }
  }

  // If event has ended
  if (endTime && now > endTime) {
    return 'Event ended';
  }

  return null;
};

// Get registration status based on current time vs registration deadline
export const getRegistrationStatus = (event) => {
  const now = new Date();
  const registrationDeadline = event.registration_deadline ? new Date(event.registration_deadline) : null;
  const eventStartTime = event.start_datetime ? new Date(event.start_datetime) : null;

  // If no registration deadline, use event start time
  const deadline = registrationDeadline || eventStartTime;

  if (!deadline) {
    return {
      status: 'open',
      label: 'Registration Open',
      color: 'bg-green-100 text-green-800',
      icon: 'user-plus'
    };
  }

  // If registration has ended
  if (now > deadline) {
    return {
      status: 'closed',
      label: 'Registration Closed',
      color: 'bg-red-100 text-red-800',
      icon: 'x-circle'
    };
  }

  // If registration is closing soon (within 24 hours)
  const hoursUntilDeadline = (deadline - now) / (1000 * 60 * 60);
  if (hoursUntilDeadline <= 24) {
    return {
      status: 'closing-soon',
      label: 'Closing Soon',
      color: 'bg-yellow-100 text-yellow-800',
      icon: 'clock'
    };
  }

  // Registration is open
  return {
    status: 'open',
    label: 'Registration Open',
    color: 'bg-green-100 text-green-800',
    icon: 'user-plus'
  };
};

// Filter events based on search term and filters
export const filterEvents = (events, { searchTerm, category, status, date }) => {
  return events.filter(event => {
    // Search filter
    const matchesSearch = !searchTerm ||
      event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.name?.toLowerCase().includes(searchTerm.toLowerCase());

    // Category filter
    const matchesCategory = category === 'all' || event.category?.toLowerCase() === category;
    
    // Status filter
    const matchesStatus = status === 'all' || event.status?.toLowerCase() === status;

    // Date filter
    let matchesDate = true;
    if (date !== 'all') {
      const eventDate = new Date(event.start_datetime);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

      switch (date) {
        case 'today':
          matchesDate = eventDate >= today && eventDate < tomorrow;
          break;
        case 'tomorrow':
          matchesDate = eventDate >= tomorrow && eventDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'this_week':
          matchesDate = eventDate >= today && eventDate < nextWeek;
          break;
        case 'upcoming':
          matchesDate = eventDate >= today;
          break;
        default:
          matchesDate = true;
      }
    }

    return matchesSearch && matchesCategory && matchesStatus && matchesDate;
  });
};

// Get filter options for dropdowns - Only 4 categories supported
export const getFilterOptions = () => ({
  categories: [
    { value: 'all', label: 'All Categories' },
    { value: 'workshop', label: 'Workshops' },
    { value: 'conference', label: 'Conferences' },
    { value: 'webinar', label: 'Webinars' },
    { value: 'competition', label: 'Competitions' }
  ],
  statuses: [
    { value: 'all', label: 'All Status' },
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' },
    { value: 'cancelled', label: 'Cancelled' }
  ],
  dates: [
    { value: 'all', label: 'All Dates' },
    { value: 'today', label: 'Today' },
    { value: 'tomorrow', label: 'Tomorrow' },
    { value: 'this_week', label: 'This Week' },
    { value: 'upcoming', label: 'Upcoming' }
  ]
});
