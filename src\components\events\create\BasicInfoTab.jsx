import React from 'react';
import { FiAward } from 'react-icons/fi';
import { EVENT_CATEGORY_OPTIONS, EVENT_CATEGORIES } from '../../../constants/eventCategories';

// Helper component for required field labels
const RequiredFieldLabel = ({ children, required = false, className = "" }) => (
  <label className={`block text-sm font-medium text-gray-700 mb-2 ${className}`}>
    {children}
    {required && <span className="text-red-500 ml-1">*</span>}
  </label>
);

const BasicInfoTab = ({ 
  formData, 
  touchedFields, 
  hasAttemptedSubmit, 
  referenceExams,
  onChange,
  onFieldTouch 
}) => {
  // Helper to determine if we should show validation styling
  const shouldShowValidation = (fieldName, value) => {
    return (touchedFields[fieldName] || hasAttemptedSubmit) && (!value || value.toString().trim() === '');
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <RequiredFieldLabel required>Event Title</RequiredFieldLabel>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => onChange('title', e.target.value)}
            onBlur={() => onFieldTouch('title')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('title', formData.title) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
            placeholder="Enter event title"
          />
          {shouldShowValidation('title', formData.title) && (
            <p className="text-red-500 text-xs mt-1">Event title is required</p>
          )}
        </div>

        <div>
          <RequiredFieldLabel required>Category</RequiredFieldLabel>
          <select
            value={formData.category}
            onChange={(e) => onChange('category', e.target.value)}
            onBlur={() => onFieldTouch('category')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('category', formData.category)
                ? 'border-red-300 bg-red-50'
                : 'border-gray-300'
            }`}
          >
            <option value="">Select category</option>
            {EVENT_CATEGORY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {shouldShowValidation('category', formData.category) && (
            <p className="text-red-500 text-xs mt-1">Category is required</p>
          )}
        </div>

        {/* Exam Selection - Only show for Competition category */}
        {formData.category === EVENT_CATEGORIES.COMPETITION && (
          <div>
            <RequiredFieldLabel required>Competition Exam</RequiredFieldLabel>
            <select
              value={formData.exam_id || ''}
              onChange={(e) => onChange('exam_id', e.target.value)}
              onBlur={() => onFieldTouch('exam_id')}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                shouldShowValidation('exam_id', formData.exam_id) 
                  ? 'border-red-300 bg-red-50' 
                  : 'border-gray-300'
              }`}
            >
              <option value="">Select exam for competition</option>
              {referenceExams.map((exam) => (
                <option key={exam.id} value={exam.id}>
                  {exam.title}
                </option>
              ))}
            </select>
            {shouldShowValidation('exam_id', formData.exam_id) && (
              <p className="text-red-500 text-xs mt-1">Competition exam is required</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Select the exam that will be used for this competition event
            </p>
            {formData.exam_id && (
              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-xs text-blue-700">
                  💡 <strong>Tip:</strong> When you set the start time, the end time will be automatically calculated based on this exam's duration
                  {(() => {
                    const selectedExam = referenceExams.find(exam => exam.id === formData.exam_id);
                    return selectedExam ? ` (${selectedExam.total_duration} minutes)` : '';
                  })()}
                </p>
              </div>
            )}
          </div>
        )}

        <div>
          <RequiredFieldLabel>Maximum Attendees</RequiredFieldLabel>
          <input
            type="number"
            value={formData.max_attendees}
            onChange={(e) => onChange('max_attendees', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Leave empty for unlimited"
          />
        </div>

        <div>
          <RequiredFieldLabel required>Start Date & Time</RequiredFieldLabel>
          <input
            type="datetime-local"
            value={formData.start_datetime}
            onChange={(e) => onChange('start_datetime', e.target.value)}
            onBlur={() => onFieldTouch('start_datetime')}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('start_datetime', formData.start_datetime) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
          />
          {shouldShowValidation('start_datetime', formData.start_datetime) && (
            <p className="text-red-500 text-xs mt-1">Start date and time is required</p>
          )}
        </div>

        <div>
          <RequiredFieldLabel required>End Date & Time</RequiredFieldLabel>
          <div className="relative">
            <input
              type="datetime-local"
              value={formData.end_datetime}
              onChange={(e) => onChange('end_datetime', e.target.value)}
              onBlur={() => onFieldTouch('end_datetime')}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                shouldShowValidation('end_datetime', formData.end_datetime)
                  ? 'border-red-300 bg-red-50'
                  : 'border-gray-300'
              } ${
                formData.category === EVENT_CATEGORIES.COMPETITION && formData.exam_id && formData.start_datetime
                  ? 'bg-blue-50 border-blue-300'
                  : ''
              }`}
              readOnly={formData.category === EVENT_CATEGORIES.COMPETITION && formData.exam_id && formData.start_datetime}
            />
            {formData.category === EVENT_CATEGORIES.COMPETITION && formData.exam_id && formData.start_datetime && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Auto-calculated
                </span>
              </div>
            )}
          </div>
          {shouldShowValidation('end_datetime', formData.end_datetime) && (
            <p className="text-red-500 text-xs mt-1">End date and time is required</p>
          )}
          {formData.start_datetime && formData.end_datetime &&
           new Date(formData.end_datetime) <= new Date(formData.start_datetime) && (
            <p className="text-red-500 text-xs mt-1">
              End date must be after start date
            </p>
          )}
          {formData.category === EVENT_CATEGORIES.COMPETITION && formData.exam_id && formData.start_datetime && (
            <p className="text-blue-600 text-xs mt-1">
              ✨ End time automatically calculated based on exam duration
              {(() => {
                const selectedExam = referenceExams.find(exam => exam.id === formData.exam_id);
                return selectedExam ? ` (${selectedExam.total_duration} minutes)` : '';
              })()}
            </p>
          )}
        </div>

        <div className="md:col-span-2">
          <RequiredFieldLabel required>Short Description</RequiredFieldLabel>
          <textarea
            value={formData.short_description}
            onChange={(e) => onChange('short_description', e.target.value)}
            onBlur={() => onFieldTouch('short_description')}
            rows={2}
            maxLength={150}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('short_description', formData.short_description) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
            placeholder="Brief description for event listings"
          />
          {shouldShowValidation('short_description', formData.short_description) && (
            <p className="text-red-500 text-xs mt-1">Short description is required</p>
          )}
          <p className="text-sm text-gray-500 mt-1">{formData.short_description.length}/150 characters</p>
        </div>

        <div className="md:col-span-2">
          <RequiredFieldLabel required>Description</RequiredFieldLabel>
          <textarea
            value={formData.description}
            onChange={(e) => onChange('description', e.target.value)}
            onBlur={() => onFieldTouch('description')}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              shouldShowValidation('description', formData.description) 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300'
            }`}
            placeholder="Detailed description of the event"
          />
          {shouldShowValidation('description', formData.description) && (
            <p className="text-red-500 text-xs mt-1">Description is required</p>
          )}
        </div>

        <div className="md:col-span-2">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_featured"
                checked={formData.is_featured}
                onChange={(e) => onChange('is_featured', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-900">
                Featured Event
              </label>
            </div>
            
            {formData.category === EVENT_CATEGORIES.COMPETITION && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_competition"
                  checked={formData.is_competition}
                  onChange={(e) => onChange('is_competition', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_competition" className="ml-2 block text-sm text-gray-900">
                  Competition Event
                </label>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
