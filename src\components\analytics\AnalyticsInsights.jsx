import React from 'react';
import {
  FiTrendingUp,
  FiTarget,
  FiInfo,
  FiCheckCircle,
  FiAlertTriangle,
  FiStar,
  FiArrowRight
} from 'react-icons/fi';

/**
 * Analytics Insights Component
 * Displays personalized insights and recommendations
 */
const AnalyticsInsights = ({ 
  data, 
  loading = false, 
  className = '' 
}) => {
  if (loading) {
    return (
      <div className={`animate-pulse space-y-4 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
        ))}
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No insights available</p>
      </div>
    );
  }

  // Extract insights from data
  const strengths = data.academic_strength_areas || [];
  const improvements = data.improvement_opportunities || [];
  const recommendations = data.personalized_recommendations || [];
  const goals = data.current_goals || [];

  // Generate dynamic insights based on performance
  const generateInsights = () => {
    const insights = [];
    
    // Performance insight
    const overallScore = data.overall_performance_score || 0;
    if (overallScore >= 90) {
      insights.push({
        type: 'success',
        icon: FiStar,
        title: 'Excellent Performance',
        message: 'You\'re performing exceptionally well! Keep up the great work.',
        action: 'Consider taking on advanced challenges'
      });
    } else if (overallScore >= 75) {
      insights.push({
        type: 'info',
        icon: FiTrendingUp,
        title: 'Strong Performance',
        message: 'You\'re doing well with room for improvement in specific areas.',
        action: 'Focus on weaker subjects for balanced growth'
      });
    } else {
      insights.push({
        type: 'warning',
        icon: FiTarget,
        title: 'Improvement Needed',
        message: 'There\'s significant room for improvement in your performance.',
        action: 'Consider additional study time and support'
      });
    }

    // Study pattern insight
    const studyHours = data.subject_analytics?.total_study_hours || 0;
    if (studyHours < 20) {
      insights.push({
        type: 'warning',
        icon: FiAlertTriangle,
        title: 'Low Study Hours',
        message: 'Your study time is below recommended levels.',
        action: 'Increase daily study time by 30 minutes'
      });
    } else if (studyHours > 50) {
      insights.push({
        type: 'info',
        icon: FiCheckCircle,
        title: 'High Study Commitment',
        message: 'You\'re putting in excellent study hours.',
        action: 'Ensure you\'re taking adequate breaks'
      });
    }

    // Competition insight
    const competitionPerformance = data.competition_analytics?.summary?.average_score || 0;
    if (competitionPerformance >= 85) {
      insights.push({
        type: 'success',
        icon: FiStar,
        title: 'Competition Excellence',
        message: 'Outstanding performance in competitions!',
        action: 'Consider participating in higher-level competitions'
      });
    }

    return insights;
  };

  const insights = generateInsights();

  return (
    <div className={className}>
      {/* AI-Generated Insights */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">
          AI-Powered Insights
        </h3>
        <div className="space-y-4">
          {insights.map((insight, index) => (
            <div
              key={index}
              className={`p-4 rounded-xl border ${
                insight.type === 'success'
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : insight.type === 'warning'
                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                  : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
              }`}
            >
              <div className="flex items-start">
                <div className={`p-2 rounded-lg mr-3 ${
                  insight.type === 'success'
                    ? 'bg-green-100 dark:bg-green-900/40'
                    : insight.type === 'warning'
                    ? 'bg-yellow-100 dark:bg-yellow-900/40'
                    : 'bg-blue-100 dark:bg-blue-900/40'
                }`}>
                  <insight.icon className={`w-5 h-5 ${
                    insight.type === 'success'
                      ? 'text-green-600 dark:text-green-400'
                      : insight.type === 'warning'
                      ? 'text-yellow-600 dark:text-yellow-400'
                      : 'text-blue-600 dark:text-blue-400'
                  }`} />
                </div>
                <div className="flex-1">
                  <h4 className={`font-semibold mb-1 ${
                    insight.type === 'success'
                      ? 'text-green-800 dark:text-green-200'
                      : insight.type === 'warning'
                      ? 'text-yellow-800 dark:text-yellow-200'
                      : 'text-blue-800 dark:text-blue-200'
                  }`}>
                    {insight.title}
                  </h4>
                  <p className={`text-sm mb-2 ${
                    insight.type === 'success'
                      ? 'text-green-700 dark:text-green-300'
                      : insight.type === 'warning'
                      ? 'text-yellow-700 dark:text-yellow-300'
                      : 'text-blue-700 dark:text-blue-300'
                  }`}>
                    {insight.message}
                  </p>
                  <div className="flex items-center">
                    <FiArrowRight className={`w-4 h-4 mr-1 ${
                      insight.type === 'success'
                        ? 'text-green-600 dark:text-green-400'
                        : insight.type === 'warning'
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-blue-600 dark:text-blue-400'
                    }`} />
                    <span className={`text-xs font-medium ${
                      insight.type === 'success'
                        ? 'text-green-600 dark:text-green-400'
                        : insight.type === 'warning'
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-blue-600 dark:text-blue-400'
                    }`}>
                      {insight.action}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Strengths and Improvements Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Academic Strengths */}
        {strengths.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <FiCheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h4 className="font-semibold text-gray-800 dark:text-gray-100">
                Academic Strengths
              </h4>
            </div>
            <ul className="space-y-2">
              {strengths.map((strength, index) => (
                <li key={index} className="flex items-center text-green-700 dark:text-green-300">
                  <FiStar className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="text-sm">{strength}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Improvement Opportunities */}
        {improvements.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <FiTarget className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
              <h4 className="font-semibold text-gray-800 dark:text-gray-100">
                Improvement Opportunities
              </h4>
            </div>
            <ul className="space-y-2">
              {improvements.map((improvement, index) => (
                <li key={index} className="flex items-center text-yellow-700 dark:text-yellow-300">
                  <FiTrendingUp className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="text-sm">{improvement}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Personalized Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-center mb-4">
            <FiInfo className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h4 className="font-semibold text-gray-800 dark:text-gray-100">
              Personalized Recommendations
            </h4>
          </div>
          <div className="space-y-3">
            {recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/40 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                  <span className="text-xs font-bold text-blue-600 dark:text-blue-400">
                    {index + 1}
                  </span>
                </div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {recommendation}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Current Goals */}
      {goals.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center mb-4">
            <FiTarget className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
            <h4 className="font-semibold text-gray-800 dark:text-gray-100">
              Current Goals
            </h4>
          </div>
          <div className="space-y-4">
            {goals.map((goal, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="font-medium text-gray-800 dark:text-gray-100">
                    {goal.title}
                  </h5>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {goal.current_progress}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${goal.current_progress}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Target: {goal.target}</span>
                  <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsInsights;
