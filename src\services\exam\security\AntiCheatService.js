/**
 * Anti-Cheating Service
 * Implements comprehensive cheating detection and prevention measures
 * 
 * Features:
 * - Browser lockdown (disable dev tools, right-click, shortcuts)
 * - Tab/window monitoring
 * - Behavioral analysis
 * - Network monitoring
 * - Real-time reporting
 */

import ExamWebSocketService from '../websocket/ExamWebSocketService';

// Cheating detection types
export const CHEAT_TYPES = {
  DEV_TOOLS: 'dev_tools_attempt',
  TAB_SWITCH: 'tab_switch',
  WINDOW_FOCUS_LOST: 'window_focus_lost',
  RIGHT_CLICK: 'right_click_attempt',
  COPY_PASTE: 'copy_paste_attempt',
  KEYBOARD_SHORTCUT: 'keyboard_shortcut',
  MOUSE_LEAVE: 'mouse_leave_exam',
  RAPID_ANSWERS: 'rapid_answer_changes',
  SUSPICIOUS_TIMING: 'suspicious_timing_pattern',
  NETWORK_CHANGE: 'network_change_detected',
  FULLSCREEN_EXIT: 'fullscreen_exit',
  MULTIPLE_TABS: 'multiple_tabs_detected'
};

class AntiCheatService {
  constructor() {
    this.isActive = false;
    this.isExamStarted = false; // Track if exam has actually started
    this.listeners = new Map();
    this.behaviorData = {
      answerChanges: [],
      mouseMovements: [],
      keystrokes: [],
      focusEvents: [],
      networkEvents: []
    };

    // Configuration
    this.config = {
      enableDevToolsBlocking: true,
      enableTabMonitoring: true,
      enableMouseTracking: true,
      enableBehaviorAnalysis: true,
      enableNetworkMonitoring: true,
      rapidAnswerThreshold: 3, // answers per 10 seconds
      suspiciousTimingThreshold: 500, // milliseconds
      mouseLeaveGracePeriod: 2000, // milliseconds
      gracePeriodAfterStart: 10000 // 10 seconds grace period after exam starts
    };
    
    // State tracking
    this.isFullscreen = false;
    this.lastAnswerTime = 0;
    this.answerCount = 0;
    this.mouseLeaveTimer = null;
    this.devToolsCheckInterval = null;
    
    // Bind methods
    this.activate = this.activate.bind(this);
    this.deactivate = this.deactivate.bind(this);
    this.reportCheating = this.reportCheating.bind(this);
    this.setExamStarted = this.setExamStarted.bind(this);
  }

  /**
   * Mark that the exam has actually started (student is in exam room)
   */
  setExamStarted(started = true) {
    this.isExamStarted = started;
    console.log(`Anti-cheat: Exam started status set to ${started}`);
  }

  /**
   * Activate anti-cheating measures
   */
  activate() {
    if (this.isActive) {
      console.warn('Anti-cheat service already active');
      return;
    }

    console.log('Activating anti-cheat measures...');
    this.isActive = true;

    // Browser lockdown
    this.enableBrowserLockdown();
    
    // Tab and window monitoring
    this.enableTabMonitoring();
    
    // Mouse and keyboard monitoring
    this.enableInputMonitoring();
    
    // Fullscreen enforcement
    this.enableFullscreenMode();
    
    // Network monitoring
    this.enableNetworkMonitoring();
    
    // Behavioral analysis
    this.enableBehaviorAnalysis();
    
    // Developer tools detection
    this.enableDevToolsDetection();
    
    console.log('Anti-cheat measures activated successfully');
  }

  /**
   * Deactivate anti-cheating measures
   */
  deactivate() {
    if (!this.isActive) return;

    console.log('Deactivating anti-cheat measures...');
    this.isActive = false;
    this.isExamStarted = false;

    // Remove all event listeners
    this.listeners.forEach((listener, event) => {
      document.removeEventListener(event, listener);
      window.removeEventListener(event, listener);
    });
    this.listeners.clear();

    // Clear intervals
    if (this.devToolsCheckInterval) {
      clearInterval(this.devToolsCheckInterval);
      this.devToolsCheckInterval = null;
    }

    // Clear timers
    if (this.mouseLeaveTimer) {
      clearTimeout(this.mouseLeaveTimer);
      this.mouseLeaveTimer = null;
    }

    // Exit fullscreen
    if (document.fullscreenElement) {
      document.exitFullscreen().catch(console.error);
    }

    // Re-enable context menu
    document.oncontextmenu = null;

    console.log('Anti-cheat measures deactivated');
  }

  /**
   * Enable browser lockdown features
   */
  enableBrowserLockdown() {
    // Disable right-click context menu
    const contextMenuHandler = (e) => {
      e.preventDefault();
      this.reportCheating(CHEAT_TYPES.RIGHT_CLICK, {
        target: e.target.tagName,
        timestamp: Date.now()
      });
      return false;
    };
    document.addEventListener('contextmenu', contextMenuHandler);
    this.listeners.set('contextmenu', contextMenuHandler);

    // Disable text selection
    const selectStartHandler = (e) => {
      e.preventDefault();
      return false;
    };
    document.addEventListener('selectstart', selectStartHandler);
    this.listeners.set('selectstart', selectStartHandler);

    // Disable drag and drop
    const dragStartHandler = (e) => {
      e.preventDefault();
      return false;
    };
    document.addEventListener('dragstart', dragStartHandler);
    this.listeners.set('dragstart', dragStartHandler);

    // Disable keyboard shortcuts
    const keydownHandler = (e) => {
      const forbiddenKeys = [
        'F12', // Developer tools
        'F5',  // Refresh
        'F11', // Fullscreen toggle
      ];

      const forbiddenCombos = [
        { ctrl: true, shift: true, key: 'I' }, // Dev tools
        { ctrl: true, shift: true, key: 'C' }, // Console
        { ctrl: true, shift: true, key: 'J' }, // Console
        { ctrl: true, key: 'U' },              // View source
        { ctrl: true, key: 'S' },              // Save page
        { ctrl: true, key: 'A' },              // Select all
        { ctrl: true, key: 'C' },              // Copy
        { ctrl: true, key: 'V' },              // Paste
        { ctrl: true, key: 'X' },              // Cut
        { ctrl: true, key: 'Z' },              // Undo
        { ctrl: true, key: 'Y' },              // Redo
        { alt: true, key: 'Tab' },             // Alt+Tab
        { alt: true, key: 'F4' },              // Alt+F4
      ];

      // Check forbidden keys
      if (forbiddenKeys.includes(e.key)) {
        e.preventDefault();
        this.reportCheating(CHEAT_TYPES.KEYBOARD_SHORTCUT, {
          key: e.key,
          timestamp: Date.now()
        });
        return false;
      }

      // Check forbidden combinations
      for (const combo of forbiddenCombos) {
        if (
          (combo.ctrl === undefined || combo.ctrl === e.ctrlKey) &&
          (combo.shift === undefined || combo.shift === e.shiftKey) &&
          (combo.alt === undefined || combo.alt === e.altKey) &&
          combo.key === e.key
        ) {
          e.preventDefault();
          this.reportCheating(CHEAT_TYPES.KEYBOARD_SHORTCUT, {
            combination: `${combo.ctrl ? 'Ctrl+' : ''}${combo.shift ? 'Shift+' : ''}${combo.alt ? 'Alt+' : ''}${combo.key}`,
            timestamp: Date.now()
          });
          return false;
        }
      }

      // Track copy/paste attempts
      if ((e.ctrlKey && ['c', 'v', 'x'].includes(e.key.toLowerCase())) ||
          (e.metaKey && ['c', 'v', 'x'].includes(e.key.toLowerCase()))) {
        this.reportCheating(CHEAT_TYPES.COPY_PASTE, {
          action: e.key.toLowerCase(),
          timestamp: Date.now()
        });
      }
    };
    document.addEventListener('keydown', keydownHandler);
    this.listeners.set('keydown', keydownHandler);
  }

  /**
   * Enable tab and window monitoring
   */
  enableTabMonitoring() {
    // Tab visibility change detection
    const visibilityChangeHandler = () => {
      if (document.hidden) {
        this.reportCheating(CHEAT_TYPES.TAB_SWITCH, {
          timestamp: Date.now(),
          visibilityState: document.visibilityState
        });
      }
    };
    document.addEventListener('visibilitychange', visibilityChangeHandler);
    this.listeners.set('visibilitychange', visibilityChangeHandler);

    // Window focus loss detection
    const blurHandler = () => {
      this.reportCheating(CHEAT_TYPES.WINDOW_FOCUS_LOST, {
        timestamp: Date.now()
      });
    };
    window.addEventListener('blur', blurHandler);
    this.listeners.set('blur', blurHandler);

    // Prevent navigation
    const beforeUnloadHandler = (e) => {
      e.preventDefault();
      e.returnValue = 'Are you sure you want to leave the exam? This will be reported as suspicious activity.';
      return e.returnValue;
    };
    window.addEventListener('beforeunload', beforeUnloadHandler);
    this.listeners.set('beforeunload', beforeUnloadHandler);
  }

  /**
   * Enable input monitoring
   */
  enableInputMonitoring() {
    // Mouse leave detection
    const mouseLeaveHandler = () => {
      this.mouseLeaveTimer = setTimeout(() => {
        this.reportCheating(CHEAT_TYPES.MOUSE_LEAVE, {
          timestamp: Date.now()
        });
      }, this.config.mouseLeaveGracePeriod);
    };

    const mouseEnterHandler = () => {
      if (this.mouseLeaveTimer) {
        clearTimeout(this.mouseLeaveTimer);
        this.mouseLeaveTimer = null;
      }
    };

    document.addEventListener('mouseleave', mouseLeaveHandler);
    document.addEventListener('mouseenter', mouseEnterHandler);
    this.listeners.set('mouseleave', mouseLeaveHandler);
    this.listeners.set('mouseenter', mouseEnterHandler);

    // Track mouse movements for behavioral analysis
    if (this.config.enableMouseTracking) {
      const mouseMoveHandler = (e) => {
        this.behaviorData.mouseMovements.push({
          x: e.clientX,
          y: e.clientY,
          timestamp: Date.now()
        });

        // Keep only recent data (last 30 seconds)
        const cutoff = Date.now() - 30000;
        this.behaviorData.mouseMovements = this.behaviorData.mouseMovements.filter(
          movement => movement.timestamp > cutoff
        );
      };
      document.addEventListener('mousemove', mouseMoveHandler);
      this.listeners.set('mousemove', mouseMoveHandler);
    }
  }

  /**
   * Enable fullscreen mode enforcement
   */
  enableFullscreenMode() {
    // Request fullscreen
    const requestFullscreen = () => {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    };

    // Monitor fullscreen changes
    const fullscreenChangeHandler = () => {
      const isFullscreen = !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );

      if (this.isFullscreen && !isFullscreen) {
        this.reportCheating(CHEAT_TYPES.FULLSCREEN_EXIT, {
          timestamp: Date.now()
        });
        
        // Re-request fullscreen after a short delay
        setTimeout(requestFullscreen, 1000);
      }
      
      this.isFullscreen = isFullscreen;
    };

    document.addEventListener('fullscreenchange', fullscreenChangeHandler);
    document.addEventListener('mozfullscreenchange', fullscreenChangeHandler);
    document.addEventListener('webkitfullscreenchange', fullscreenChangeHandler);
    document.addEventListener('msfullscreenchange', fullscreenChangeHandler);
    
    this.listeners.set('fullscreenchange', fullscreenChangeHandler);

    // Initial fullscreen request
    setTimeout(requestFullscreen, 1000);
  }

  /**
   * Enable network monitoring
   */
  enableNetworkMonitoring() {
    const onlineHandler = () => {
      this.behaviorData.networkEvents.push({
        type: 'online',
        timestamp: Date.now()
      });
    };

    const offlineHandler = () => {
      this.behaviorData.networkEvents.push({
        type: 'offline',
        timestamp: Date.now()
      });
    };

    window.addEventListener('online', onlineHandler);
    window.addEventListener('offline', offlineHandler);
    this.listeners.set('online', onlineHandler);
    this.listeners.set('offline', offlineHandler);
  }

  /**
   * Enable behavioral analysis
   */
  enableBehaviorAnalysis() {
    // This will be called when answers are updated
    this.analyzeAnswerPattern = (questionId, answer) => {
      const now = Date.now();
      this.behaviorData.answerChanges.push({
        questionId,
        answer,
        timestamp: now
      });

      // Check for rapid answer changes
      const recentChanges = this.behaviorData.answerChanges.filter(
        change => now - change.timestamp < 10000 // Last 10 seconds
      );

      if (recentChanges.length > this.config.rapidAnswerThreshold) {
        this.reportCheating(CHEAT_TYPES.RAPID_ANSWERS, {
          changesInLast10Seconds: recentChanges.length,
          timestamp: now
        });
      }

      // Check for suspicious timing patterns
      if (this.lastAnswerTime > 0) {
        const timeDiff = now - this.lastAnswerTime;
        if (timeDiff < this.config.suspiciousTimingThreshold) {
          this.reportCheating(CHEAT_TYPES.SUSPICIOUS_TIMING, {
            timeBetweenAnswers: timeDiff,
            timestamp: now
          });
        }
      }

      this.lastAnswerTime = now;
      this.answerCount++;
    };
  }

  /**
   * Enable developer tools detection
   */
  enableDevToolsDetection() {
    if (!this.config.enableDevToolsBlocking) return;

    // Method 1: Console detection
    let devtools = { open: false, orientation: null };
    
    this.devToolsCheckInterval = setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          this.reportCheating(CHEAT_TYPES.DEV_TOOLS, {
            method: 'window_size_detection',
            timestamp: Date.now()
          });
        }
      } else {
        devtools.open = false;
      }
    }, 1000);

    // Method 2: Console.log override
    const originalLog = console.log;
    console.log = function(...args) {
      // If console is open, this will be called
      if (args.length === 0) {
        // This is our detection call
        return;
      }
      originalLog.apply(console, args);
    };

    // Trigger detection
    setInterval(() => {
      console.log();
    }, 2000);
  }

  /**
   * Report cheating incident
   */
  reportCheating(type, details = {}) {
    if (!this.isActive) return;

    // Don't report violations if exam hasn't actually started yet
    if (!this.isExamStarted) {
      console.log(`Anti-cheat: Ignoring ${type} violation - exam not started yet`);
      return;
    }

    console.warn('Cheating detected:', type, details);

    // Send to WebSocket service
    ExamWebSocketService.reportCheating(type, details);

    // Store locally for analysis
    this.behaviorData.violations = this.behaviorData.violations || [];
    this.behaviorData.violations.push({
      type,
      details,
      timestamp: Date.now()
    });
  }

  /**
   * Get behavior analysis data
   */
  getBehaviorData() {
    return { ...this.behaviorData };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Check if service is active
   */
  isServiceActive() {
    return this.isActive;
  }
}

// Export singleton instance
export default new AntiCheatService();
