import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createInstituteEvent, selectCreateLoading, selectCreateError, selectCreateSuccess } from '../store/slices/InstituteEventsSlice';
import { getInstituteReferenceExams } from '../store/slices/ExamSlice';
import { cleanEventData, isFormReadyForPublishing } from '../utils/eventValidation';
import { EVENT_CATEGORIES } from '../constants/eventCategories';

const useEventForm = () => {
  const dispatch = useDispatch();
  const isSubmitting = useSelector(selectCreateLoading);
  const createError = useSelector(selectCreateError);
  const createSuccess = useSelector(selectCreateSuccess);

  // Form state
  const [activeTab, setActiveTab] = useState('basic');
  const [showValidationSummary, setShowValidationSummary] = useState(false);
  const [touchedFields, setTouchedFields] = useState({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    short_description: '',
    start_datetime: '',
    end_datetime: '',
    location: '',
    max_attendees: '',
    category: '', // Will be set to one of: WORKSHOP, CONFERENCE, WEBINAR, COMPETITION
    status: 'DRAFT',
    is_featured: false,
    is_public: true,
    requires_approval: false,
    registration_start: '',
    registration_end: '',
    banner_image: '',
    requirements: '',
    tags: '',
    is_competition: false,
    competition_rules: '',
    first_prize: '',
    second_prize: '',
    third_prize: '',
    exam_id: null
  });

  // Speakers and mentors (storing IDs only)
  const [selectedSpeakers, setSelectedSpeakers] = useState([]);
  const [selectedMentors, setSelectedMentors] = useState([]);

  // Tickets
  const [tickets, setTickets] = useState([{
    name: 'General Admission',
    description: '',
    price: 0,
    currency: 'ZAR',
    total_quantity: 100,
    status: 'ACTIVE',
    min_quantity_per_order: 1,
    max_quantity_per_order: 10,
    requires_approval: false,
    is_transferable: true,
    is_refundable: true,
    terms_and_conditions: ''
  }]);

  // Reference exams for competitions
  const [referenceExams, setReferenceExams] = useState([]);

  // Load reference exams on mount
  useEffect(() => {
    const loadReferenceExams = async () => {
      try {
        const response = await dispatch(getInstituteReferenceExams()).unwrap();
        setReferenceExams(response || []);
      } catch (error) {
        console.error('Failed to load reference exams:', error);
        setReferenceExams([]);
      }
    };

    loadReferenceExams();
  }, [dispatch]);

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Auto-set competition flag and location based on category
      if (field === 'category') {
        newData.is_competition = value === EVENT_CATEGORIES.COMPETITION;
        // Auto-set location for competition events
        if (value === EVENT_CATEGORIES.COMPETITION) {
          newData.location = 'OnWebsite';
        }
      }

      // Auto-calculate end time for competition events
      if (newData.category === EVENT_CATEGORIES.COMPETITION) {
        // If start time is being set and we have an exam selected
        if (field === 'start_datetime' && value && newData.exam_id) {
          const selectedExam = referenceExams.find(exam => exam.id === newData.exam_id);
          if (selectedExam && selectedExam.total_duration) {
            const startTime = new Date(value);
            const endTime = new Date(startTime.getTime() + (selectedExam.total_duration * 60 * 1000)); // Convert minutes to milliseconds
            newData.end_datetime = endTime.toISOString().slice(0, 16); // Format for datetime-local input
            console.log('🕐 Auto-calculated end time:', {
              startTime: value,
              duration: selectedExam.total_duration,
              endTime: newData.end_datetime,
              examTitle: selectedExam.title
            });
          }
        }

        // If exam is being selected and we have a start time
        if (field === 'exam_id' && value && newData.start_datetime) {
          const selectedExam = referenceExams.find(exam => exam.id === value);
          if (selectedExam && selectedExam.total_duration) {
            const startTime = new Date(newData.start_datetime);
            const endTime = new Date(startTime.getTime() + (selectedExam.total_duration * 60 * 1000)); // Convert minutes to milliseconds
            newData.end_datetime = endTime.toISOString().slice(0, 16); // Format for datetime-local input
            console.log('🕐 Auto-calculated end time:', {
              startTime: newData.start_datetime,
              duration: selectedExam.total_duration,
              endTime: newData.end_datetime,
              examTitle: selectedExam.title
            });
          }
        }
      }

      return newData;
    });
  };

  // Handle field touch for validation
  const handleFieldTouch = (field) => {
    setTouchedFields(prev => ({ ...prev, [field]: true }));
  };

  // Handle ticket operations
  const handleAddTicket = () => {
    setTickets(prev => [...prev, {
      name: '',
      description: '',
      price: 0,
      currency: 'ZAR',
      total_quantity: 100,
      status: 'ACTIVE',
      min_quantity_per_order: 1,
      max_quantity_per_order: 10,
      requires_approval: false,
      is_transferable: true,
      is_refundable: true,
      terms_and_conditions: ''
    }]);
  };

  const handleRemoveTicket = (index) => {
    setTickets(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (status) => {
    setHasAttemptedSubmit(true);

    // Transform tickets to match API specification
    const transformedTickets = tickets
      .filter(ticket => ticket.name && (ticket.total_quantity !== undefined || ticket.quantity !== undefined))
      .map(ticket => ({
        name: ticket.name,
        description: ticket.description || '',
        price: ticket.price || 0,
        currency: ticket.currency || 'ZAR',
        total_quantity: ticket.total_quantity || ticket.quantity || 100,
        status: ticket.status || 'ACTIVE',
        min_quantity_per_order: ticket.min_quantity_per_order || ticket.min_purchase || 1,
        max_quantity_per_order: ticket.max_quantity_per_order || ticket.max_purchase || 10,
        requires_approval: ticket.requires_approval || false,
        is_transferable: ticket.is_transferable !== undefined ? ticket.is_transferable : true,
        is_refundable: ticket.is_refundable !== undefined ? ticket.is_refundable : true,
        terms_and_conditions: ticket.terms_and_conditions || ''
      }));

    const eventData = {
      ...formData,
      status,
      speaker_ids: selectedSpeakers, // Array of speaker IDs
      mentor_ids: selectedMentors, // Array of mentor IDs
      tickets: transformedTickets
    };

    console.log('🚀 Event payload:', eventData);
    console.log('🎫 Transformed tickets:', transformedTickets);
    const cleanedData = cleanEventData(eventData);
    
    try {
      await dispatch(createInstituteEvent(cleanedData)).unwrap();
      return true;
    } catch (error) {
      console.error('Failed to create event:', error);
      return false;
    }
  };

  // Show validation summary
  const handleShowValidation = () => {
    setHasAttemptedSubmit(true);
    setShowValidationSummary(true);
  };

  // Hide validation summary when form becomes valid
  useEffect(() => {
    if (showValidationSummary && isFormReadyForPublishing(formData)) {
      setShowValidationSummary(false);
    }
  }, [formData, showValidationSummary]);

  return {
    // State
    activeTab,
    formData,
    selectedSpeakers,
    selectedMentors,
    tickets,
    referenceExams,
    showValidationSummary,
    touchedFields,
    hasAttemptedSubmit,
    isSubmitting,
    createError,
    createSuccess,

    // Actions
    setActiveTab,
    handleFieldChange,
    handleFieldTouch,
    setSelectedSpeakers,
    setSelectedMentors,
    setTickets,
    handleAddTicket,
    handleRemoveTicket,
    handleSubmit,
    handleShowValidation
  };
};

export default useEventForm;
