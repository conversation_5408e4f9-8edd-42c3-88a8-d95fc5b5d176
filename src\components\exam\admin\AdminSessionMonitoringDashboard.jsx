/**
 * Admin Session Monitoring Dashboard
 * Comprehensive dashboard for admins to monitor and control exam sessions
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../../providers/ThemeContext';
import { useNotification } from '../../../contexts/NotificationContext';
import {
  getActiveExamSessions,
  viewExamSession,
  forceSubmitExamSession,
  terminateExamSession,
  selectActiveExamSessions,
  selectExamSessionAdminState
} from '../../../store/slices/exam/examSessionAdminSlice';
import {
  FiMonitor,
  FiUsers,
  FiClock,
  FiPlay,
  FiPause,
  FiStop,
  FiEye,
  FiRefreshCw,
  FiAlertTriangle,
  FiCheckCircle,
  FiXCircle,
  FiSettings,
  FiActivity
} from 'react-icons/fi';

const AdminSessionMonitoringDashboard = () => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { showSuccess, showError, showWarning } = useNotification();
  
  const activeSessions = useSelector(selectActiveExamSessions);
  const adminState = useSelector(selectExamSessionAdminState);
  
  const [selectedSession, setSelectedSession] = useState(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState(null); // 'submit', 'terminate'
  const [actionReason, setActionReason] = useState('');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Theme-based styling
  const isDark = currentTheme === 'dark';
  const bgColor = isDark ? 'bg-gray-800' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-gray-900';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';

  // Load active sessions on mount
  useEffect(() => {
    dispatch(getActiveExamSessions());
  }, [dispatch]);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        dispatch(getActiveExamSessions());
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, dispatch]);

  // Handle manual refresh
  const handleRefresh = () => {
    dispatch(getActiveExamSessions());
  };

  // Handle view session details
  const handleViewSession = async (sessionId) => {
    try {
      const sessionDetails = await dispatch(viewExamSession({ sessionId })).unwrap();
      setSelectedSession(sessionDetails);
    } catch (error) {
      showError(error.message || 'Failed to load session details');
    }
  };

  // Handle action (submit/terminate)
  const handleAction = (session, action) => {
    setSelectedSession(session);
    setActionType(action);
    setActionReason('');
    setShowActionModal(true);
  };

  // Submit action
  const handleSubmitAction = async () => {
    if (!selectedSession || !actionType) return;

    const reason = actionReason.trim() || `Admin ${actionType}`;

    try {
      if (actionType === 'submit') {
        await dispatch(forceSubmitExamSession({
          sessionId: selectedSession.session_id,
          reason
        })).unwrap();
        showSuccess('Exam session force submitted successfully');
      } else if (actionType === 'terminate') {
        await dispatch(terminateExamSession({
          sessionId: selectedSession.session_id,
          reason
        })).unwrap();
        showWarning('Exam session terminated');
      }

      setShowActionModal(false);
      setSelectedSession(null);
      setActionReason('');
      
      // Refresh sessions
      dispatch(getActiveExamSessions());
    } catch (error) {
      showError(error.message || `Failed to ${actionType} session`);
    }
  };

  // Get session status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-green-500';
      case 'paused':
        return 'text-yellow-500';
      case 'disconnected':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  // Get session status icon
  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return FiPlay;
      case 'paused':
        return FiPause;
      case 'disconnected':
        return FiXCircle;
      default:
        return FiActivity;
    }
  };

  // Format duration
  const formatDuration = (startTime) => {
    const now = new Date();
    const start = new Date(startTime);
    const diffMs = now - start;
    const diffMins = Math.floor(diffMs / 60000);
    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    
    return `${hours}h ${mins}m`;
  };

  return (
    <div className={`${bgColor} rounded-xl shadow-sm border ${borderColor}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FiMonitor className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className={`text-xl font-semibold ${textColor}`}>
                Active Exam Sessions
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Monitor and control ongoing exam sessions
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Auto-refresh controls */}
            <div className="flex items-center gap-2">
              <label className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded"
                />
                Auto-refresh
              </label>
              
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(Number(e.target.value))}
                disabled={!autoRefresh}
                className={`text-xs px-2 py-1 border rounded ${
                  isDark 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } disabled:opacity-50`}
              >
                <option value={15}>15s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={120}>2m</option>
              </select>
            </div>
            
            <button
              onClick={handleRefresh}
              disabled={adminState.loading}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <FiRefreshCw className={`w-4 h-4 ${adminState.loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{activeSessions.length}</div>
            <div className="text-sm text-gray-500">Total Sessions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {activeSessions.filter(s => s.status === 'active').length}
            </div>
            <div className="text-sm text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {activeSessions.filter(s => s.status === 'paused').length}
            </div>
            <div className="text-sm text-gray-500">Paused</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {activeSessions.filter(s => s.status === 'disconnected').length}
            </div>
            <div className="text-sm text-gray-500">Disconnected</div>
          </div>
        </div>
      </div>

      {/* Sessions List */}
      <div className="p-6">
        {adminState.loading && activeSessions.length === 0 ? (
          <div className="text-center py-8">
            <FiRefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">Loading sessions...</p>
          </div>
        ) : activeSessions.length === 0 ? (
          <div className="text-center py-8">
            <FiCheckCircle className="w-8 h-8 text-green-500 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">No active exam sessions</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeSessions.map((session) => {
              const StatusIcon = getStatusIcon(session.status);
              
              return (
                <div
                  key={session.session_id}
                  className={`border ${borderColor} rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <StatusIcon className={`w-5 h-5 ${getStatusColor(session.status)}`} />
                        <span className={`font-medium ${textColor}`}>
                          {session.exam_title || session.exam_id}
                        </span>
                        <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {session.session_id.slice(-8)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-300">
                        <div className="flex items-center gap-1">
                          <FiUsers className="w-3 h-3" />
                          Student: {session.student_id}
                        </div>
                        <div className="flex items-center gap-1">
                          <FiClock className="w-3 h-3" />
                          Duration: {formatDuration(session.started_at)}
                        </div>
                        <div className="flex items-center gap-1">
                          <FiActivity className="w-3 h-3" />
                          Status: {session.status}
                        </div>
                        <div className="flex items-center gap-1">
                          <FiSettings className="w-3 h-3" />
                          Progress: {session.progress || 0}%
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => handleViewSession(session.session_id)}
                        className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-1 text-sm"
                      >
                        <FiEye className="w-3 h-3" />
                        View
                      </button>
                      <button
                        onClick={() => handleAction(session, 'submit')}
                        className="px-3 py-1 bg-orange-600 text-white rounded hover:bg-orange-700 flex items-center gap-1 text-sm"
                      >
                        <FiCheckCircle className="w-3 h-3" />
                        Submit
                      </button>
                      <button
                        onClick={() => handleAction(session, 'terminate')}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 flex items-center gap-1 text-sm"
                      >
                        <FiStop className="w-3 h-3" />
                        Terminate
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Action Modal */}
      {showActionModal && selectedSession && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${bgColor} rounded-xl shadow-2xl max-w-md w-full border ${borderColor}`}>
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <FiAlertTriangle className="w-6 h-6 text-orange-500" />
                <h3 className={`text-lg font-semibold ${textColor}`}>
                  {actionType === 'submit' ? 'Force Submit' : 'Terminate'} Session
                </h3>
              </div>
              
              <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">Session:</span> {selectedSession.session_id}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">Student:</span> {selectedSession.student_id}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium">Exam:</span> {selectedSession.exam_title || selectedSession.exam_id}
                </p>
              </div>
              
              <div className="mb-4">
                <label className={`block text-sm font-medium ${textColor} mb-2`}>
                  Reason for {actionType === 'submit' ? 'Force Submission' : 'Termination'}
                </label>
                <textarea
                  value={actionReason}
                  onChange={(e) => setActionReason(e.target.value)}
                  placeholder={`Provide a reason for ${actionType === 'submit' ? 'force submitting' : 'terminating'} this session...`}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    isDark 
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                  rows={3}
                />
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={handleSubmitAction}
                  disabled={adminState.loading}
                  className={`flex-1 px-4 py-2 text-white rounded-lg disabled:opacity-50 ${
                    actionType === 'submit' 
                      ? 'bg-orange-600 hover:bg-orange-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {adminState.loading ? 'Processing...' : `${actionType === 'submit' ? 'Force Submit' : 'Terminate'}`}
                </button>
                <button
                  onClick={() => setShowActionModal(false)}
                  className={`px-4 py-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 ${borderColor} ${textColor}`}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSessionMonitoringDashboard;
